import threading
import subprocess

def run_discord_bot():
    subprocess.run(["python", "interfaces/discord/bit_bear_bot.py"])

def run_tweet_scraper():
    subprocess.run(["python", "interfaces/scrapes/tweet_scraper_service.py"])

if __name__ == "__main__":
    t1 = threading.Thread(target=run_discord_bot)
    t2 = threading.Thread(target=run_tweet_scraper)

    t1.start()
    t2.start()

    t1.join()
    t2.join()
