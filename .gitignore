# ─── Env ───
**/.idea/**

*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

labs/echo-scripts/data/crypto_cache/*
labs/echo-scripts/data/pionex_balance_adjustment.json

# Used by dotenv library to load environment variables.
# .env

# Ignore Byebug command history file.
.byebug_history

## Specific to RubyMotion:
.dat*
.repl_history
build/
*.bridgesupport
build-iPhoneOS/
build-iPhoneSimulator/

## Specific to RubyMotion (use of CocoaPods):
#
# We recommend against adding the Pods directory to your .gitignore. However
# you should judge for yourself, the pros and cons are mentioned at:
# https://guides.cocoapods.org/using/using-cocoapods.html#should-i-check-the-pods-directory-into-source-control
#
# vendor/Pods/

# ─── Python bytecode and cache ───
__pycache__/
*.py[cod]
*$py.class

# ─── Virtual environments ───
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# ─── Build system artifacts ───
*.egg-info/
.eggs/
dist/
build/
develop-eggs/
.installed.cfg
MANIFEST
.Python

# ─── Installers / logs ───
pip-log.txt
pip-delete-this-directory.txt

# ─── Dependency managers ───
.uv/
uv.lock
.pdm.toml
.pdm-python
.pdm-build/
__pypackages__/
poetry.lock
poetry.toml

# ─── Test and coverage ───
.coverage
.coverage.*
.cache
.nox/
.tox/
htmlcov/
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json
.ruff_cache/
.pyre/
.pytype/

# ─── Jupyter / IPython ───
.ipynb_checkpoints
profile_default/
ipython_config.py

# ─── Logs and local configs ───
*.log
.env
htpasswd

# ─── Documentation build artifacts ───
docs/_build/
site/

# ─── System files ───
.DS_Store
Thumbs.db
*.swp
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*
# Exclude data directory except for crypto cache
data
!labs/echo-scripts/data/
!labs/echo-scripts/data/crypto_cache/

# Keep crypto cache directory in the repo
# labs/echo-scripts/data/crypto_cache/
