require 'redis'
require 'notifier'
require_relative 'rest'

# Set the default notifier to terminal_notifier
Notifier.default_notifier = :terminal_notifier

# Represents an order for trading
class Order
  attr_reader :symbol, :target_price, :quantity, :type, :offset, :operation

  # Initialize an order with symbol, target price, quantity, type, offset, and operation.
  #
  # @param symbol [String] The trading symbol.
  # @param target_price [Float] The target price for the order.
  # @param quantity [Float] The quantity to trade.
  # @param type [String] The order type (e.g., 'LIMIT' or 'MARKET').
  # @param offset [Float] The price offset.
  # @param operation [Symbol] The operation (:sell or :buy).
  def initialize(symbol:, target_price:, quantity:, type:, offset:, operation:)
    @symbol = symbol
    @target_price = target_price
    @quantity = quantity
    @type = type
    @offset = offset # e.g., 0.00006
    @operation = operation # :sell or :buy
  end

  # Initialize a Redis connection.
  def redis
    @redis ||= Redis.new
  end

  # Initialize a Binance REST client.
  def rest
    @rest ||= Binance::Client::REST.new
  end

  # Place an order (either limit or market) based on the specified type.
  #
  # @param type [Symbol] The order type (:limit or :market).
  # @return [Hash] The order response.
  def order(type:)
    if type == :limit
      rest.create_order!(
        symbol: symbol, side: side, type: 'LIMIT', timeInForce: 'GTC',
        quantity: quantity, price: target_price.to_s
      )
    else
      response = rest.create_order!(
        symbol: symbol, side: side, type: 'MARKET',
        quantity: quantity
      )
    end
  end

  # Determine the trading side (BUY or SELL) based on the operation.
  #
  # @return [String] The trading side.
  def side
    return "SELL" if sell?

    "BUY"
  end

  # Check if the operation is to sell.
  #
  # @return [Boolean] True if the operation is to sell, false otherwise.
  def sell?
    operation == :sell
  end

  # Check if the operation is to buy.
  #
  # @return [Boolean] True if the operation is to buy, false otherwise.
  def buy?
    operation == :buy
  end

  # Main trading logic for the order.
  #
  # @param tries [Integer] The number of retry attempts.
  def run(tries: 0)
    sold = {}

    while true do
      r = rand 0.7..4.2
      print "#{r.round(2)}"

      sleep(r)
      price = rest.price(symbol: symbol)
      if r > 3.2
        puts "\n#{operation}#: #{symbol} at #{target_price} current => #{price} **"
        puts "\n time server: #{Time.at(rest.time['serverTime']/1000)}, Time Local: #{Time.now}"
      end

      if sold[symbol].nil? && operation_cond?(price["price"].to_f)
        puts "$"*1000
        puts "\ntarget price reached"
        puts "\n#{operation}#: #{symbol} at #{target_price} current => #{price} **"
        response = order(type: type)
        Notifier.notify(title: "Notify", message: "XCODE", sound: :default)
        puts response
        sold[symbol] = true
        puts "$"*1000

        return true
      end
    end
  rescue StandardError => e
    puts "çß" * 100
    puts e.message
    Notifier.notify(title: "Notify", message: "Error Error ErroR!", sound: :default)

    if tries > 100
      sleep rand 10..20
    else
      sleep rand 0.9..2.3
    end

    run(tries: tries + 1)
    puts "çß" * 100
  end
end
