=begin
Here's an explanation of the code you provided:

The code appears to be a script for trading on the Binance cryptocurrency exchange using the Binance API. It utilizes the `Order` class to create and execute trading orders based on predefined conditions. Each order is defined with specific parameters such as the trading symbol, target price, quantity, order type, offset, and operation (buy or sell).

The script creates instances of the `Order` class with different parameters to define trading strategies. Here's an overview of the main parts of the code:

1. Importing Dependencies:
   - The code begins by importing the necessary libraries, including `redis`, `notifier`, and a custom `order.rb` file.

2. Setting Up Notifier:
   - The default notifier is set to `:terminal_notifier` for displaying notifications.

3. Creating `Order` Instances:
   - The code creates instances of the `Order` class with specific parameters to define trading strategies. Each `Order` object represents a specific trading action for a particular trading pair (e.g., BTCUPUSDT or LTCUPUSDT).

4. Running Trading Strategies:
   - The `run` method is called on each `Order` object to execute the trading strategy. The `run` method contains the main trading logic, including checking market conditions, placing orders, and handling errors.

5. Conditional Execution:
   - Some parts of the code include conditional execution of trading strategies, such as executing one order after another.

6. Commented Output:
   - The code includes commented output lines that provide information about the executed orders, current prices, and notifications. These comments help track the progress and results of the trading strategies.

Please note that the script seems to be tailored for cryptocurrency trading on Binance and may require continuous monitoring and adjustments based on market conditions. Additionally, it uses the Binance API, so you should ensure that you have the necessary API keys and permissions set up for trading.
=end



# Trading URLs for reference:
# - BTCUSDT: https://www.binance.com/en/trade/BTC_USDT?layout=pro&type=spot
# - LTCUPUSDT: https://www.binance.com/en/trade/LTCUP_USDT?layout=pro&type=spot

require_relative 'order'

# Define trading strategies for BTCUPUSDT
one = Order.new(symbol: "BTCUPUSDT", target_price: 55.83, quantity: 1.5, type: :limit, offset: 0.009, operation: :buy)
two = Order.new(symbol: "BTCUPUSDT", target_price: 57.57, quantity: 1.5, type: :limit, offset: 0.009, operation: :sell)
two.run if one.run

# Define trading strategies for BTCUPUSDT (additional)
Order.new(symbol: "BTCUPUSDT", target_price: 59.87, quantity: 1.5, type: :limit, offset: 0.009, operation: :buy).run
Order.new(symbol: "BTCUPUSDT", target_price: 64.17, quantity: 1.5, type: :limit, offset: 0.009, operation: :sell).run
Order.new(symbol: "BTCUPUSDT", target_price: 61.87, quantity: 1.5, type: :limit, offset: 0.009, operation: :buy).run

# Execute additional strategies if the first two are successful
if one.run
  if two.run
    tree.run
  end
end

# Trading strategy for LTCUPUSDT
# sell#: LTCUPUSDT at 9.907 current => {"symbol"=>"LTCUPUSDT", "price"=>"10.32900000"} **
# {"symbol"=>"LTCUPUSDT", "orderId"=>122866240, "orderListId"=>-1, "clientOrderId"=>"EccOwz....9m886X2",
# "transactTime"=>1621474044500, "price"=>"9.90700000", "origQty"=>"3.00000000", "executedQty"=>"0.00000000",
# "cummulativeQuoteQty"=>"0.00000000", "status"=>"NEW", "timeInForce"=>"GTC", "type"=>"LIMIT", "side"=>"SELL", "fills"=>[]}
Order.new(symbol: "LTCUPUSDT", target_price: 11.65, quantity: 5, type: :limit, offset: 0.09, operation: :buy).run
Order.new(symbol: "LTCUPUSDT", target_price: 14.907, quantity: 5, type: :limit, offset: 0.09, operation: :sell).run

# Additional trading strategies for LTCUPUSDT
two = Order.new(symbol: "LTCUPUSDT", target_price: 5.23, quantity: 5.23, type: :limit, offset: 0.009, operation: :sell)
tree = Order.new(symbol: "LTCUPUSDT", target_price: 5.0, quantity: 5.0, type: :limit, offset: 0.009, operation: :buy)
tree.run if two.run

# Another trading strategy for LTCUPUSDT
tree = Order.new(symbol: "LTCUPUSDT", target_price: 14.207, quantity: 5.13, type: :limit, offset: 0.009, operation: :sell)
tree.run

#buy#: LTCUPUSDT at 33.57 current => {"symbol"=>"LTCUPUSDT", "price"=>"33.23900000"} **
#* Removing previously sent notification, which was sent on: 2021-05-18 19:02:28 +0000
#{"symbol"=>"LTCUPUSDT", "orderId"=>121782384, "orderListId"=>-1, "clientOrderId"=>"a60DhL....ZixKaxC",
# "transactTime"=>1621365087008, "price"=>"0.00000000", "origQty"=>"3.09000000", "executedQty"=>"3.09000000",
# "cummulativeQuoteQty"=>"102.93099000", "status"=>"FILLED", "timeInForce"=>"GTC", "type"=>"MARKET", "side"=>"BUY",
# "fills"=>[{"price"=>"33.31100000", "qty"=>"3.09000000", "commission"=>"0.00015181", "commissionAsset"=>"BNB",
# "tradeId"=>3042905}]}


