=begin
This code is designed for automated trading on the Binance cryptocurrency exchange. It utilizes the Binance API to monitor cryptocurrency prices and execute buy and sell orders based on predefined conditions.

1. Importing Dependencies:
   - The code starts by importing required libraries, including 'redis' for data storage, 'notifier' for notifications, and a custom 'rest.rb' file for handling API requests.

2. Setting Up Notifier:
   - It configures the default notification mechanism to use ':terminal_notifier' for displaying notifications.

3. Initialization and Configuration:
   - The code defines several functions:
      - `redis`: Initializes a Redis connection for data storage.
      - `rest`: Initializes a Binance API client using the 'rest.rb' module.
      - `target_price`: Sets a predefined target price for trading.
      - `target_price_incr`: Calculates an increased target price (7% higher).
      - `quantity`: Sets the quantity for trading orders.

4. Main Trading Logic:
   - The `run` function is the core of the trading strategy. It accepts an optional `tries` parameter to handle retries in case of errors.
   - The code enters an infinite loop, continuously monitoring and reacting to market conditions.
   - Within the loop:
      - It generates a random sleep duration (`r`) between 0.7 and 4.2 seconds, simulating trading activity.
      - Sleeps for the calculated duration.
      - Retrieves the current cryptocurrency price for the trading pair "VETBUSD."
      - Prints the price if the random sleep duration is greater than 3.5 seconds.
      - Checks conditions for buying and selling:
         - If the `bot` for the trading pair is not initialized, and the current price is less than or equal to the `target_price`, it triggers a market buy order with the defined `quantity`.
         - If the `bot` is initialized, and the current price is greater than or equal to the `target_price_incr`, it triggers a market sell order with the same `quantity`.
      - Notifies with a message "XCODE" when an order is executed.
      - The code updates the `bot` and `sold` flags accordingly.

5. Error Handling and Retrying:
   - The code includes a rescue block to handle exceptions (StandardError).
   - If an error occurs, it prints an error message, notifies with "Error Error ErroR!" message, and sleeps for a random duration.
   - If the number of retries (`tries`) exceeds 100, it waits for a longer period (between 10 to 20 seconds) before retrying.
   - Otherwise, it waits for a shorter period (between 0.9 to 2.3 seconds) and retries the `run` function with an incremented `tries` count.

6. Execution:
   - Finally, the `run` function is called to start the automated trading process.

=end

require 'redis'
require 'notifier'
require_relative 'rest'

Notifier.default_notifier = :terminal_notifier

# Initialize a Redis connection for data storage
def redis
  @redis ||= Redis.new
end

# Initialize a Binance API client using the 'rest.rb' module
def rest
  @rest ||= Binance::Client::REST.new
end

# Define the target price for trading
def target_price
  0.21975
end

# Calculate the increased target price (7% higher)
def target_price_incr
  target_price * 1.07
end

# Define the quantity for trading orders
def quantity
  400
end

# Main trading logic
def run(tries: 0)
  # Initialize trading status flags
  bot = {"VETBUSD"=>true}
  sold = {}
  symbol = "VETBUSD"

  while true do
    r = rand 0.7..4.2  
    print "#{r.round(2)}"
    
    sleep(r)
    price = rest.price(symbol: symbol)
    puts price if r > 3.5

    # Buy condition
    if bot[symbol].nil? && price["price"].to_f <= target_price
      puts "$"*100
      puts price
      puts "target price reached"

      response = rest.create_order!(
        symbol: symbol, side: 'BUY', type: 'MARKET', 
        quantity: quantity
      )
      # response:
			#
			# {"symbol"=>"VETBUSD", "orderId"=>130594700, "orderListId"=>-1, "clientOrderId"=>"stakb3...EFObyxp70",
			# "transactTime"=>1620656448378, "price"=>"0.00000000", "origQty"=>"400.00000000", "executedQty"=>"400.00000000",
			# "cummulativeQuoteQty"=>"87.98000000", "status"=>"FILLED", "timeInForce"=>"GTC", "type"=>"MARKET", "side"=>"BUY", 
			# "fills"=>[{"price"=>"0.21995000", "qty"=>"400.00000000", "commission"=>"0.00009987", "commissionAsset"=>"BNB",
			# "tradeId"=>6796567}]}

      # Notify and print the response
      Notifier.notify(title: "Notify", message: "XCODE", sound: :default)
      puts response
      bot[symbol] = true
    end

    # Sell condition
    if bot[symbol] && price["price"].to_f >= target_price_incr
      puts "$"*100
      puts price
      puts "target price reached"

      response = rest.create_order!(
        symbol: symbol, side: 'SELL', type: 'MARKET', quantity: quantity
      )
      # sometimes error you get
      # {"code"=>-1106, "msg"=>"Parameter 'timeInForce' sent when not required."}
			# time_in_force: 'GTC'

      # Notify and print the response
      Notifier.notify(title: "Notify", message: "XCODE", sound: :default)
      puts response
      sold[symbol] = true
    end
  end
rescue StandardError => e
  # Error handling and retries
  puts "çß" * 100
  puts e.message
  Notifier.notify(title: "Notify", message: "Error Error ErroR!", sound: :default)
  
  if tries > 100
    sleep rand 10..20
  else
    sleep rand 0.9..2.3
  end

  # Retry the 'run' function with an incremented 'tries' count
  run(tries: tries + 1)
  puts "çß" * 100
end

# Start the automated trading process
run

