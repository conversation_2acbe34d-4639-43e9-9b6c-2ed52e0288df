=begin
  This script is a WebSocket client designed for connecting to the Binance WebSocket API.
  It uses the EventMachine library for handling asynchronous events and Faye::WebSocket
  for WebSocket communication.

  The script connects to the Binance WebSocket API to receive real-time cryptocurrency market data.
  It establishes a connection to multiple WebSocket streams, including Kline (candlestick) data,
  trade data, and ticker data, for various cryptocurrency trading pairs.

  Key components of the script:
  - It establishes a connection to the Binance WebSocket API using EventMachine.
  - Defines event handler methods for WebSocket events such as open, message, error, and close.
  - Configures the list of cryptocurrency trading pairs and intervals to subscribe to.
  - Receives real-time data updates from the WebSocket streams and processes them.
  - Stores Kline (candlestick) data in a MySQL database and sends pings periodically.
  - Handles errors and retries the WebSocket connection in case of failures.

  The code leverages the ActiveRecord library to connect to a MySQL database and stores
  incoming Kline (candlestick) data in the 'finanzas' database. It uses the Redis database
  to store some real-time data.

  The 'process' method manages the WebSocket client's lifecycle, including error handling
  and retrying the connection if it encounters issues.

  The WebSocket streams subscribed to include Kline data for multiple trading pairs and
  intervals, as well as ticker data for various cryptocurrencies.

  References:
  - Binance WebSocket API documentation: https://github.com/binance/binance-spot-api-docs/blob/master/web-socket-streams.md
  - Binance REST API documentation for symbol price ticker and Kline data:
    https://github.com/binance/binance-spot-api-docs/blob/master/rest-api.md#symbol-price-ticker
    https://github.com/binance/binance-spot-api-docs/blob/master/rest-api.md#klinecandlestick-data

  Note: This script is designed for real-time market data analysis and may require additional
  configuration and adjustments to meet specific trading or data analysis needs.
=end


require 'eventmachine'
require 'faye/websocket'
require 'mysql2'
require 'active_record'
require 'oj'
require 'redis'


def db_connect
  ActiveRecord::Base.establish_connection(
    adapter: 'mysql2', 
    database: 'finanzas',
    host: 'mysqlhost',
    username: 'username',
    password: 'password'
  )
end


class Stream < ActiveRecord::Base ; end
class StreamOnError < StandardError ; end

module Binance
  module Client
    # Public: Client with methods mirroring the Binance WebSocket API
    class WebSocket
      # Public: String base url for WebSocket client to use
      BASE_URL = 'wss://stream.binance.com:9443'.freeze

      # Public: Create a single WebSocket stream
      #
      # :stream - The Hash used to define the stream
      #   :symbol   - The String symbol to listen to
      #   :type     - The String type of stream to listen to
      #   :level    - The String level to use for the depth stream (optional)
      #   :interval - The String interval to use for the kline stream (optional)
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def single(stream:, methods:)
        create_stream("#{BASE_URL}/ws/#{stream_url(stream)}",
                      methods: methods)
      end

      # Public: Create multiple WebSocket streams
      #
      # :streams - The Array of Hashes used to define the stream. Each Hash can
      #            have the following keys:
      #   :symbol   - The String symbol the stream will listen to
      #   :type     - The String type of stream to listen to
      #   :level    - The String level to use for the depth stream (optional)
      #   :interval - The String interval to use for the kline stream (optional)
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def multi(streams:, methods:)
        names = streams.map { |stream| stream_url(stream) }
        create_stream("#{BASE_URL}/stream?streams=#{names.join('/')}", methods: methods)
      end

      # Public: Create an Aggregate Trade stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def agg_trade(symbol:, methods:)
        single stream: { symbol: symbol, type: 'aggTrade' }, methods: methods
      end

      # Public: Create a Trade stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def trade(symbol:, methods:)
        single stream: { symbol: symbol, type: 'trade' }, methods: methods
      end

      # Public: Create an Kline stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :interval - The String interval the stream will update with. The
      #             intervals that may be used can be found in the Binance API
      #             docs.
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def kline(symbol:, interval:, methods:)
        single stream: { symbol: symbol, type: 'kline', interval: interval },
               methods: methods
      end

      # Public: Create a Ticker stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def ticker(symbol:, methods:)
        single stream: { symbol: symbol, type: 'ticker' }, methods: methods
      end

      # Public: Create a Ticker stream for all symbols
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def all_market_ticker(methods:)
        single stream: { symbol: '!ticker', type: 'arr' }, methods: methods
      end

      # Public: Create an Partial Book Depth stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :level - The String interval the stream will update with. The intervals
      #          that may be used can be found in the Binance API docs.
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def partial_book_depth(symbol:, level:, methods:)
        single stream: { symbol: symbol, type: 'depth', level: level },
               methods: methods
      end

      # Public: Create a Diff Depth stream
      #
      # :symbol - The String symbol the stream will listen to
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def diff_depth(symbol:, methods:)
        single stream: { symbol: symbol, type: 'depth' }, methods: methods
      end

      # Public: Create a User Data stream
      #
      # listen_key - The String key the stream will listen to, attained by
      #              interacting with the REST API userDataStream endpoint
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def user_data(listen_key:, methods:)
        create_stream "#{BASE_URL}/ws/#{listen_key}", methods: methods
      end

      private

      # Internal: Create a valid URL for a WebSocket to use
      #
      # :symbol - The String symbol to listen to
      # :type   - The String type the stream will listen to
      # :level    - The String level to use for the depth stream (optional)
      # :interval - The String interval to use for the kline stream (optional)
      def stream_url(symbol:, type:, level: '', interval: '')
        "#{symbol.downcase}@#{type}".tap do |url|
          url << level
          url << "_#{interval}" unless interval.empty?
        end
      end

      # Internal: Initialize and return a Faye::WebSocket::Client
      #
      # url - The String url that the WebSocket should try to connect to
      #
      # :methods - The Hash which contains the event handler methods to pass to
      #            the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def create_stream(url, methods:)
        Faye::WebSocket::Client.new(url)
                               .tap { |ws| attach_methods(ws, methods) }
      end

      # Internal: Iterate through methods passed and add them to the WebSocket
      #
      # websocket - The Faye::WebSocket::Client to apply methods to
      #
      # methods - The Hash which contains the event handler methods to pass to
      #   the WebSocket client
      #   :open    - The Proc called when a stream is opened (optional)
      #   :message - The Proc called when a stream receives a message
      #   :error   - The Proc called when a stream receives an error (optional)
      #   :close   - The Proc called when a stream is closed (optional)
      def attach_methods(websocket, methods)
        methods.each_pair do |key, method|
          websocket.on(key) { |event| method.call(event) }
        end
      end
    end
  end
end

def run_websocket_client(client)
  EM.run {

    # Create event handlers
    open    = proc { puts 'connected' }

    message = proc do |e|
      json_data = Oj.load(e.data)
      datum = Stream.create!(stream: json_data["stream"], data: json_data["data"], created_at: Time.now)

      redis.set(datum.stream, datum.data['k']['c']) if datum.stream.include?('kline')

      now = Time.now
      if (now.min % 15).zero? && now.sec == 15
        e.current_target.ping("ok")
        puts "SENDING PING - at #{Time.now}"
      end

      puts Time.now
    end

    error   = proc do |e|
      puts e.message
      raise StreamOnError
    end

    close   = proc { puts 'closed' }

    # Bundle our event handlers into Hash
    methods = { open: open, message: message, error: error, close: close }
    streams = []

    [
      "ADABTC", "ADAUSDT", "BNBBTC","BNBUSDT", "DOGEBTC", "DOGEUSDT", "ETHBTC", "ETHDOWNUSDT",
      "ETHUSDT", "ETCBUSD", "ETCUSDT", "ETCBTC", "DOTUSDT", "DOTDOWNUSDT", "DOTUPUSDT", "LTCUPUSDT", "LTCUSDT",
      "XLMBTC", "XLMUSDT", "XRPBTC", "XRPUSDT", "VETBUSD", "VETBTC", "BTCUPUSDT", "BTCUSDT"
    ].each do |symbol|
        streams.push({ type: 'kline', symbol: symbol, interval: '1m' })
        streams.push({ type: 'kline', symbol: symbol, interval: '5m' })
        streams.push({ type: 'kline', symbol: symbol, interval: '15m' })
        streams.push({ type: 'ticker', symbol: symbol })
    end

    client.multi(streams: streams, methods: methods)
  }
end

def redis
  @redis ||= Redis.new
end

def process(client, try_number: 0)
  begin
    db_connect
    run_websocket_client(client)
  rescue StandardError => error_
    puts "*" * 100
    puts error_
    puts "*" * 100
    try_n = try_number + 1
    puts "Retrying: #{try_n}"
    sleep rand 2..10 if try_n > 10 && try_n <= 20
    sleep rand 10..40 if try_n > 20
    process(Binance::Client::WebSocket.new, try_number: try_n)
  end
end

process(Binance::Client::WebSocket.new)

#<Faye::WebSocket::API::MessageEvent:0x00007ff128274338>

# https://github.com/binance/binance-spot-api-docs/blob/master/web-socket-streams.md
# https://github.com/binance/binance-spot-api-docs/blob/master/rest-api.md#symbol-price-ticker
# https://github.com/binance/binance-spot-api-docs/blob/master/rest-api.md#klinecandlestick-data
# https://academy.binance.com/es/articles/a-guide-to-crypto-collectibles-and-non-fungible-tokens-nfts

# https://github.com/craysiii/binance/blob/dcf17359c894cc2891d22f40284a1c7e18163011/lib/binance/client/websocket.rb
# https://developer.amazon.com/en-US/docs/alexa/custom-skills/batch-test-your-nlu-model.html
# https://developer.amazon.com/en-US/docs/alexa/smapi/interaction-model-schema.html


# buy#: BTCUPUSDT at 58.57 current => {"symbol"=>"BTCUPUSDT", "price"=>"58.50000000"} **
# * Removing previously sent notification, which was sent on: 2021-05-19 23:37:52 +0000
# {"symbol"=>"BTCUPUSDT", "orderId"=>162015699, "orderListId"=>-1, "clientOrderId"=>"8hMhxB....mTfKG",
# "transactTime"=>1621468327495, "price"=>"58.57000000", "origQty"=>"1.50000000",
# "executedQty"=>"0.00000000", "cummulativeQuoteQty"=>"0.00000000", "status"=>"NEW", "timeInForce"=>"GTC",
# "type"=>"LIMIT", "side"=>"BUY", "fills"=>[]}
