require 'faraday_middleware'

module Binance
  module Client
    class REST
      # Creates a public client for making unauthenticated requests.
      #
      # @param adapter [Faraday::Adapter] The Faraday adapter to use.
      # @return [Faraday::Connection] The Faraday connection.
      def public_client(adapter)
        Faraday.new(url: "#{BASE_URL}/api") do |conn|
          conn.request :json
          conn.response :json, content_type: /\bjson$/
          conn.adapter adapter
        end
      end

      # Creates a verified client for making authenticated requests with an API key.
      #
      # @param api_key [String] The API key.
      # @param adapter [Faraday::Adapter] The Faraday adapter to use.
      # @return [Faraday::Connection] The Faraday connection.
      def verified_client(api_key, adapter)
        Faraday.new(url: "#{BASE_URL}/api") do |conn|
          conn.response :json, content_type: /\bjson$/
          conn.headers['X-MBX-APIKEY'] = api_key
          conn.adapter adapter
        end
      end

      # Creates a signed client for making authenticated requests with an API key and secret key.
      #
      # @param api_key [String] The API key.
      # @param secret_key [String] The secret key.
      # @param adapter [Faraday::Adapter] The Faraday adapter to use.
      # @return [Faraday::Connection] The Faraday connection.
      def signed_client(api_key, secret_key, adapter)
        Faraday.new(url: "#{BASE_URL}/api") do |conn|
          conn.request :json
          conn.response :json, content_type: /\bjson$/
          conn.headers['X-MBX-APIKEY'] = api_key
          conn.use TimestampRequestMiddleware
          conn.use SignRequestMiddleware, secret_key
          conn.adapter adapter
        end
      end
    end
  end
end
