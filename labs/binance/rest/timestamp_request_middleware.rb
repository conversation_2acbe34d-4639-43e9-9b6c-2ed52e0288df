require 'date'

module Binance
  module Client
    class REST
      # Middleware to generate a timestamp in milliseconds and append it to the query string.
      TimestampRequestMiddleware = Struct.new(:app) do
        # Middleware call method.
        #
        # @param env [Faraday::Env] The Faraday environment.
        # @return [Faraday::Env] The updated Faraday environment.
        def call(env)
          # Generate a timestamp in milliseconds and append it to the query string.
          env.url.query = REST.add_query_param(
            env.url.query, 'timestamp', DateTime.now.strftime('%Q')
          )

          app.call env
        end
      end
    end
  end
end
