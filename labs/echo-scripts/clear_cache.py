#!/usr/bin/env python3
"""
Clear all cached data to force fresh API calls.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.storage.crypto_storage import CryptoStorage

def clear_all_cache():
    """Clear all cached data."""
    print("="*60)
    print("CLEARING ALL CACHED DATA")
    print("="*60)
    
    storage = CryptoStorage()
    
    # List of cache keys to clear
    cache_keys = [
        # Pionex caches
        'pionex_balances',
        'pionex_active_operations',
        'pionex_detailed_balances',
        'pionex_movements',
        'pionex_api_key_error',
        
        # Binance caches
        'binance_spot_snapshot',
        'binance_futures_snapshot',
        'binance_simple_earn_account',
        'binance_simple_earn_flexible',
        'binance_simple_earn_locked',
        'binance_deposits',
        'binance_withdrawals',
        
        # Portfolio caches
        'portfolio_total',
        'portfolio_active_operations',
        'portfolio_bot_history',
        'portfolio_bot_performance',
        'portfolio_historical',
        
        # Price caches
        'binance_price'
    ]
    
    cleared_count = 0
    
    for key in cache_keys:
        try:
            # Try different parameter combinations
            param_combinations = [
                {},
                {'symbol': 'BTCUSDT'},
                {'symbol': 'ETHUSDT'},
                {'symbol': 'BNBUSDT'},
                {'asset': None},
                {'asset': 'BNB'},
                {'asset': 'BTC'},
                {'asset': 'ETH'}
            ]
            
            for params in param_combinations:
                if storage.data_exists(key, params):
                    storage.delete_data(key, params)
                    cleared_count += 1
                    print(f"✅ Cleared {key} with params {params}")
                    
        except Exception as e:
            print(f"❌ Error clearing {key}: {e}")
    
    print(f"\n✅ Cleared {cleared_count} cache entries")
    print("🔄 Fresh data will be fetched on next API call")

if __name__ == "__main__":
    clear_all_cache()
