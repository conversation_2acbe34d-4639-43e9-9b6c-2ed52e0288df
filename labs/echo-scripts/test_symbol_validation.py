#!/usr/bin/env python3
"""
Test script for symbol validation and improved price fetching.
Tests the new blacklisting and error handling functionality.
"""

import sys
import os
import time

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.symbol_validator import symbol_validator
from services.binance_service import BinanceService
from services.portfolio_service import PortfolioService

def test_symbol_validator():
    """Test the symbol validator functionality."""
    print("="*60)
    print("TESTING SYMBOL VALIDATOR")
    print("="*60)
    
    # Test valid symbols
    valid_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    print("\n--- Testing Valid Symbols ---")
    for symbol in valid_symbols:
        is_valid = symbol_validator.is_valid_symbol(symbol)
        print(f"{symbol}: {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    # Test invalid symbols (Liquid Swap tokens)
    invalid_symbols = ['LDBTC', 'LDETH', 'LDBNB', 'LDUSDT']
    print("\n--- Testing Liquid Swap Tokens (Should be Invalid) ---")
    for symbol in invalid_symbols:
        is_valid = symbol_validator.is_valid_symbol(symbol)
        is_liquid_swap = symbol_validator.is_liquid_swap_token(symbol)
        print(f"{symbol}: {'❌ Invalid' if not is_valid else '✅ Valid'} (Liquid Swap: {is_liquid_swap})")
    
    # Test symbol normalization
    print("\n--- Testing Symbol Normalization ---")
    test_corrections = ['ETHWUSDT', 'ETHWUSDC', 'BTCUSDT']
    for symbol in test_corrections:
        normalized = symbol_validator.normalize_symbol(symbol)
        print(f"{symbol} -> {normalized}")
    
    # Test alternative symbols
    print("\n--- Testing Alternative Symbols ---")
    test_symbol = 'BTCUSDT'
    alternatives = symbol_validator.get_alternative_symbols(test_symbol, max_alternatives=3)
    print(f"Alternatives for {test_symbol}: {alternatives}")
    
    # Test symbol info
    print("\n--- Testing Symbol Information ---")
    test_symbols = ['BTCUSDT', 'LDBTC', 'ETHWUSDT']
    for symbol in test_symbols:
        info = symbol_validator.get_symbol_info(symbol)
        print(f"{symbol}:")
        print(f"  Valid: {info['is_valid']}")
        print(f"  Liquid Swap: {info['is_liquid_swap']}")
        print(f"  Blacklisted: {info['is_blacklisted']}")
        print(f"  Alternatives: {info['alternatives']}")
    
    return True

def test_improved_price_fetching():
    """Test the improved price fetching with blacklisting."""
    print("\n" + "="*60)
    print("TESTING IMPROVED PRICE FETCHING")
    print("="*60)
    
    binance_service = BinanceService()
    
    # Test valid symbols
    valid_symbols = ['BTCUSDT', 'ETHUSDT']
    print("\n--- Testing Valid Symbol Price Fetching ---")
    for symbol in valid_symbols:
        print(f"\nFetching price for {symbol}...")
        start_time = time.time()
        price = binance_service.get_current_price(symbol)
        end_time = time.time()
        
        if price is not None:
            print(f"✅ {symbol}: ${price:,.2f} (fetched in {end_time - start_time:.2f}s)")
        else:
            print(f"❌ {symbol}: Price not available")
    
    # Test invalid symbols (should be blocked)
    invalid_symbols = ['LDBTC', 'LDETH', 'INVALIDTOKEN']
    print("\n--- Testing Invalid Symbol Handling ---")
    for symbol in invalid_symbols:
        print(f"\nTesting invalid symbol {symbol}...")
        start_time = time.time()
        price = binance_service.get_current_price(symbol)
        end_time = time.time()
        
        if price is None:
            print(f"✅ {symbol}: Correctly blocked (processed in {end_time - start_time:.2f}s)")
        else:
            print(f"❌ {symbol}: Unexpectedly got price ${price}")
    
    # Test symbol that will fail multiple times
    print("\n--- Testing Blacklist Functionality ---")
    failing_symbol = 'NONEXISTENTUSDT'
    
    for attempt in range(3):
        print(f"\nAttempt {attempt + 1} for {failing_symbol}...")
        start_time = time.time()
        price = binance_service.get_current_price(failing_symbol)
        end_time = time.time()
        
        print(f"Result: {price} (processed in {end_time - start_time:.2f}s)")
        
        # Check if symbol is now blacklisted
        is_valid = symbol_validator.is_valid_symbol(failing_symbol)
        print(f"Symbol still valid for lookup: {is_valid}")
    
    return True

def test_safe_price_fetching():
    """Test the safe price fetching in portfolio service."""
    print("\n" + "="*60)
    print("TESTING SAFE PRICE FETCHING")
    print("="*60)
    
    portfolio_service = PortfolioService()
    
    # Test various assets
    test_assets = ['BTC', 'ETH', 'USDT', 'LDBTC', 'INVALIDASSET']
    
    print("\n--- Testing Safe Price Fetching for Various Assets ---")
    for asset in test_assets:
        print(f"\nFetching safe price for {asset}...")
        start_time = time.time()
        price = portfolio_service._get_safe_price(asset)
        end_time = time.time()
        
        print(f"{asset}: ${price:,.2f} (fetched in {end_time - start_time:.2f}s)")
        
        if asset == 'USDT':
            expected = 1.0
            if abs(price - expected) < 0.01:
                print(f"✅ Stablecoin correctly priced at ${expected}")
            else:
                print(f"❌ Stablecoin incorrectly priced")
        elif asset.startswith('LD'):
            if price == 0.0:
                print(f"✅ Liquid Swap token correctly blocked")
            else:
                print(f"❌ Liquid Swap token should be blocked")
    
    return True

def test_blacklist_stats():
    """Test blacklist statistics and management."""
    print("\n" + "="*60)
    print("TESTING BLACKLIST STATISTICS")
    print("="*60)
    
    # Get current stats
    stats = symbol_validator.get_blacklist_stats()
    
    print("\n--- Current Blacklist Statistics ---")
    print(f"Permanent blacklist: {stats['permanent_blacklist']} symbols")
    print(f"Dynamic blacklist: {stats['dynamic_blacklist']} symbols")
    print(f"Failed lookups: {stats['failed_lookups']} symbols")
    print(f"Logged errors: {stats['logged_errors']} unique errors")
    
    # Test adding to permanent blacklist
    print("\n--- Testing Permanent Blacklist Management ---")
    test_symbols = ['TESTTOKEN1', 'TESTTOKEN2']
    symbol_validator.add_permanent_blacklist(test_symbols)
    
    for symbol in test_symbols:
        is_valid = symbol_validator.is_valid_symbol(symbol)
        print(f"{symbol}: {'❌ Blocked' if not is_valid else '✅ Allowed'}")
    
    # Get updated stats
    updated_stats = symbol_validator.get_blacklist_stats()
    print(f"\nPermanent blacklist after addition: {updated_stats['permanent_blacklist']} symbols")
    
    return True

def test_error_logging():
    """Test that error logging only happens once per symbol."""
    print("\n" + "="*60)
    print("TESTING ERROR LOGGING (SHOULD SEE MINIMAL REPEATED MESSAGES)")
    print("="*60)
    
    binance_service = BinanceService()
    
    # Test the same invalid symbol multiple times
    invalid_symbol = 'REPEATEDTESTUSDT'
    
    print(f"\n--- Testing Repeated Calls to {invalid_symbol} ---")
    print("(Should only see error message once)")
    
    for i in range(5):
        print(f"\nCall {i + 1}:")
        price = binance_service.get_current_price(invalid_symbol)
        print(f"  Result: {price}")
    
    return True

def main():
    """Main test function."""
    print("🔍 TESTING SYMBOL VALIDATION AND IMPROVED PRICE FETCHING")
    print("=" * 80)
    
    try:
        # Test 1: Symbol validator
        print("\n1️⃣ Testing Symbol Validator...")
        validator_success = test_symbol_validator()
        print(f"✅ Symbol validator test: {'PASSED' if validator_success else 'FAILED'}")
        
        # Test 2: Improved price fetching
        print("\n2️⃣ Testing Improved Price Fetching...")
        price_success = test_improved_price_fetching()
        print(f"✅ Price fetching test: {'PASSED' if price_success else 'FAILED'}")
        
        # Test 3: Safe price fetching
        print("\n3️⃣ Testing Safe Price Fetching...")
        safe_price_success = test_safe_price_fetching()
        print(f"✅ Safe price fetching test: {'PASSED' if safe_price_success else 'FAILED'}")
        
        # Test 4: Blacklist statistics
        print("\n4️⃣ Testing Blacklist Statistics...")
        stats_success = test_blacklist_stats()
        print(f"✅ Blacklist statistics test: {'PASSED' if stats_success else 'FAILED'}")
        
        # Test 5: Error logging
        print("\n5️⃣ Testing Error Logging...")
        logging_success = test_error_logging()
        print(f"✅ Error logging test: {'PASSED' if logging_success else 'FAILED'}")
        
        # Summary
        all_tests = [validator_success, price_success, safe_price_success, stats_success, logging_success]
        passed_tests = sum(all_tests)
        
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"Tests passed: {passed_tests}/{len(all_tests)}")
        
        if passed_tests == len(all_tests):
            print("🎉 ALL TESTS PASSED! Symbol validation and improved price fetching working correctly.")
        else:
            print("⚠️ Some tests failed. Please check the implementation.")
        
        print("\n📋 IMPROVEMENTS IMPLEMENTED:")
        print("✅ Symbol validation with blacklisting")
        print("✅ Liquid Swap token detection and blocking")
        print("✅ Failed lookup tracking and retry prevention")
        print("✅ One-time error logging per symbol per session")
        print("✅ Timeout handling for API calls")
        print("✅ Safe price fetching with fallback handling")
        print("✅ Alternative symbol suggestions with limits")
        print("✅ Permanent and dynamic blacklist management")
        
        # Show final blacklist stats
        final_stats = symbol_validator.get_blacklist_stats()
        print(f"\n📊 FINAL STATISTICS:")
        print(f"Permanent blacklist: {final_stats['permanent_blacklist']} symbols")
        print(f"Dynamic blacklist: {final_stats['dynamic_blacklist']} symbols")
        print(f"Failed lookups: {final_stats['failed_lookups']} symbols")
        print(f"Unique errors logged: {final_stats['logged_errors']}")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
