help:
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m  %-30s\033[0m %s\n", $$1, $$2}'

prepare: ## How to prepare environment using UV
	uv venv echo-scripts
	@echo "✅ Virtual environment 'echo-scripts' created!"

install: ## Install dependencies using UV
	uv pip install -r requirements.txt
	@echo "✅ Dependencies installed using UV!"

refresh_orders: ## Update orders
	uv run python app/fetch_orders.py
	@echo "✅ Orders fetched successfully!"

run_dashboard: ## Run the Streamlit app
	@export PYTHONPATH=$(pwd) && uv run -m streamlit run app/main.py

tweet_scraper: ## Run the Tweet Scraper service
	@export PYTHONPATH=$(pwd) && uv run python app/services/tweet_scraper_service.py
	@echo "✅ Tweet Scraper service started!"

tweet_scraper_debug: ## Run the Tweet Scraper service in debug mode with visible browser
	@export PYTHONPATH=$(pwd) && export TWEET_SCRAPER_DEBUG=true && uv run python app/services/tweet_scraper_service.py
	@echo "✅ Tweet Scraper service started in debug mode!"

bit_bear: ## Run the BitBear Discord bot service
	@export PYTHONPATH=$(pwd) && uv run python app/services/bit_bear_service.py
	@echo "✅ BitBear Discord bot service started!"
