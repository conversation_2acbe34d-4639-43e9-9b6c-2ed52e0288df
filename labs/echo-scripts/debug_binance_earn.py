#!/usr/bin/env python3
"""
Debug Binance Earn API response structure.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.binance_service import BinanceService

def debug_binance_earn():
    """Debug Binance Earn API response structure."""
    print("="*60)
    print("DEBUGGING BINANCE EARN API RESPONSE STRUCTURE")
    print("="*60)
    
    binance_service = BinanceService()
    
    # Clear cache first
    print("\n--- Clearing Earn Cache ---")
    binance_service.storage.delete_data('binance_simple_earn_flexible', {'asset': None})
    binance_service.storage.delete_data('binance_simple_earn_locked', {'asset': None})
    print("✅ Cleared Binance Earn cache")
    
    # Test flexible positions with raw output
    print("\n--- Raw Flexible Positions Response ---")
    try:
        flexible_positions = binance_service.get_simple_earn_flexible_positions()
        print(f"Response type: {type(flexible_positions)}")
        print(f"Response length: {len(flexible_positions) if hasattr(flexible_positions, '__len__') else 'N/A'}")
        print(f"Raw response: {flexible_positions}")
        
        if isinstance(flexible_positions, list):
            print("\n--- Analyzing List Items ---")
            for i, item in enumerate(flexible_positions):
                print(f"Item {i}: Type={type(item)}, Value={item}")
                if hasattr(item, '__dict__'):
                    print(f"  Attributes: {item.__dict__}")
                    
        elif isinstance(flexible_positions, dict):
            print("\n--- Analyzing Dictionary ---")
            for key, value in flexible_positions.items():
                print(f"Key: {key}, Type: {type(value)}, Value: {value}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    # Test locked positions with raw output
    print("\n--- Raw Locked Positions Response ---")
    try:
        locked_positions = binance_service.get_simple_earn_locked_positions()
        print(f"Response type: {type(locked_positions)}")
        print(f"Response length: {len(locked_positions) if hasattr(locked_positions, '__len__') else 'N/A'}")
        print(f"Raw response: {locked_positions}")
        
        if isinstance(locked_positions, list):
            print("\n--- Analyzing List Items ---")
            for i, item in enumerate(locked_positions):
                print(f"Item {i}: Type={type(item)}, Value={item}")
                if hasattr(item, '__dict__'):
                    print(f"  Attributes: {item.__dict__}")
                    
        elif isinstance(locked_positions, dict):
            print("\n--- Analyzing Dictionary ---")
            for key, value in locked_positions.items():
                print(f"Key: {key}, Type: {type(value)}, Value: {value}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    # Test direct API call without caching
    print("\n--- Direct API Call Test ---")
    try:
        print("Testing direct client call...")
        client = binance_service.client
        
        # Try direct API call
        direct_flexible = client.get_simple_earn_flexible_product_position()
        print(f"Direct flexible type: {type(direct_flexible)}")
        print(f"Direct flexible: {direct_flexible}")
        
        direct_locked = client.get_simple_earn_locked_product_position()
        print(f"Direct locked type: {type(direct_locked)}")
        print(f"Direct locked: {direct_locked}")
        
    except Exception as e:
        print(f"❌ Direct API call error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_binance_earn()
