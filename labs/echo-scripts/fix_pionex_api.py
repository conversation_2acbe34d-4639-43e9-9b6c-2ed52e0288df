#!/usr/bin/env python3
"""
Fix Pionex API connection issues and test real data fetching.
"""

import sys
import os
import time
import requests
import hmac
import hashlib

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.pionex_service import PionexService
from services.storage.crypto_storage import CryptoStorage
from config import PIONEX_API_KEY, PIONEX_API_SECRET

def clear_pionex_error_cache():
    """Clear any cached Pionex API errors."""
    print("="*60)
    print("CLEARING PIONEX ERROR CACHE")
    print("="*60)
    
    storage = CryptoStorage()
    
    # Clear the API key error cache
    if storage.data_exists('pionex_api_key_error', {}):
        storage.delete_data('pionex_api_key_error', {})
        print("✅ Cleared Pionex API key error cache")
    else:
        print("ℹ️ No Pionex API key error cache found")
    
    # Clear other Pionex caches that might be stale
    cache_keys = [
        'pionex_balances',
        'pionex_active_operations', 
        'pionex_detailed_balances',
        'pionex_movements'
    ]
    
    for key in cache_keys:
        if storage.data_exists(key, {}):
            storage.delete_data(key, {})
            print(f"✅ Cleared {key} cache")

def test_pionex_signature():
    """Test Pionex signature generation manually."""
    print("\n" + "="*60)
    print("TESTING PIONEX SIGNATURE GENERATION")
    print("="*60)
    
    # Test parameters
    method = "GET"
    endpoint = "/api/v1/account/balances"
    timestamp = int(time.time() * 1000)
    params = {"timestamp": timestamp}
    
    print(f"Method: {method}")
    print(f"Endpoint: {endpoint}")
    print(f"Timestamp: {timestamp}")
    print(f"API Key: {PIONEX_API_KEY[:10]}...")
    print(f"API Secret: {PIONEX_API_SECRET[:10]}...")
    
    # Generate signature manually
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
    path_url = f"{endpoint}?{query_string}"
    message = f"{method}{path_url}"
    
    print(f"Query string: {query_string}")
    print(f"Path URL: {path_url}")
    print(f"Message to sign: {message}")
    
    signature = hmac.new(
        PIONEX_API_SECRET.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    print(f"Generated signature: {signature}")
    
    return signature, params

def test_direct_pionex_api():
    """Test direct API call to Pionex."""
    print("\n" + "="*60)
    print("TESTING DIRECT PIONEX API CALL")
    print("="*60)
    
    signature, params = test_pionex_signature()
    
    url = "https://api.pionex.com/api/v1/account/balances"
    
    headers = {
        'PIONEX-KEY': PIONEX_API_KEY,
        'PIONEX-SIGNATURE': signature,
        'Content-Type': 'application/json'
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Params: {params}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response data: {data}")
            
            if data.get('result') is True:
                balances = data.get('data', {}).get('balances', [])
                print(f"✅ Success! Found {len(balances)} balances")
                
                # Show non-zero balances
                non_zero = [b for b in balances if float(b.get('free', 0)) + float(b.get('locked', 0)) > 0]
                print(f"Non-zero balances: {len(non_zero)}")
                
                for balance in non_zero[:5]:  # Show first 5
                    asset = balance.get('asset')
                    free = balance.get('free')
                    locked = balance.get('locked')
                    print(f"  {asset}: Free={free}, Locked={locked}")
                    
                return True
            else:
                print(f"❌ API returned error: {data}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_pionex_grid_bots():
    """Test fetching grid bot data from Pionex."""
    print("\n" + "="*60)
    print("TESTING PIONEX GRID BOT DATA")
    print("="*60)
    
    # Note: Pionex doesn't have public API endpoints for grid bots
    # Grid bots are managed through the web interface
    # We need to check if there are any undocumented endpoints
    
    endpoints_to_try = [
        "/api/v1/grid/bots",
        "/api/v1/bots",
        "/api/v1/grid/list",
        "/api/v1/account/grid",
        "/api/v1/trading/grid"
    ]
    
    for endpoint in endpoints_to_try:
        print(f"\nTrying endpoint: {endpoint}")
        
        timestamp = int(time.time() * 1000)
        params = {"timestamp": timestamp}
        
        # Generate signature
        sorted_params = sorted(params.items())
        query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        path_url = f"{endpoint}?{query_string}"
        message = f"GET{path_url}"
        
        signature = hmac.new(
            PIONEX_API_SECRET.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            'PIONEX-KEY': PIONEX_API_KEY,
            'PIONEX-SIGNATURE': signature,
            'Content-Type': 'application/json'
        }
        
        try:
            url = f"https://api.pionex.com{endpoint}"
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"  Response: {data}")
                
                if data.get('result') is True:
                    print(f"  ✅ Success! Found data: {data.get('data', {})}")
                else:
                    print(f"  ❌ API error: {data.get('code', 'Unknown')}")
            else:
                print(f"  ❌ HTTP error: {response.text[:100]}")
                
        except Exception as e:
            print(f"  ❌ Request failed: {e}")

def test_fixed_pionex_service():
    """Test the Pionex service after clearing caches."""
    print("\n" + "="*60)
    print("TESTING FIXED PIONEX SERVICE")
    print("="*60)
    
    pionex = PionexService()
    
    print(f"API configured: {pionex._is_api_configured()}")
    
    # Test account balances
    print("\n--- Testing Account Balances ---")
    balances = pionex.get_account_balances()
    print(f"Balances: {len(balances) if isinstance(balances, list) else 'Error'}")
    
    if isinstance(balances, list) and balances:
        non_zero = [b for b in balances if float(b.get('free', 0)) + float(b.get('locked', 0)) > 0]
        print(f"Non-zero balances: {len(non_zero)}")
        
        for balance in non_zero[:3]:
            print(f"  {balance}")
    
    # Test active operations
    print("\n--- Testing Active Operations ---")
    operations = pionex.get_active_operations()
    
    if isinstance(operations, dict):
        grid_bots = operations.get('grid_bots', [])
        active_orders = operations.get('active_orders', [])
        
        print(f"Grid bots: {len(grid_bots)}")
        print(f"Active orders: {len(active_orders)}")
        
        # Check if we're getting real data or sample data
        if grid_bots:
            first_bot = grid_bots[0]
            if first_bot.get('id') in ['12345', '12346', '12347']:
                print("❌ Still getting sample data")
            else:
                print("✅ Getting real grid bot data!")
                
        if active_orders:
            print("Active orders found:")
            for order in active_orders[:3]:
                print(f"  {order}")

def main():
    """Main function to fix and test Pionex API."""
    print("🔧 FIXING PIONEX API CONNECTION ISSUES")
    print("=" * 80)
    
    try:
        # Step 1: Clear error caches
        clear_pionex_error_cache()
        
        # Step 2: Test signature generation
        test_pionex_signature()
        
        # Step 3: Test direct API call
        api_success = test_direct_pionex_api()
        
        # Step 4: Test grid bot endpoints
        test_pionex_grid_bots()
        
        # Step 5: Test fixed service
        test_fixed_pionex_service()
        
        print("\n" + "="*60)
        print("FIX SUMMARY")
        print("="*60)
        
        if api_success:
            print("✅ Pionex API connection is working")
            print("✅ Authentication is successful")
            print("ℹ️ Grid bots may not be available via API")
            print("ℹ️ Check dashboard for updated real data")
        else:
            print("❌ Pionex API connection still has issues")
            print("🔍 Check API keys and permissions")
            
    except Exception as e:
        print(f"\n❌ Fix failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
