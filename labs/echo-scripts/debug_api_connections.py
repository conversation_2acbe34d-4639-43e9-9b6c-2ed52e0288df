#!/usr/bin/env python3
"""
Debug script to test API connections and identify data discrepancies.
Tests both Pionex and Binance API connections and data fetching.
"""

import sys
import os
import time
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.pionex_service import PionexService
from services.binance_service import BinanceService
from config import PIONEX_API_KEY, PIONEX_API_SECRET, BINANCE_API_KEY, BINANCE_API_SECRET

def test_api_keys():
    """Test if API keys are properly configured."""
    print("="*60)
    print("TESTING API KEY CONFIGURATION")
    print("="*60)
    
    print(f"Binance API Key: {'✅ Configured' if BINANCE_API_KEY else '❌ Missing'}")
    print(f"Binance API Secret: {'✅ Configured' if BINANCE_API_SECRET else '❌ Missing'}")
    print(f"Pionex API Key: {'✅ Configured' if PIONEX_API_KEY else '❌ Missing'}")
    print(f"Pionex API Secret: {'✅ Configured' if PIONEX_API_SECRET else '❌ Missing'}")
    
    if PIONEX_API_KEY:
        print(f"Pionex API Key length: {len(PIONEX_API_KEY)} characters")
        print(f"Pionex API Key preview: {PIONEX_API_KEY[:10]}...")
    
    if BINANCE_API_KEY:
        print(f"Binance API Key length: {len(BINANCE_API_KEY)} characters")
        print(f"Binance API Key preview: {BINANCE_API_KEY[:10]}...")

def test_pionex_connection():
    """Test Pionex API connection and data fetching."""
    print("\n" + "="*60)
    print("TESTING PIONEX API CONNECTION")
    print("="*60)
    
    pionex = PionexService()
    
    # Test API configuration
    print(f"API configured: {pionex._is_api_configured()}")
    
    # Test basic account balances
    print("\n--- Testing Account Balances ---")
    try:
        balances = pionex.get_account_balances()
        print(f"Balances fetched: {len(balances) if isinstance(balances, list) else 'Error'}")
        
        if isinstance(balances, list) and balances:
            print("Sample balances:")
            for balance in balances[:3]:  # Show first 3
                print(f"  {balance}")
        elif not balances:
            print("No balances returned - this might indicate API issues")
        
    except Exception as e:
        print(f"Error fetching balances: {e}")
    
    # Test active operations (grid bots)
    print("\n--- Testing Active Operations (Grid Bots) ---")
    try:
        operations = pionex.get_active_operations()
        print(f"Operations type: {type(operations)}")
        
        if isinstance(operations, dict):
            grid_bots = operations.get('grid_bots', [])
            active_orders = operations.get('active_orders', [])
            
            print(f"Grid bots found: {len(grid_bots)}")
            print(f"Active orders found: {len(active_orders)}")
            
            if grid_bots:
                print("Grid bot details:")
                for i, bot in enumerate(grid_bots[:3]):  # Show first 3
                    print(f"  Bot {i+1}: {bot.get('name', 'Unknown')} - {bot.get('symbol', 'Unknown')} - Status: {bot.get('status', 'Unknown')}")
            else:
                print("❌ No grid bots found - this indicates sample data is being used")
                
        else:
            print(f"Unexpected operations format: {operations}")
            
    except Exception as e:
        print(f"Error fetching active operations: {e}")
    
    # Test detailed spot balances
    print("\n--- Testing Detailed Spot Balances ---")
    try:
        detailed_balances = pionex.get_detailed_spot_balances()
        print(f"Detailed balances fetched: {len(detailed_balances) if isinstance(detailed_balances, list) else 'Error'}")
        
        if isinstance(detailed_balances, list) and detailed_balances:
            print("Sample detailed balances:")
            for balance in detailed_balances[:3]:  # Show first 3
                asset = balance.get('asset', 'Unknown')
                total = balance.get('total', 0)
                usd_value = balance.get('usd_value', 0)
                print(f"  {asset}: {total} (${usd_value:.2f})")
                
    except Exception as e:
        print(f"Error fetching detailed balances: {e}")
    
    # Test direct API call
    print("\n--- Testing Direct API Call ---")
    try:
        # Try a simple API endpoint
        endpoint = "/api/v1/account/balances"
        response = pionex._make_request('GET', endpoint)
        print(f"Direct API response type: {type(response)}")
        print(f"Direct API response: {response}")
        
    except Exception as e:
        print(f"Error with direct API call: {e}")

def test_binance_connection():
    """Test Binance API connection and data fetching."""
    print("\n" + "="*60)
    print("TESTING BINANCE API CONNECTION")
    print("="*60)
    
    binance = BinanceService()
    
    # Test spot account snapshot
    print("\n--- Testing Spot Account Snapshot ---")
    try:
        snapshot = binance.get_spot_account_snapshot()
        print(f"Snapshot fetched: {len(snapshot) if isinstance(snapshot, list) else 'Error'}")
        
        if isinstance(snapshot, list) and snapshot:
            latest_snapshot = snapshot[0] if snapshot else {}
            balances = latest_snapshot.get('data', {}).get('balances', [])
            
            print(f"Total assets in latest snapshot: {len(balances)}")
            
            # Look for BNB specifically
            bnb_balances = [b for b in balances if b.get('asset') == 'BNB']
            if bnb_balances:
                bnb = bnb_balances[0]
                free = float(bnb.get('free', 0))
                locked = float(bnb.get('locked', 0))
                print(f"BNB found - Free: {free}, Locked: {locked}, Total: {free + locked}")
            else:
                print("❌ No BNB found in spot balances")
            
            # Show assets with non-zero balances
            non_zero_balances = [b for b in balances if float(b.get('free', 0)) + float(b.get('locked', 0)) > 0]
            print(f"Assets with non-zero balances: {len(non_zero_balances)}")
            
            for balance in non_zero_balances[:5]:  # Show first 5
                asset = balance.get('asset')
                free = float(balance.get('free', 0))
                locked = float(balance.get('locked', 0))
                total = free + locked
                print(f"  {asset}: Free={free}, Locked={locked}, Total={total}")
                
    except Exception as e:
        print(f"Error fetching spot snapshot: {e}")
    
    # Test if there are Binance Earn products
    print("\n--- Testing Binance Earn Integration ---")
    try:
        # Check if the client has methods for Earn products
        client = binance.client
        
        # Try to get savings products (this might not be available with current permissions)
        print("Checking for Earn/Savings methods...")
        
        # List available methods that might be related to Earn
        earn_methods = [method for method in dir(client) if 'saving' in method.lower() or 'earn' in method.lower() or 'lending' in method.lower()]
        print(f"Potential Earn methods found: {earn_methods}")
        
        # Try to call some Earn-related endpoints if they exist
        if hasattr(client, 'get_savings_products'):
            try:
                savings = client.get_savings_products()
                print(f"Savings products: {len(savings) if isinstance(savings, list) else 'Error'}")
            except Exception as e:
                print(f"Error fetching savings products: {e}")
        
        if hasattr(client, 'get_savings_account'):
            try:
                savings_account = client.get_savings_account()
                print(f"Savings account: {savings_account}")
            except Exception as e:
                print(f"Error fetching savings account: {e}")
                
    except Exception as e:
        print(f"Error testing Earn integration: {e}")

def test_portfolio_data():
    """Test the portfolio service to see what data it's actually using."""
    print("\n" + "="*60)
    print("TESTING PORTFOLIO SERVICE DATA")
    print("="*60)
    
    from services.portfolio_service import PortfolioService
    
    portfolio = PortfolioService()
    
    # Test Pionex holdings
    print("\n--- Testing Pionex Holdings in Portfolio ---")
    try:
        pionex_holdings = portfolio._get_pionex_holdings()
        print(f"Pionex holdings type: {type(pionex_holdings)}")
        
        if hasattr(pionex_holdings, 'empty') and not pionex_holdings.empty:
            print(f"Pionex holdings count: {len(pionex_holdings)}")
            print("Pionex assets:")
            for asset in pionex_holdings['asset'].unique():
                asset_data = pionex_holdings[pionex_holdings['asset'] == asset]
                total_value = asset_data['value_usd'].sum()
                print(f"  {asset}: ${total_value:.2f}")
        else:
            print("❌ No Pionex holdings found or empty DataFrame")
            
    except Exception as e:
        print(f"Error getting Pionex holdings: {e}")
    
    # Test Binance holdings
    print("\n--- Testing Binance Holdings in Portfolio ---")
    try:
        binance_holdings = portfolio._get_binance_spot_holdings()
        print(f"Binance holdings type: {type(binance_holdings)}")
        
        if hasattr(binance_holdings, 'empty') and not binance_holdings.empty:
            print(f"Binance holdings count: {len(binance_holdings)}")
            print("Binance assets:")
            for asset in binance_holdings['asset'].unique():
                asset_data = binance_holdings[binance_holdings['asset'] == asset]
                total_value = asset_data['value_usd'].sum()
                quantity = asset_data['quantity'].sum()
                print(f"  {asset}: {quantity} (${total_value:.2f})")
        else:
            print("❌ No Binance holdings found or empty DataFrame")
            
    except Exception as e:
        print(f"Error getting Binance holdings: {e}")

def main():
    """Main diagnostic function."""
    print("🔍 DIAGNOSING API CONNECTIONS AND DATA DISCREPANCIES")
    print("=" * 80)
    
    try:
        # Test 1: API key configuration
        test_api_keys()
        
        # Test 2: Pionex connection
        test_pionex_connection()
        
        # Test 3: Binance connection
        test_binance_connection()
        
        # Test 4: Portfolio data
        test_portfolio_data()
        
        print("\n" + "="*60)
        print("DIAGNOSIS SUMMARY")
        print("="*60)
        print("✅ API keys are configured")
        print("🔍 Check the output above for specific issues:")
        print("   - Pionex: Look for 'No grid bots found' or API errors")
        print("   - Binance: Look for BNB balances and Earn integration")
        print("   - Portfolio: Check if real data is being used")
        
    except Exception as e:
        print(f"\n❌ Diagnostic failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
