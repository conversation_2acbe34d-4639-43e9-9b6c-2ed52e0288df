services:
  echo-app:
    build: .
    container_name: echo-app
    ports:
      - "8080:8080"  # Expone el puerto 80 del contenedor al host
    volumes:
      - .:/app
    environment:
      - PIA_USERNAME=${PIA_USERNAME}
      - PIA_PASSWORD=${PIA_PASSWORD}
    cap_add:
      - NET_ADMIN
      - SYS_ADMIN
    devices:
      - /dev/net/tun:/dev/net/tun
    sysctls:
      - net.ipv4.ip_forward=1
    privileged: true
    networks:
      - vpn-network

  nginx-proxy:
    image: nginx:alpine
    container_name: nginx-proxy
    network_mode: host
    ports:
      - "80:80"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/conf.d/default.conf:ro
      - ./htpasswd:/etc/nginx/.htpasswd:ro
    depends_on:
      - echo-app

networks:
  vpn-network:
    driver: bridge
