server {
    listen 8080 default_server;
    server_name _;

    # Increase timeouts for slow connections
    proxy_connect_timeout 60s;
    proxy_read_timeout 60s;
    proxy_send_timeout 60s;

    location / {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Disable buffering for streaming responses
        proxy_buffering off;

        # Basic authentication
        auth_basic "Protegido";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }

    # Health check endpoint that doesn't require authentication
    location /health {
        return 200 'OK';
        add_header Content-Type text/plain;
    }
}
