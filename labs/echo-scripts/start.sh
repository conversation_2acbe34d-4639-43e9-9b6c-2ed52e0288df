#!/bin/bash

# Function for logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

error() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

# Setup VPN if credentials are available
if [ -n "$PIA_USERNAME" ] && [ -n "$PIA_PASSWORD" ]; then
    log "Setting up VPN connection..."

    # Create necessary directories
    mkdir -p /etc/openvpn/client

    # Download PIA configs
    cd /tmp
    wget -qO pia-openvpn.zip "https://www.privateinternetaccess.com/openvpn/openvpn.zip"
    unzip -o pia-openvpn.zip

    # Select server (prefer Singapore, Japan, or London)
    PIA_SERVERS=("singapore" "japan" "uk_london")
    SELECTED_SERVER=""

    for s in "${PIA_SERVERS[@]}"; do
        if [ -f "${s}.ovpn" ]; then
            SELECTED_SERVER="$s"
            break
        fi
    done

    if [ -z "$SELECTED_SERVER" ]; then
        SELECTED_SERVER=$(ls *.ovpn | head -1 | sed 's/.ovpn//')
        log "Fallback server: $SELECTED_SERVER"
    else
        log "Using preferred server: $SELECTED_SERVER"
    fi

    # Extract host and port from .ovpn file
    REMOTE_HOST=$(grep '^remote ' "${SELECTED_SERVER}.ovpn" | head -1 | awk '{print $2}')
    REMOTE_PORT=$(grep '^remote ' "${SELECTED_SERVER}.ovpn" | head -1 | awk '{print $3}')
    log "Remote server: $REMOTE_HOST:$REMOTE_PORT"

    # Copy certificates
    cp ca.rsa.2048.crt crl.rsa.2048.pem /etc/openvpn/client/

    # Write credentials
    cat > /etc/openvpn/client/pia-credentials << EOF
$PIA_USERNAME
$PIA_PASSWORD
EOF
    chmod 600 /etc/openvpn/client/pia-credentials

    # Write VPN config
    cat > /etc/openvpn/client/pia-binance.conf << EOF
client
dev tun
proto udp
remote $REMOTE_HOST $REMOTE_PORT
resolv-retry infinite
nobind
persist-key
persist-tun
ca /etc/openvpn/client/ca.rsa.2048.crt
crl-verify /etc/openvpn/client/crl.rsa.2048.pem
auth-user-pass /etc/openvpn/client/pia-credentials
remote-cert-tls server
auth SHA1
cipher aes-128-cbc
comp-lzo
dhcp-option DNS **************
dhcp-option DNS **************
redirect-gateway def1
connect-retry-max 3
connect-retry 10
ping 10
ping-restart 60
verb 3
mute 3
script-security 2
EOF

    # Start VPN with more verbose output for debugging
    log "Starting VPN connection..."
    mkdir -p /var/log/openvpn

    # Ensure TUN device exists and has correct permissions
    if [ ! -c /dev/net/tun ]; then
        log "Creating TUN device..."
        mkdir -p /dev/net
        mknod /dev/net/tun c 10 200
        chmod 600 /dev/net/tun
    fi

    # Start OpenVPN
    openvpn --config /etc/openvpn/client/pia-binance.conf --log /var/log/openvpn/openvpn.log --verb 5 --daemon

    # Give OpenVPN more time to start
    sleep 10

    # Check OpenVPN logs
    log "OpenVPN startup logs:"
    if [ -f /var/log/openvpn/openvpn.log ]; then
        cat /var/log/openvpn/openvpn.log
    else
        log "No OpenVPN log file found"
    fi

    # Check if OpenVPN process is running
    log "OpenVPN process status:"
    ps aux | grep openvpn

    # Wait for VPN to connect
    log "Waiting for VPN connection..."
    for i in {1..60}; do  # Increased timeout to 60 seconds
        if ip addr show | grep -q tun0; then
            log "VPN interface tun0 detected"

            # Check interface details
            log "tun0 interface details:"
            ip addr show tun0

            log "Current routing table:"
            ip route

            # Ensure proper routing through VPN
            log "Setting up routing rules for VPN..."
            # Get the default gateway
            DEFAULT_GW=$(ip route | grep default | awk '{print $3}')
            log "Default gateway: $DEFAULT_GW"

            # Get the VPN gateway and interface details
            TUN_ADDR=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d/ -f1)
            log "TUN0 address: $TUN_ADDR"

            if [ -n "$DEFAULT_GW" ] && [ -n "$TUN_ADDR" ]; then
                # Save original default route
                ORIG_DEFAULT=$(ip route | grep default)
                log "Original default route: $ORIG_DEFAULT"

                # Delete the default route
                log "Deleting default route"
                ip route del default

                # Add a new default route via the VPN
                log "Adding new default route via VPN"
                ip route add default dev tun0

                # Add a route to the VPN server via the original default gateway
                log "Adding route to VPN server via original gateway"
                # Resolve domain name to IP address
                if command -v host >/dev/null 2>&1; then
                    REMOTE_IP=$(host -t A $REMOTE_HOST | grep "has address" | head -1 | awk '{print $4}')
                    if [ -n "$REMOTE_IP" ]; then
                        log "Resolved $REMOTE_HOST to $REMOTE_IP"
                        ip route add $REMOTE_IP via $DEFAULT_GW
                    else
                        log "Could not resolve $REMOTE_HOST to an IP address, skipping route"
                    fi
                else
                    # If host command is not available, try with dig or nslookup
                    if command -v dig >/dev/null 2>&1; then
                        REMOTE_IP=$(dig +short $REMOTE_HOST | head -1)
                    elif command -v nslookup >/dev/null 2>&1; then
                        REMOTE_IP=$(nslookup $REMOTE_HOST | grep "Address:" | tail -1 | awk '{print $2}')
                    fi

                    if [ -n "$REMOTE_IP" ]; then
                        log "Resolved $REMOTE_HOST to $REMOTE_IP"
                        ip route add $REMOTE_IP via $DEFAULT_GW
                    else
                        log "Could not resolve $REMOTE_HOST to an IP address, skipping route"
                    fi
                fi

                # Make sure local traffic can still reach the Docker host
                log "Ensuring local Docker traffic works"
                ip route add **********/12 via $DEFAULT_GW
                ip route add ***********/16 via $DEFAULT_GW

                log "Routing configured successfully"

                # Verify routing
                log "Updated routing table:"
                ip route

                # Test connection
                log "Testing VPN connection..."
                PUBLIC_IP=$(curl -s --max-time 10 https://ifconfig.me || echo "Timeout")
                log "Current public IP: $PUBLIC_IP"

                # If we couldn't get the public IP, try an alternative service
                if [ "$PUBLIC_IP" = "Timeout" ]; then
                    log "Trying alternative IP service..."
                    PUBLIC_IP=$(curl -s --max-time 10 https://api.ipify.org || echo "Failed to get public IP")
                    log "Alternative public IP check: $PUBLIC_IP"
                fi
            else
                error "Failed to configure routing: DEFAULT_GW=$DEFAULT_GW, TUN_ADDR=$TUN_ADDR"
            fi

            break
        fi

        # Check OpenVPN logs periodically
        if [ $((i % 10)) -eq 0 ]; then
            log "OpenVPN logs after $i seconds:"
            if [ -f /var/log/openvpn/openvpn.log ]; then
                tail -n 20 /var/log/openvpn/openvpn.log
            fi
        fi

        if [ $i -eq 60 ]; then
            error "VPN connection failed after 60 seconds"
            log "Final OpenVPN logs:"
            if [ -f /var/log/openvpn/openvpn.log ]; then
                tail -n 50 /var/log/openvpn/openvpn.log
            fi
        fi
        sleep 1
    done
else
    log "VPN credentials not found, skipping VPN setup"
fi

# Return to app directory
cd /app

# Ensure Nginx is properly configured
log "Checking Nginx configuration..."
nginx -t || error "Nginx configuration test failed"

# Start Streamlit in background
log "Starting Streamlit application..."
PYTHONPATH=$(pwd) streamlit run app/main.py --server.port=8501 --server.address=0.0.0.0 &
STREAMLIT_PID=$!

# Wait for Streamlit to start
log "Waiting for Streamlit to start..."
for i in {1..30}; do
    if curl -s http://localhost:8501 > /dev/null; then
        log "Streamlit started successfully"
        break
    fi

    if [ $i -eq 30 ]; then
        error "Streamlit failed to start after 30 seconds"
        # Check if process is still running
        if kill -0 $STREAMLIT_PID 2>/dev/null; then
            log "Streamlit process is running but not responding"
        else
            log "Streamlit process is not running"
        fi
    fi
    sleep 1
done

# Start Nginx in foreground
log "Starting Nginx..."
log "Container setup complete. You should be able to access the application at http://$(curl -s https://ifconfig.me):80"
nginx -g "daemon off;"
