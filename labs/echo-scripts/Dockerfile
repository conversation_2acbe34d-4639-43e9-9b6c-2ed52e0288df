FROM python:3.12

# Instalar dependencias del sistema, uv y OpenVPN
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl gcc libffi-dev libssl-dev nginx apache2-utils \
    openvpn wget unzip iptables && \
    # Manual setup for OpenVPN DNS resolution without resolvconf
    mkdir -p /etc/openvpn/update-resolv-conf.d && \
    mkdir -p /run/openvpn && \
    # Create TUN/TAP device directory
    mkdir -p /dev/net && \
    mknod /dev/net/tun c 10 200 && \
    chmod 600 /dev/net/tun && \
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    mv ~/.local/bin/uv /usr/local/bin/uv && \
    mv ~/.local/bin/uvx /usr/local/bin/uvx && \
    apt-get purge -y gcc && apt-get clean && rm -rf /var/lib/apt/lists/*

# Establecer el directorio de trabajo
WORKDIR /app

# Copiar e instalar dependencias con uv
COPY requirements.txt .
RUN uv pip install --system --no-cache-dir -r requirements.txt

# Copiar el resto del código y configuraciones
COPY . .

# Configurar nginx
COPY nginx.conf /etc/nginx/sites-available/default
COPY htpasswd /etc/nginx/.htpasswd

RUN rm -f /etc/nginx/sites-enabled/default && \
    ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/ && \
    ln -sf /dev/stdout /var/log/nginx/access.log && \
    ln -sf /dev/stderr /var/log/nginx/error.log

# Exponer el puerto HTTP
EXPOSE 80

# Script de inicio para lanzar Nginx y Streamlit
COPY start.sh /start.sh
RUN chmod +x /start.sh

CMD ["/start.sh"]
