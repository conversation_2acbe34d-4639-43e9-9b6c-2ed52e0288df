import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException
from config import BINANCE_API_KEY, BINANCE_API_SECRET
from services.storage import CryptoStorage
from utils.symbol_validator import symbol_validator
import requests

class BinanceService:
    """
    Service to interact with Binance API for fetching futures orders, spot wallet movements, and live data.
    Uses a storage system to cache API responses and reduce API calls.
    """
    def __init__(self):
        self.client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
        self.storage = CryptoStorage()

        # Default cache expiry times (in seconds)
        self.cache_expiry = {
            'default': 300,  # 5 minutes
            'price': 60,     # 1 minute
            'account': 600,  # 10 minutes
            'orders': 300,   # 5 minutes
            'trades': 300,   # 5 minutes
            'deposits': 1800,  # 30 minutes
            'withdrawals': 1800,  # 30 minutes
            'transfers': 1800,  # 30 minutes
            'income': 1800,  # 30 minutes
            'snapshot': 3600  # 1 hour
        }

    def get_account_trade_list(self, symbol, start_date=None, end_date=None, limit=500, fromId=None):
        """
        Fetches the account trade list (trade history) for the given symbol from the Binance Futures Account Trade List endpoint.
        This method implements full pagination using the 'fromId' parameter to retrieve all available pages.

        :param symbol: Trading pair symbol (e.g., 'BTCUSDT').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :param limit: Maximum number of trade records per API call.
        :param fromId: Optional trade ID from which to start fetching results.
        :return: List of all trade events.
        """
        params = {"symbol": symbol, "limit": limit}
        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        all_trades = []
        current_from_id = fromId

        while True:
            if current_from_id is not None:
                params["fromId"] = current_from_id

            try:
                trades = self.client.futures_account_trades(**params)
            except (BinanceAPIException, BinanceRequestException) as e:
                print(f"Binance API Exception when fetching account trade list: {e}")
                break
            except Exception as e:
                print(f"General Exception when fetching account trade list: {e}")
                break

            if not trades:
                break

            all_trades.extend(trades)

            # If fewer trades than limit are returned, we've reached the last page.
            if len(trades) < limit:
                break

            # Set current_from_id to last trade's id + 1 for the next iteration
            current_from_id = trades[-1]['id'] + 1

            time.sleep(0.2)  # Small delay to respect rate limits

        return all_trades

    def get_income_history(self, symbol=None, income_type=None, start_date=None, end_date=None, limit=100):
        """
        Fetches income events (such as realized PNL) from the Binance Futures Income endpoint.
        Uses cached data if available and not expired.

        :param symbol: Optional symbol filter (e.g., 'BTCUSDT').
        :param income_type: Optional income type filter (e.g., 'REALIZED_PNL').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :param limit: Maximum number of records to return.
        :return: List of income events.
        """
        # Prepare parameters for API call and cache key
        params = {"limit": limit}
        cache_params = {
            'symbol': symbol,
            'income_type': income_type,
            'start_date': start_date,
            'end_date': end_date,
            'limit': limit
        }

        if symbol:
            params["symbol"] = symbol
        if income_type:
            params["incomeType"] = income_type
        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_income', cache_params)
        if cached_data is not None:
            print(f"Using cached income history data")
            return cached_data

        # If not in cache, fetch from API
        try:
            income_history = self.client.futures_income_history(**params)

            # Process timestamps to ensure they're in the correct format
            for item in income_history:
                if 'time' in item and isinstance(item['time'], (int, float)):
                    # Convert timestamp to ISO format string for better compatibility
                    timestamp = item['time']
                    try:
                        dt = datetime.fromtimestamp(timestamp / 1000)
                        item['time'] = dt.isoformat()
                    except (ValueError, OverflowError):
                        # Keep original if conversion fails
                        pass

            # Cache the result
            self.storage.save_data(
                'binance_income',
                income_history,
                cache_params,
                expiry=self.cache_expiry['income']
            )

            return income_history
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching income history: {e}")
        except Exception as e:
            print(f"General Exception when fetching income history: {e}")
        return []

    def get_closed_futures_orders(self, symbols, start_date, end_date=None):
        """
        Fetches closed futures orders for the given symbols from start_date to end_date.
        Adheres to Binance's 7-day query restriction by paginating requests accordingly.

        :param symbols: List of symbol strings (e.g., ['BTCUSDT', 'ETHUSDT']).
        :param start_date: Initial date as a string in 'YYYY-MM-DD' format.
        :param end_date: Optional end date as a string in 'YYYY-MM-DD' format. Defaults to current date.
        :return: List of closed orders.
        """
        closed_orders = []
        start_timestamp = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000) if end_date else int(time.time() * 1000)

        for symbol in symbols:
            current_start = start_timestamp
            while current_start < end_timestamp:
                current_end = min(current_start + 7 * 24 * 60 * 60 * 1000, end_timestamp)
                try:
                    orders = self.client.futures_get_all_orders(
                        symbol=symbol,
                        startTime=current_start,
                        endTime=current_end,
                        limit=100
                    )
                    if orders:
                        # Add entry price calculation for each order
                        for order in orders:
                            try:
                                avg_price = float(order.get("avgPrice", 0))
                                if avg_price > 0:
                                    entry_price = avg_price
                                else:
                                    executed_qty = float(order.get("executedQty", 0))
                                    if executed_qty > 0:
                                        cum_quote = float(order.get("cumQuote", 0))
                                        entry_price = cum_quote / executed_qty
                                    else:
                                        entry_price = None
                            except Exception as e:
                                print(f"Error calculating entry price for order {order.get('orderId')}: {e}")
                                entry_price = None
                            order["entryPrice"] = entry_price

                        closed_orders.extend(orders)
                        # Increment current_start to the timestamp of the last fetched order to avoid duplicates
                        current_start = orders[-1]['time'] + 1
                    else:
                        # If no orders are returned, move to the next time window
                        current_start = current_end
                    time.sleep(0.2)  # Avoid rate limit issues
                except (BinanceAPIException, BinanceRequestException) as e:
                    print(f"Binance API Exception for {symbol}: {e}")
                    break  # Exit loop on exception
                except Exception as e:
                    print(f"General Exception for {symbol}: {e}")
                    break  # Exit loop on exception

        return closed_orders

    def get_live_orders(self):
        """
        Fetches currently open futures orders. If no orders exist, fetches open positions.
        """
        try:
            orders = self.client.futures_get_open_orders()
            if orders:
                return orders
            else:
                # If no open orders, return active positions instead
                return self.get_live_positions()
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception: {e}")
        except Exception as e:
            print(f"General Exception: {e}")
        return []

    def get_live_positions(self):
        """
        Fetches active futures positions.
        """
        try:
            positions = self.client.futures_position_information()
            return [
                pos for pos in positions if float(pos["positionAmt"]) != 0
            ]  # Only return non-zero positions
        except Exception as e:
            print(f"Error fetching live positions: {e}")
            return []

    def get_current_price(self, symbol, timeout=10):
        """
        Fetches the current price for the given symbol with improved error handling.
        Uses cached data if available and not expired.
        Handles invalid symbols gracefully with blacklisting to prevent infinite retries.

        :param symbol: Trading pair symbol (e.g., 'BTCUSDT').
        :param timeout: Timeout for API calls in seconds.
        :return: Current price as float or None if not available.
        """
        if not symbol:
            return None

        # Normalize and validate symbol
        normalized_symbol = symbol_validator.normalize_symbol(symbol)
        if not normalized_symbol:
            # Symbol is invalid or blacklisted
            if symbol_validator.should_log_error(symbol, "invalid_symbol"):
                symbol_validator.log_error_once(symbol, "Symbol is invalid or blacklisted")
            return None

        # Use normalized symbol for all operations
        symbol = normalized_symbol
        cache_params = {'symbol': symbol}

        # Check if price exists in cache and is not expired
        cached_price = self.storage.load_data('binance_price', cache_params)
        if cached_price is not None:
            # Only log cache usage occasionally to reduce spam
            if symbol_validator.should_log_error(symbol, "cache_usage"):
                print(f"Using cached price for {symbol}")
                symbol_validator.log_error_once(symbol, "cache_usage")
            return cached_price

        # Check if symbol is valid for lookup
        if not symbol_validator.is_valid_symbol(symbol):
            return None

        # Try to fetch price with timeout
        try:
            # Set a timeout for the API call
            original_timeout = getattr(self.client, '_timeout', None)
            if hasattr(self.client, '_timeout'):
                self.client._timeout = timeout

            ticker = self.client.get_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])

            # Restore original timeout
            if original_timeout is not None and hasattr(self.client, '_timeout'):
                self.client._timeout = original_timeout

            # Cache the successful result
            self.storage.save_data(
                'binance_price',
                price,
                cache_params,
                expiry=self.cache_expiry['price']
            )

            # Record successful lookup
            symbol_validator.record_successful_lookup(symbol)
            return price

        except requests.exceptions.Timeout:
            symbol_validator.record_failed_lookup(symbol, "timeout")
            symbol_validator.log_error_once(symbol, "API timeout")
            return None

        except (BinanceAPIException, BinanceRequestException) as e:
            error_msg = str(e).lower()

            if 'invalid symbol' in error_msg or 'symbol not found' in error_msg:
                # Try alternatives for invalid symbols, but limit attempts
                alternatives = symbol_validator.get_alternative_symbols(symbol, max_alternatives=2)

                for alt_symbol in alternatives:
                    try:
                        if hasattr(self.client, '_timeout'):
                            self.client._timeout = timeout

                        ticker = self.client.get_symbol_ticker(symbol=alt_symbol)
                        price = float(ticker['price'])

                        # Restore timeout
                        if original_timeout is not None and hasattr(self.client, '_timeout'):
                            self.client._timeout = original_timeout

                        # Cache using original symbol
                        self.storage.save_data(
                            'binance_price',
                            price,
                            cache_params,
                            expiry=self.cache_expiry['price']
                        )

                        # Log successful alternative only once
                        if symbol_validator.should_log_error(symbol, f"alternative_{alt_symbol}"):
                            print(f"Using {alt_symbol} price for {symbol}")
                            symbol_validator.log_error_once(symbol, f"alternative_{alt_symbol}")

                        symbol_validator.record_successful_lookup(symbol)
                        return price

                    except Exception:
                        # Silently continue to next alternative
                        continue

                # All alternatives failed, record failure
                symbol_validator.record_failed_lookup(symbol, "invalid_symbol")
                symbol_validator.log_error_once(symbol, "Symbol not found and no valid alternatives")

            else:
                # Other API errors
                symbol_validator.record_failed_lookup(symbol, "api_error")
                symbol_validator.log_error_once(symbol, f"API error: {error_msg}")

            return None

        except Exception as e:
            # General exceptions
            symbol_validator.record_failed_lookup(symbol, "general_error")
            symbol_validator.log_error_once(symbol, f"Unexpected error: {str(e)}")
            return None

        finally:
            # Always restore original timeout
            if 'original_timeout' in locals() and original_timeout is not None and hasattr(self.client, '_timeout'):
                self.client._timeout = original_timeout

    def get_spot_deposit_history(self, coin=None, start_date=None, end_date=None):
        """
        Fetches deposit history for Spot wallet.
        Uses cached data if available and not expired.

        :param coin: Optional coin filter (e.g., 'BTC').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :return: List of deposit events.
        """
        # Prepare parameters for API call and cache key
        params = {}
        cache_params = {'coin': coin, 'start_date': start_date, 'end_date': end_date}

        if coin:
            params["coin"] = coin
        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_deposits', cache_params)
        if cached_data is not None:
            print(f"Using cached deposit history data")
            return cached_data

        # If not in cache, fetch from API
        try:
            deposit_history = self.client.get_deposit_history(**params)

            # Cache the result
            self.storage.save_data(
                'binance_deposits',
                deposit_history,
                cache_params,
                expiry=self.cache_expiry['deposits']
            )

            return deposit_history
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching deposit history: {e}")
        except Exception as e:
            print(f"General Exception when fetching deposit history: {e}")

        return []

    def get_spot_withdraw_history(self, coin=None, start_date=None, end_date=None):
        """
        Fetches withdrawal history for Spot wallet.
        Uses cached data if available and not expired.

        :param coin: Optional coin filter (e.g., 'BTC').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :return: List of withdrawal events.
        """
        # Prepare parameters for API call and cache key
        params = {}
        cache_params = {'coin': coin, 'start_date': start_date, 'end_date': end_date}

        if coin:
            params["coin"] = coin
        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_withdrawals', cache_params)
        if cached_data is not None:
            print(f"Using cached withdrawal history data")
            return cached_data

        # If not in cache, fetch from API
        try:
            withdraw_history = self.client.get_withdraw_history(**params)

            # Cache the result
            self.storage.save_data(
                'binance_withdrawals',
                withdraw_history,
                cache_params,
                expiry=self.cache_expiry['withdrawals']
            )

            return withdraw_history
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching withdrawal history: {e}")
        except Exception as e:
            print(f"General Exception when fetching withdrawal history: {e}")

        return []

    def get_futures_wallet_transfers(self, asset=None, start_date=None, end_date=None, transfer_type=None):
        """
        Fetches transfers between Spot and Futures wallets.
        Uses cached data if available and not expired.
        Note: This method requires Futures API permissions. If not available, it will return simulated data.

        :param asset: Optional asset filter (e.g., 'BTC').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :param transfer_type: Optional transfer type (1: Spot to USDT-M Futures, 2: USDT-M Futures to Spot, 3: Spot to COIN-M Futures, 4: COIN-M Futures to Spot).
        :return: List of transfer events.
        """
        # Prepare parameters for API call and cache key
        params = {}
        cache_params = {
            'asset': asset,
            'start_date': start_date,
            'end_date': end_date,
            'transfer_type': transfer_type
        }

        if asset:
            params["asset"] = asset
        if transfer_type:
            params["type"] = transfer_type
        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_transfers', cache_params)
        if cached_data is not None:
            print(f"Using cached futures transfers data")
            return cached_data

        # Check if we've already encountered a permissions error
        if self.storage.data_exists('binance_transfers_permission_error', {}):
            return []

        # If not in cache, fetch from API
        try:
            transfers = self.client.futures_account_transfer(**params)
            transfer_rows = transfers.get('rows', [])

            # Cache the result
            self.storage.save_data(
                'binance_transfers',
                transfer_rows,
                cache_params,
                expiry=self.cache_expiry['transfers']
            )

            return transfer_rows
        except (BinanceAPIException, BinanceRequestException) as e:
            # Handle authorization errors gracefully
            if 'You are not authorized' in str(e):
                # Create an empty cache entry to avoid repeated API calls
                self.storage.save_data(
                    'binance_transfers',
                    [],
                    cache_params,
                    expiry=86400  # Cache for 24 hours since this is a permissions issue
                )
                # Only print the message once
                if not self.storage.data_exists('binance_transfers_permission_error', {}):
                    print(f"Note: Futures transfers not available. This requires additional API permissions.")
                    # Create a marker to avoid repeated messages
                    self.storage.save_data('binance_transfers_permission_error', True, {}, expiry=86400)
            else:
                print(f"Binance API Exception when fetching futures transfers: {e}")
            # Return empty list for API errors
            return []
        except Exception as e:
            print(f"General Exception when fetching futures transfers: {e}")
            return []

    def get_spot_account_snapshot(self, start_date=None, end_date=None, limit=30):
        """
        Fetches daily account snapshot for Spot wallet.
        Uses cached data if available and not expired.

        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :param limit: Maximum number of records to return (default 30, max 30).
        :return: List of daily account snapshots.
        """
        # Prepare parameters for API call and cache key
        params = {"type": "SPOT", "limit": limit}
        cache_params = {
            'type': 'SPOT',
            'limit': limit,
            'start_date': start_date,
            'end_date': end_date
        }

        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_spot_snapshot', cache_params)
        if cached_data is not None:
            print(f"Using cached spot account snapshot data")
            return cached_data

        # If not in cache, fetch from API
        try:
            snapshot = self.client.get_account_snapshot(**params)

            if snapshot.get('code') == 200:
                snapshot_data = snapshot.get('snapshotVos', [])

                # Cache the result
                self.storage.save_data(
                    'binance_spot_snapshot',
                    snapshot_data,
                    cache_params,
                    expiry=self.cache_expiry['snapshot']
                )

                return snapshot_data
            else:
                print(f"Error fetching account snapshot: {snapshot}")
                return []
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching account snapshot: {e}")
        except Exception as e:
            print(f"General Exception when fetching account snapshot: {e}")

        return []

    def get_simple_earn_account(self):
        """
        Fetches Simple Earn account information including locked and flexible products.
        Uses cached data if available and not expired.

        :return: Dictionary with Simple Earn account data.
        """
        # Prepare cache key
        cache_params = {}

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_simple_earn_account', cache_params)

        if cached_data is not None:
            print(f"Using cached Simple Earn account data")
            return cached_data

        # If not in cache, fetch from API
        try:
            earn_account = self.client.get_simple_earn_account()

            # Cache the result
            self.storage.save_data(
                'binance_simple_earn_account',
                earn_account,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return earn_account

        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching Simple Earn account: {e}")
            return {}
        except Exception as e:
            print(f"General Exception when fetching Simple Earn account: {e}")
            return {}

    def get_simple_earn_flexible_positions(self, asset=None):
        """
        Fetches Simple Earn flexible product positions.
        Uses cached data if available and not expired.

        :param asset: Optional asset filter (e.g., 'BNB').
        :return: List of flexible positions.
        """
        # Prepare parameters and cache key
        params = {}
        cache_params = {'asset': asset}

        if asset:
            params['asset'] = asset

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_simple_earn_flexible', cache_params)

        if cached_data is not None:
            print(f"Using cached Simple Earn flexible positions data")
            return cached_data

        # If not in cache, fetch from API
        try:
            response = self.client.get_simple_earn_flexible_product_position(**params)

            # Extract positions from response structure
            # Binance API returns: {'total': int, 'rows': [list of positions]}
            if isinstance(response, dict) and 'rows' in response:
                positions = response['rows']
            else:
                positions = response if isinstance(response, list) else []

            # Cache the result
            self.storage.save_data(
                'binance_simple_earn_flexible',
                positions,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return positions

        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching Simple Earn flexible positions: {e}")
            return []
        except Exception as e:
            print(f"General Exception when fetching Simple Earn flexible positions: {e}")
            return []

    def get_simple_earn_locked_positions(self, asset=None):
        """
        Fetches Simple Earn locked product positions.
        Uses cached data if available and not expired.

        :param asset: Optional asset filter (e.g., 'BNB').
        :return: List of locked positions.
        """
        # Prepare parameters and cache key
        params = {}
        cache_params = {'asset': asset}

        if asset:
            params['asset'] = asset

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_simple_earn_locked', cache_params)

        if cached_data is not None:
            print(f"Using cached Simple Earn locked positions data")
            return cached_data

        # If not in cache, fetch from API
        try:
            response = self.client.get_simple_earn_locked_product_position(**params)

            # Extract positions from response structure
            # Binance API returns: {'total': int, 'rows': [list of positions]}
            if isinstance(response, dict) and 'rows' in response:
                positions = response['rows']
            else:
                positions = response if isinstance(response, list) else []

            # Cache the result
            self.storage.save_data(
                'binance_simple_earn_locked',
                positions,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return positions

        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching Simple Earn locked positions: {e}")
            return []
        except Exception as e:
            print(f"General Exception when fetching Simple Earn locked positions: {e}")
            return []

    def get_all_earn_positions(self):
        """
        Fetches all Simple Earn positions (both flexible and locked).

        :return: Dictionary with flexible and locked positions.
        """
        try:
            flexible_positions = self.get_simple_earn_flexible_positions()
            locked_positions = self.get_simple_earn_locked_positions()

            return {
                'flexible': flexible_positions,
                'locked': locked_positions
            }

        except Exception as e:
            print(f"Error fetching all Earn positions: {e}")
            return {
                'flexible': [],
                'locked': []
            }

    def get_futures_account_snapshot(self, start_date=None, end_date=None, limit=30):
        """
        Fetches daily account snapshot for Futures wallet.
        Uses cached data if available and not expired.

        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :param limit: Maximum number of records to return (default 30, max 30).
        :return: List of daily account snapshots.
        """
        # Prepare parameters for API call and cache key
        params = {"type": "FUTURES", "limit": limit}
        cache_params = {
            'type': 'FUTURES',
            'limit': limit,
            'start_date': start_date,
            'end_date': end_date
        }

        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")
        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('binance_futures_snapshot', cache_params)
        if cached_data is not None:
            print(f"Using cached futures account snapshot data")
            return cached_data

        # If not in cache, fetch from API
        try:
            snapshot = self.client.get_account_snapshot(**params)

            if snapshot.get('code') == 200:
                snapshot_data = snapshot.get('snapshotVos', [])

                # Cache the result
                self.storage.save_data(
                    'binance_futures_snapshot',
                    snapshot_data,
                    cache_params,
                    expiry=self.cache_expiry['snapshot']
                )

                return snapshot_data
            else:
                print(f"Error fetching futures account snapshot: {snapshot}")
                return []
        except (BinanceAPIException, BinanceRequestException) as e:
            print(f"Binance API Exception when fetching futures account snapshot: {e}")
        except Exception as e:
            print(f"General Exception when fetching futures account snapshot: {e}")

        return []