from typing import Any, Dict, List, Optional, Union
import json
import os
import time
import shutil
from datetime import datetime, timedelta
from .storage_interface import StorageInterface
import threading
import hashlib

class EnhancedFileStorage(StorageInterface):
    """
    Enhanced file-based storage with non-destructive updates, versioning, and backup mechanisms.
    Preserves historical data while allowing incremental updates.
    """

    def __init__(self, storage_dir: str = "data/crypto_cache"):
        """
        Initialize enhanced file storage.

        :param storage_dir: Directory to store files
        """
        self.storage_dir = storage_dir
        self.backup_dir = os.path.join(storage_dir, "backups")
        self.metadata_dir = os.path.join(storage_dir, "metadata")
        
        # Create directories
        os.makedirs(storage_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        # Thread lock for concurrent access
        self._lock = threading.RLock()

    def _get_file_path(self, key: str) -> str:
        """Get the file path for a key."""
        safe_key = "".join(c if c.isalnum() or c in "._-" else "_" for c in key)
        return os.path.join(self.storage_dir, f"{safe_key}.json")

    def _get_metadata_path(self, key: str) -> str:
        """Get the metadata file path for a key."""
        safe_key = "".join(c if c.isalnum() or c in "._-" else "_" for c in key)
        return os.path.join(self.metadata_dir, f"{safe_key}_meta.json")

    def _get_backup_path(self, key: str) -> str:
        """Get the backup file path for a key."""
        safe_key = "".join(c if c.isalnum() or c in "._-" else "_" for c in key)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(self.backup_dir, f"{safe_key}_{timestamp}.json")

    def _create_backup(self, key: str) -> bool:
        """Create a backup of existing data before updating."""
        try:
            file_path = self._get_file_path(key)
            if os.path.exists(file_path):
                backup_path = self._get_backup_path(key)
                shutil.copy2(file_path, backup_path)
                
                # Keep only the last 5 backups per key
                self._cleanup_old_backups(key)
                return True
            return False
        except Exception as e:
            print(f"Error creating backup for {key}: {e}")
            return False

    def _cleanup_old_backups(self, key: str, keep_count: int = 5):
        """Clean up old backup files, keeping only the most recent ones."""
        try:
            safe_key = "".join(c if c.isalnum() or c in "._-" else "_" for c in key)
            backup_files = []
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(f"{safe_key}_") and filename.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, filename)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups
            for file_path, _ in backup_files[keep_count:]:
                os.remove(file_path)
                
        except Exception as e:
            print(f"Error cleaning up backups for {key}: {e}")

    def _save_metadata(self, key: str, metadata: Dict[str, Any]) -> bool:
        """Save metadata for a key."""
        try:
            metadata_path = self._get_metadata_path(key)
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving metadata for {key}: {e}")
            return False

    def _load_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """Load metadata for a key."""
        try:
            metadata_path = self._get_metadata_path(key)
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"Error loading metadata for {key}: {e}")
            return None

    def _validate_data_integrity(self, file_path: str) -> bool:
        """Validate the integrity of a data file."""
        try:
            if not os.path.exists(file_path):
                return False
                
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            # Basic validation - ensure it's a valid storage object
            if not isinstance(data, dict):
                return False
                
            required_fields = ['data', 'timestamp']
            return all(field in data for field in required_fields)
            
        except Exception as e:
            print(f"Data integrity validation failed for {file_path}: {e}")
            return False

    def save(self, key: str, data: Any, expiry: Optional[int] = None) -> bool:
        """
        Save data with non-destructive updates and backup mechanisms.
        """
        with self._lock:
            try:
                file_path = self._get_file_path(key)
                
                # Create backup if file exists
                if os.path.exists(file_path):
                    self._create_backup(key)
                
                # Prepare storage object with enhanced metadata
                current_time = time.time()
                storage_obj = {
                    "data": data,
                    "timestamp": current_time,
                    "expiry": expiry,
                    "version": 1,
                    "checksum": self._calculate_checksum(data)
                }
                
                # Load existing metadata to increment version
                existing_metadata = self._load_metadata(key)
                if existing_metadata:
                    storage_obj["version"] = existing_metadata.get("version", 0) + 1
                
                # Write data atomically (write to temp file, then rename)
                temp_path = f"{file_path}.tmp"
                with open(temp_path, 'w') as f:
                    json.dump(storage_obj, f, indent=2)
                
                # Validate the written data
                if self._validate_data_integrity(temp_path):
                    os.rename(temp_path, file_path)
                    
                    # Save metadata
                    metadata = {
                        "key": key,
                        "last_updated": current_time,
                        "version": storage_obj["version"],
                        "size": os.path.getsize(file_path),
                        "checksum": storage_obj["checksum"]
                    }
                    self._save_metadata(key, metadata)
                    
                    return True
                else:
                    # Remove invalid temp file
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    return False
                    
            except Exception as e:
                print(f"Error saving data to enhanced storage: {e}")
                return False

    def _calculate_checksum(self, data: Any) -> str:
        """Calculate checksum for data integrity verification."""
        try:
            data_str = json.dumps(data, sort_keys=True)
            return hashlib.sha256(data_str.encode()).hexdigest()
        except Exception:
            return ""

    def load(self, key: str) -> Optional[Any]:
        """Load data with integrity validation."""
        with self._lock:
            try:
                file_path = self._get_file_path(key)
                
                if not os.path.exists(file_path):
                    return None
                
                # Validate data integrity first
                if not self._validate_data_integrity(file_path):
                    print(f"Data integrity check failed for {key}, attempting to restore from backup")
                    if self._restore_from_backup(key):
                        # Try loading again after restore
                        if not self._validate_data_integrity(file_path):
                            return None
                    else:
                        return None
                
                with open(file_path, 'r') as f:
                    storage_obj = json.load(f)
                
                # Check if data is expired
                if storage_obj.get("expiry") is not None:
                    timestamp = storage_obj.get("timestamp", 0)
                    expiry = storage_obj.get("expiry")
                    
                    if time.time() > timestamp + expiry:
                        return None  # Don't delete expired data, just return None
                
                # Verify checksum if available
                stored_checksum = storage_obj.get("checksum")
                if stored_checksum:
                    calculated_checksum = self._calculate_checksum(storage_obj.get("data"))
                    if stored_checksum != calculated_checksum:
                        print(f"Checksum mismatch for {key}, data may be corrupted")
                        return None
                
                return storage_obj.get("data")
                
            except Exception as e:
                print(f"Error loading data from enhanced storage: {e}")
                return None

    def _restore_from_backup(self, key: str) -> bool:
        """Restore data from the most recent backup."""
        try:
            safe_key = "".join(c if c.isalnum() or c in "._-" else "_" for c in key)
            backup_files = []
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(f"{safe_key}_") and filename.endswith(".json"):
                    file_path = os.path.join(self.backup_dir, filename)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            if not backup_files:
                return False
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Try to restore from the most recent valid backup
            for backup_path, _ in backup_files:
                if self._validate_data_integrity(backup_path):
                    file_path = self._get_file_path(key)
                    shutil.copy2(backup_path, file_path)
                    print(f"Successfully restored {key} from backup")
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error restoring from backup for {key}: {e}")
            return False

    def exists(self, key: str) -> bool:
        """Check if data exists and is not expired."""
        with self._lock:
            try:
                file_path = self._get_file_path(key)
                
                if not os.path.exists(file_path):
                    return False
                
                if not self._validate_data_integrity(file_path):
                    return False
                
                with open(file_path, 'r') as f:
                    storage_obj = json.load(f)
                
                # Check if data is expired
                if storage_obj.get("expiry") is not None:
                    timestamp = storage_obj.get("timestamp", 0)
                    expiry = storage_obj.get("expiry")
                    
                    if time.time() > timestamp + expiry:
                        return False
                
                return True
                
            except Exception as e:
                print(f"Error checking if data exists in enhanced storage: {e}")
                return False

    def delete(self, key: str) -> bool:
        """Delete data (moves to backup instead of permanent deletion)."""
        with self._lock:
            try:
                file_path = self._get_file_path(key)
                
                if os.path.exists(file_path):
                    # Create backup before deletion
                    self._create_backup(key)
                    os.remove(file_path)
                    
                    # Remove metadata
                    metadata_path = self._get_metadata_path(key)
                    if os.path.exists(metadata_path):
                        os.remove(metadata_path)
                    
                    return True
                
                return False
                
            except Exception as e:
                print(f"Error deleting data from enhanced storage: {e}")
                return False

    def list_keys(self, prefix: str = "") -> List[str]:
        """List all keys in storage, optionally filtered by prefix."""
        try:
            keys = []
            
            for filename in os.listdir(self.storage_dir):
                if filename.endswith(".json") and not filename.endswith(".tmp"):
                    key = filename[:-5]  # Remove .json extension
                    
                    if key.startswith(prefix):
                        keys.append(key)
            
            return keys
            
        except Exception as e:
            print(f"Error listing keys in enhanced storage: {e}")
            return []

    def clear(self) -> bool:
        """Clear all data (creates backups before clearing)."""
        with self._lock:
            try:
                # Create backups of all files before clearing
                for filename in os.listdir(self.storage_dir):
                    if filename.endswith(".json") and not filename.endswith(".tmp"):
                        key = filename[:-5]
                        self._create_backup(key)
                
                # Clear main storage
                for filename in os.listdir(self.storage_dir):
                    file_path = os.path.join(self.storage_dir, filename)
                    
                    if os.path.isfile(file_path) and filename.endswith(".json"):
                        os.remove(file_path)
                
                # Clear metadata
                for filename in os.listdir(self.metadata_dir):
                    file_path = os.path.join(self.metadata_dir, filename)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                
                return True
                
            except Exception as e:
                print(f"Error clearing enhanced storage: {e}")
                return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics and health information."""
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "backup_count": 0,
                "metadata_count": 0,
                "corrupted_files": 0,
                "oldest_file": None,
                "newest_file": None
            }
            
            file_times = []
            
            # Count main storage files
            for filename in os.listdir(self.storage_dir):
                if filename.endswith(".json") and not filename.endswith(".tmp"):
                    file_path = os.path.join(self.storage_dir, filename)
                    stats["total_files"] += 1
                    stats["total_size"] += os.path.getsize(file_path)
                    
                    mtime = os.path.getmtime(file_path)
                    file_times.append(mtime)
                    
                    # Check integrity
                    if not self._validate_data_integrity(file_path):
                        stats["corrupted_files"] += 1
            
            # Count backups
            if os.path.exists(self.backup_dir):
                stats["backup_count"] = len([f for f in os.listdir(self.backup_dir) if f.endswith(".json")])
            
            # Count metadata files
            if os.path.exists(self.metadata_dir):
                stats["metadata_count"] = len([f for f in os.listdir(self.metadata_dir) if f.endswith("_meta.json")])
            
            # File age statistics
            if file_times:
                stats["oldest_file"] = datetime.fromtimestamp(min(file_times)).isoformat()
                stats["newest_file"] = datetime.fromtimestamp(max(file_times)).isoformat()
            
            return stats
            
        except Exception as e:
            print(f"Error getting storage stats: {e}")
            return {}
