from typing import Any, Dict, List, Optional, Union
import json
import hashlib
import time
from datetime import datetime, timedelta
from .storage_interface import StorageInterface, StorageFactory
from .enhanced_storage import EnhancedFileStorage

class CryptoStorage:
    """
    Storage manager for cryptocurrency data.
    Provides methods to store and retrieve data with appropriate caching.
    """

    def __init__(self, storage: Optional[StorageInterface] = None, use_enhanced: bool = True):
        """
        Initialize crypto storage.

        :param storage: Storage implementation to use
        :param use_enhanced: Whether to use enhanced storage with non-destructive updates
        """
        if storage:
            self.storage = storage
        elif use_enhanced:
            self.storage = EnhancedFileStorage(storage_dir="data/crypto_cache")
        else:
            self.storage = StorageFactory.get_storage("file", storage_dir="data/crypto_cache")

    def _generate_key(self, category: str, params: Dict[str, Any]) -> str:
        """
        Generate a unique key for the data based on category and parameters.

        :param category: Data category (e.g., 'binance_spot', 'pionex_trades')
        :param params: Parameters used to fetch the data
        :return: Unique key
        """
        # Convert params to a sorted string representation for consistent hashing
        param_str = json.dumps(params, sort_keys=True)

        # Create a hash of the parameters
        param_hash = hashlib.md5(param_str.encode()).hexdigest()

        # Combine category and hash
        return f"{category}_{param_hash}"

    def save_data(self, category: str, data: Any, params: Dict[str, Any], expiry: Optional[int] = None) -> bool:
        """
        Save data to storage with appropriate key.

        :param category: Data category
        :param data: Data to store
        :param params: Parameters used to fetch the data
        :param expiry: Optional expiry time in seconds
        :return: True if successful, False otherwise
        """
        key = self._generate_key(category, params)
        return self.storage.save(key, data, expiry)

    def load_data(self, category: str, params: Dict[str, Any]) -> Optional[Any]:
        """
        Load data from storage.

        :param category: Data category
        :param params: Parameters used to fetch the data
        :return: Stored data or None if not found or expired
        """
        key = self._generate_key(category, params)
        return self.storage.load(key)

    def data_exists(self, category: str, params: Dict[str, Any]) -> bool:
        """
        Check if data exists in storage and is not expired.

        :param category: Data category
        :param params: Parameters used to fetch the data
        :return: True if data exists and is not expired, False otherwise
        """
        key = self._generate_key(category, params)
        return self.storage.exists(key)

    def delete_data(self, category: str, params: Dict[str, Any]) -> bool:
        """
        Delete data from storage.

        :param category: Data category
        :param params: Parameters used to fetch the data
        :return: True if successful, False otherwise
        """
        key = self._generate_key(category, params)
        return self.storage.delete(key)

    def clear_category(self, category: str) -> bool:
        """
        Clear all data for a specific category.

        :param category: Data category
        :return: True if successful, False otherwise
        """
        keys = self.storage.list_keys(prefix=f"{category}_")

        success = True
        for key in keys:
            if not self.storage.delete(key):
                success = False

        return success

    def clear_all(self) -> bool:
        """
        Clear all data from storage.

        :return: True if successful, False otherwise
        """
        return self.storage.clear()

    def get_categories(self) -> List[str]:
        """
        Get list of all categories in storage.

        :return: List of categories
        """
        keys = self.storage.list_keys()
        categories = set()

        for key in keys:
            if "_" in key:
                category = key.split("_")[0]
                categories.add(category)

        return list(categories)

    def save_incremental_data(self, category: str, new_data: Any, params: Dict[str, Any],
                            merge_strategy: str = "append", expiry: Optional[int] = None) -> bool:
        """
        Save data incrementally, preserving existing data.

        :param category: Data category
        :param new_data: New data to add
        :param params: Parameters used to fetch the data
        :param merge_strategy: How to merge with existing data ("append", "update", "merge")
        :param expiry: Optional expiry time in seconds
        :return: True if successful, False otherwise
        """
        try:
            # Load existing data
            existing_data = self.load_data(category, params)

            if existing_data is None:
                # No existing data, save new data directly
                return self.save_data(category, new_data, params, expiry)

            # Merge data based on strategy
            merged_data = self._merge_data(existing_data, new_data, merge_strategy)

            # Save merged data
            return self.save_data(category, merged_data, params, expiry)

        except Exception as e:
            print(f"Error saving incremental data for {category}: {e}")
            return False

    def _merge_data(self, existing_data: Any, new_data: Any, strategy: str) -> Any:
        """
        Merge existing data with new data based on the specified strategy.

        :param existing_data: Existing data
        :param new_data: New data to merge
        :param strategy: Merge strategy
        :return: Merged data
        """
        if strategy == "append":
            # Append new data to existing data (for lists)
            if isinstance(existing_data, list) and isinstance(new_data, list):
                # Remove duplicates based on common identifier fields
                merged = existing_data.copy()
                existing_ids = set()

                # Extract IDs from existing data
                for item in existing_data:
                    if isinstance(item, dict):
                        # Try common ID fields
                        for id_field in ['id', 'orderId', 'symbol', 'timestamp', 'time']:
                            if id_field in item:
                                existing_ids.add(item[id_field])
                                break

                # Add new items that don't exist
                for item in new_data:
                    if isinstance(item, dict):
                        item_id = None
                        for id_field in ['id', 'orderId', 'symbol', 'timestamp', 'time']:
                            if id_field in item:
                                item_id = item[id_field]
                                break

                        if item_id is None or item_id not in existing_ids:
                            merged.append(item)

                return merged
            else:
                # For non-list data, replace with new data
                return new_data

        elif strategy == "update":
            # Update existing data with new data (for dictionaries)
            if isinstance(existing_data, dict) and isinstance(new_data, dict):
                merged = existing_data.copy()
                merged.update(new_data)
                return merged
            else:
                return new_data

        elif strategy == "merge":
            # Deep merge for complex structures
            if isinstance(existing_data, dict) and isinstance(new_data, dict):
                return self._deep_merge_dict(existing_data, new_data)
            elif isinstance(existing_data, list) and isinstance(new_data, list):
                return existing_data + new_data
            else:
                return new_data

        else:
            # Default: replace with new data
            return new_data

    def _deep_merge_dict(self, dict1: Dict, dict2: Dict) -> Dict:
        """
        Deep merge two dictionaries.

        :param dict1: First dictionary
        :param dict2: Second dictionary
        :return: Merged dictionary
        """
        result = dict1.copy()

        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_dict(result[key], value)
            else:
                result[key] = value

        return result

    def is_data_fresh(self, category: str, params: Dict[str, Any],
                     max_age_seconds: int) -> bool:
        """
        Check if cached data is fresh enough based on age.

        :param category: Data category
        :param params: Parameters used to fetch the data
        :param max_age_seconds: Maximum age in seconds for data to be considered fresh
        :return: True if data is fresh, False otherwise
        """
        try:
            key = self._generate_key(category, params)

            # Check if using enhanced storage
            if hasattr(self.storage, '_load_metadata'):
                metadata = self.storage._load_metadata(key)
                if metadata:
                    last_updated = metadata.get('last_updated', 0)
                    return (time.time() - last_updated) < max_age_seconds

            # Fallback: check if data exists and is not expired
            return self.data_exists(category, params)

        except Exception as e:
            print(f"Error checking data freshness for {category}: {e}")
            return False

    def get_data_age(self, category: str, params: Dict[str, Any]) -> Optional[int]:
        """
        Get the age of cached data in seconds.

        :param category: Data category
        :param params: Parameters used to fetch the data
        :return: Age in seconds, or None if data doesn't exist
        """
        try:
            key = self._generate_key(category, params)

            # Check if using enhanced storage
            if hasattr(self.storage, '_load_metadata'):
                metadata = self.storage._load_metadata(key)
                if metadata:
                    last_updated = metadata.get('last_updated', 0)
                    return int(time.time() - last_updated)

            # Fallback: try to get timestamp from data
            data = self.load_data(category, params)
            if data and isinstance(data, dict) and 'timestamp' in data:
                return int(time.time() - data['timestamp'])

            return None

        except Exception as e:
            print(f"Error getting data age for {category}: {e}")
            return None

    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics and health information.

        :return: Dictionary with storage statistics
        """
        try:
            # Check if using enhanced storage
            if hasattr(self.storage, 'get_storage_stats'):
                return self.storage.get_storage_stats()

            # Fallback: basic stats
            categories = self.get_categories()
            return {
                "total_categories": len(categories),
                "categories": categories,
                "storage_type": type(self.storage).__name__
            }

        except Exception as e:
            print(f"Error getting storage stats: {e}")
            return {}

    def cleanup_expired_data(self) -> int:
        """
        Clean up expired data entries.

        :return: Number of entries cleaned up
        """
        try:
            cleaned_count = 0
            keys = self.storage.list_keys()

            for key in keys:
                # Try to load data - this will automatically clean up expired entries
                if not self.storage.exists(key):
                    cleaned_count += 1

            return cleaned_count

        except Exception as e:
            print(f"Error cleaning up expired data: {e}")
            return 0
