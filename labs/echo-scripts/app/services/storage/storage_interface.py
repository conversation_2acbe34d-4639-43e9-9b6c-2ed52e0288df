from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import json
import os
import time
from datetime import datetime, timedelta

class StorageInterface(ABC):
    """
    Abstract interface for data storage operations.
    Implementations can use different storage backends (file, database, etc.)
    while maintaining the same interface.
    """

    @abstractmethod
    def save(self, key: str, data: Any, expiry: Optional[int] = None) -> bool:
        """
        Save data to storage.

        :param key: Unique identifier for the data
        :param data: Data to store (must be JSON serializable)
        :param expiry: Optional expiry time in seconds
        :return: True if successful, False otherwise
        """
        pass

    @abstractmethod
    def load(self, key: str) -> Optional[Any]:
        """
        Load data from storage.

        :param key: Unique identifier for the data
        :return: Stored data or None if not found or expired
        """
        pass

    @abstractmethod
    def exists(self, key: str) -> bool:
        """
        Check if data exists in storage and is not expired.

        :param key: Unique identifier for the data
        :return: True if data exists and is not expired, False otherwise
        """
        pass

    @abstractmethod
    def delete(self, key: str) -> bool:
        """
        Delete data from storage.

        :param key: Unique identifier for the data
        :return: True if successful, False otherwise
        """
        pass

    @abstractmethod
    def list_keys(self, prefix: str = "") -> List[str]:
        """
        List all keys in storage, optionally filtered by prefix.

        :param prefix: Optional prefix to filter keys
        :return: List of keys
        """
        pass

    @abstractmethod
    def clear(self) -> bool:
        """
        Clear all data from storage.

        :return: True if successful, False otherwise
        """
        pass


class FileStorage(StorageInterface):
    """
    File-based implementation of StorageInterface.
    Stores data in JSON files within a specified directory.

    Note: The cache files are included in the repository to maintain a history of the data.
    This allows tracking changes in portfolio data over time.
    """

    def __init__(self, storage_dir: str = "data/cache"):
        """
        Initialize file storage.

        :param storage_dir: Directory to store files
        """
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)

    def _get_file_path(self, key: str) -> str:
        """
        Get the file path for a key.

        :param key: Storage key
        :return: File path
        """
        # Sanitize key to be a valid filename
        safe_key = "".join(c if c.isalnum() or c in "._- " else "_" for c in key)
        return os.path.join(self.storage_dir, f"{safe_key}.json")

    def save(self, key: str, data: Any, expiry: Optional[int] = None) -> bool:
        """
        Save data to a JSON file.

        :param key: Unique identifier for the data
        :param data: Data to store (must be JSON serializable)
        :param expiry: Optional expiry time in seconds
        :return: True if successful, False otherwise
        """
        try:
            file_path = self._get_file_path(key)

            # Prepare storage object with metadata
            storage_obj = {
                "data": data,
                "timestamp": time.time(),
                "expiry": expiry
            }

            with open(file_path, 'w') as f:
                json.dump(storage_obj, f)

            return True
        except Exception as e:
            print(f"Error saving data to file storage: {e}")
            return False

    def load(self, key: str) -> Optional[Any]:
        """
        Load data from a JSON file.

        :param key: Unique identifier for the data
        :return: Stored data or None if not found or expired
        """
        try:
            file_path = self._get_file_path(key)

            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r') as f:
                storage_obj = json.load(f)

            # Check if data is expired
            if storage_obj.get("expiry") is not None:
                timestamp = storage_obj.get("timestamp", 0)
                expiry = storage_obj.get("expiry")

                if time.time() > timestamp + expiry:
                    # Data is expired, delete the file
                    os.remove(file_path)
                    return None

            return storage_obj.get("data")
        except Exception as e:
            print(f"Error loading data from file storage: {e}")
            return None

    def exists(self, key: str) -> bool:
        """
        Check if data exists in storage and is not expired.

        :param key: Unique identifier for the data
        :return: True if data exists and is not expired, False otherwise
        """
        try:
            file_path = self._get_file_path(key)

            if not os.path.exists(file_path):
                return False

            with open(file_path, 'r') as f:
                storage_obj = json.load(f)

            # Check if data is expired
            if storage_obj.get("expiry") is not None:
                timestamp = storage_obj.get("timestamp", 0)
                expiry = storage_obj.get("expiry")

                if time.time() > timestamp + expiry:
                    # Data is expired
                    return False

            return True
        except Exception as e:
            print(f"Error checking if data exists in file storage: {e}")
            return False

    def delete(self, key: str) -> bool:
        """
        Delete data from storage.

        :param key: Unique identifier for the data
        :return: True if successful, False otherwise
        """
        try:
            file_path = self._get_file_path(key)

            if os.path.exists(file_path):
                os.remove(file_path)
                return True

            return False
        except Exception as e:
            print(f"Error deleting data from file storage: {e}")
            return False

    def list_keys(self, prefix: str = "") -> List[str]:
        """
        List all keys in storage, optionally filtered by prefix.

        :param prefix: Optional prefix to filter keys
        :return: List of keys
        """
        try:
            keys = []

            for filename in os.listdir(self.storage_dir):
                if filename.endswith(".json"):
                    key = filename[:-5]  # Remove .json extension

                    if key.startswith(prefix):
                        keys.append(key)

            return keys
        except Exception as e:
            print(f"Error listing keys in file storage: {e}")
            return []

    def clear(self) -> bool:
        """
        Clear all data from storage.

        :return: True if successful, False otherwise
        """
        try:
            for filename in os.listdir(self.storage_dir):
                file_path = os.path.join(self.storage_dir, filename)

                if os.path.isfile(file_path) and filename.endswith(".json"):
                    os.remove(file_path)

            return True
        except Exception as e:
            print(f"Error clearing file storage: {e}")
            return False


class StorageFactory:
    """
    Factory class to create storage instances.
    """

    @staticmethod
    def get_storage(storage_type: str = "file", **kwargs) -> StorageInterface:
        """
        Get a storage instance of the specified type.

        :param storage_type: Type of storage ('file', 'memory', etc.)
        :param kwargs: Additional arguments for the storage constructor
        :return: Storage instance
        """
        if storage_type == "file":
            return FileStorage(**kwargs)
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")
