import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
from services.binance_service import BinanceService
from services.pionex_service import PionexService
from services.storage import CryptoStorage
from utils.concurrent_utils import execute_concurrently
from utils.symbol_validator import symbol_validator
import asyncio

class PortfolioService:
    """
    Service to aggregate and analyze portfolio data across multiple platforms.
    Uses a storage system to cache aggregated data and reduce API calls.
    """
    def __init__(self):
        self.binance_service = BinanceService()
        self.pionex_service = PionexService()
        self.storage = CryptoStorage()
        # Add more exchange services as needed

        # Default cache expiry times (in seconds)
        self.cache_expiry = {
            'default': 300,  # 5 minutes
            'portfolio': 300,  # 5 minutes
            'historical': 1800,  # 30 minutes
            'allocation': 300,  # 5 minutes
            'transactions': 600,  # 10 minutes
            'roi': 300,  # 5 minutes
            'bot_history': 1800,  # 30 minutes
            'bot_performance': 600  # 10 minutes
        }

    def get_total_portfolio(self, start_date=None, end_date=None):
        """
        Get aggregated portfolio data across all platforms.
        Uses cached data if available and not expired.

        :param start_date: Start date in 'YYYY-MM-DD' format.
        :param end_date: End date in 'YYYY-MM-DD' format.
        :return: DataFrame with portfolio data.
        """
        # Prepare cache key
        cache_params = {
            'start_date': start_date,
            'end_date': end_date
        }

        # Check if data exists in cache
        cached_data = self.storage.load_data('portfolio_total', cache_params)

        if cached_data is not None:
            print(f"Using cached total portfolio data")
            # Convert cached data back to DataFrame
            return pd.DataFrame(cached_data)

        # If not in cache, fetch and aggregate data
        # Get data from each platform
        binance_spot = self._get_binance_spot_holdings()
        binance_futures = self._get_binance_futures_holdings()
        binance_earn = self._get_binance_earn_holdings()
        pionex_holdings = self._get_pionex_holdings()

        # Combine all holdings
        all_holdings = pd.concat([binance_spot, binance_futures, binance_earn, pionex_holdings], ignore_index=True)

        if all_holdings.empty:
            # Return empty DataFrame with expected columns
            return pd.DataFrame(columns=['asset', 'quantity', 'value_usd', 'cost_basis', 'platform', 'roi'])

        # Group by asset and calculate totals
        portfolio = all_holdings.groupby('asset').agg({
            'quantity': 'sum',
            'value_usd': 'sum',
            'cost_basis': 'sum',
            'platform': lambda x: ', '.join(set(x))
        }).reset_index()

        # Calculate ROI
        portfolio['roi'] = portfolio.apply(
            lambda row: (row['value_usd'] - row['cost_basis']) / row['cost_basis'] * 100 if row['cost_basis'] > 0 else 0,
            axis=1
        )

        # Cache the result
        self.storage.save_data(
            'portfolio_total',
            portfolio.to_dict('records'),
            cache_params,
            expiry=self.cache_expiry['portfolio']
        )

        return portfolio

    def _get_binance_earn_holdings(self):
        """
        Get current Binance Earn holdings (Simple Earn flexible and locked products).

        :return: DataFrame with Binance Earn holdings.
        """
        try:
            # Get all Earn positions
            earn_positions = self.binance_service.get_all_earn_positions()

            holdings = []

            # Process flexible positions
            flexible_positions = earn_positions.get('flexible', [])
            if isinstance(flexible_positions, list):
                for position in flexible_positions:
                    if isinstance(position, dict):
                        asset = position.get('asset', '')
                        amount_str = position.get('totalAmount', '0')

                        try:
                            amount = float(amount_str) if amount_str else 0
                        except (ValueError, TypeError):
                            amount = 0

                        if amount > 0:
                            # Get current price
                            price = self._get_safe_price(asset)

                            holdings.append({
                                'asset': asset,
                                'quantity': amount,
                                'value_usd': amount * price,
                                'platform': 'Binance Earn (Flexible)',
                                'earn_type': 'Flexible',
                                'apy': position.get('annualPercentageRate', 0)
                            })

            # Process locked positions
            locked_positions = earn_positions.get('locked', [])
            if isinstance(locked_positions, list):
                for position in locked_positions:
                    if isinstance(position, dict):
                        asset = position.get('asset', '')
                        amount_str = position.get('amount', '0')

                        try:
                            amount = float(amount_str) if amount_str else 0
                        except (ValueError, TypeError):
                            amount = 0

                        if amount > 0:
                            # Get current price
                            price = self._get_safe_price(asset)

                            holdings.append({
                                'asset': asset,
                                'quantity': amount,
                                'value_usd': amount * price,
                                'platform': 'Binance Earn (Locked)',
                                'earn_type': 'Locked',
                                'apy': position.get('annualPercentageRate', 0),
                                'duration': position.get('duration', 0),
                                'lot_size': position.get('lotSize', 0)
                            })

            if not holdings:
                return pd.DataFrame()

            return pd.DataFrame(holdings)

        except Exception as e:
            print(f"Error getting Binance Earn holdings: {e}")
            return pd.DataFrame()

    def _get_safe_price(self, asset: str) -> float:
        """
        Safely get price for an asset with proper validation and fallback handling.

        :param asset: Asset symbol (e.g., 'BTC', 'ETH')
        :return: Price in USD or 0 if not available
        """
        # Handle stablecoins
        if asset in ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD']:
            return 1.0

        # Try different trading pairs in order of preference
        symbol_candidates = [f"{asset}USDT", f"{asset}USDC", f"{asset}BUSD"]

        for symbol in symbol_candidates:
            # Validate symbol before attempting to fetch
            if not symbol_validator.is_valid_symbol(symbol):
                continue

            try:
                price = self.binance_service.get_current_price(symbol)
                if price is not None and price > 0:
                    return price
            except Exception as e:
                # Log error only once per symbol per session
                symbol_validator.log_error_once(symbol, f"Price fetch error: {str(e)}")
                continue

        # If all attempts failed, log and return 0
        symbol_validator.log_error_once(asset, f"No valid price found for asset {asset}")
        return 0.0

    def _get_binance_spot_holdings(self):
        """
        Get current Binance spot holdings with cost basis.

        :return: DataFrame with Binance spot holdings.
        """
        try:
            # Get account snapshot
            snapshot = self.binance_service.get_spot_account_snapshot()

            if not snapshot:
                return pd.DataFrame()

            # Extract balances
            balances = []
            for item in snapshot:
                update_time = datetime.fromtimestamp(item.get('updateTime', 0) / 1000)
                for balance in item.get('data', {}).get('balances', []):
                    asset = balance.get('asset')
                    free = float(balance.get('free', 0))
                    locked = float(balance.get('locked', 0))
                    total = free + locked

                    if total > 0:
                        # Get current price using safe price fetching
                        price = self._get_safe_price(asset)

                        balances.append({
                            'asset': asset,
                            'quantity': total,
                            'value_usd': total * price,
                            'platform': 'Binance Spot',
                            'last_updated': update_time
                        })

            if not balances:
                return pd.DataFrame()

            # Create DataFrame
            df_balances = pd.DataFrame(balances)

            # Get deposit and withdrawal history to calculate cost basis
            deposits = self.binance_service.get_spot_deposit_history()
            withdrawals = self.binance_service.get_spot_withdraw_history()

            # Calculate cost basis
            df_balances = self._calculate_binance_spot_cost_basis(df_balances, deposits, withdrawals)

            return df_balances
        except Exception as e:
            print(f"Error getting Binance spot holdings: {e}")
            return pd.DataFrame()

    def _get_binance_futures_holdings(self):
        """
        Get current Binance futures holdings with cost basis.
        Shows total futures balance by margin asset (e.g., USDC) instead of individual positions.

        :return: DataFrame with Binance futures holdings.
        """
        try:
            # Get futures account information to get total balances by margin asset
            account_info = self.binance_service.client.futures_account()

            if not account_info:
                return pd.DataFrame()

            # Extract assets with non-zero balances
            holdings = []
            assets = account_info.get('assets', [])

            for asset_info in assets:
                asset = asset_info.get('asset', '')
                wallet_balance = float(asset_info.get('walletBalance', 0))
                unrealized_pnl = float(asset_info.get('unrealizedPnL', 0))
                margin_balance = float(asset_info.get('marginBalance', 0))

                # Only include assets with significant margin balance
                if margin_balance > 1.0:  # More than $1
                    # Use margin balance as the total value (includes unrealized PnL)
                    value_usd = margin_balance
                    cost_basis = wallet_balance  # Original investment

                    holdings.append({
                        'asset': asset,
                        'quantity': margin_balance,  # Use margin balance as quantity for display
                        'value_usd': value_usd,
                        'cost_basis': cost_basis,
                        'platform': 'Binance USD-M Futures',
                        'unrealized_pnl': unrealized_pnl,
                        'wallet_balance': wallet_balance,
                        'margin_balance': margin_balance
                    })

            if not holdings:
                return pd.DataFrame()

            return pd.DataFrame(holdings)
        except Exception as e:
            print(f"Error getting Binance futures holdings: {e}")
            # Fallback to individual positions if account info fails
            try:
                positions = self.binance_service.get_live_positions()
                if not positions:
                    return pd.DataFrame()

                # Group by margin asset
                margin_assets = {}
                for position in positions:
                    margin_asset = position.get('marginAsset', 'USDT')
                    position_amt = float(position.get('positionAmt', 0))
                    mark_price = float(position.get('markPrice', 0))
                    entry_price = float(position.get('entryPrice', 0))

                    if position_amt != 0:
                        abs_position = abs(position_amt)
                        value_usd = abs_position * mark_price
                        cost_basis = abs_position * entry_price

                        if margin_asset not in margin_assets:
                            margin_assets[margin_asset] = {
                                'total_value': 0,
                                'total_cost': 0,
                                'total_quantity': 0
                            }

                        margin_assets[margin_asset]['total_value'] += value_usd
                        margin_assets[margin_asset]['total_cost'] += cost_basis
                        margin_assets[margin_asset]['total_quantity'] += abs_position

                # Create holdings from grouped data
                holdings = []
                for asset, data in margin_assets.items():
                    holdings.append({
                        'asset': asset,
                        'quantity': data['total_quantity'],
                        'value_usd': data['total_value'],
                        'cost_basis': data['total_cost'],
                        'platform': 'Binance USD-M Futures'
                    })

                return pd.DataFrame(holdings) if holdings else pd.DataFrame()
            except Exception as fallback_error:
                print(f"Fallback futures holdings also failed: {fallback_error}")
                return pd.DataFrame()

    def _create_sample_pionex_holdings(self):
        """
        Create sample Pionex holdings for demonstration purposes.

        :return: DataFrame with sample Pionex holdings.
        """
        try:
            # Create sample holdings
            sample_holdings = [
                {
                    'asset': 'BTC',
                    'quantity': 0.5,
                    'value_usd': 30000.0,  # Assuming BTC price of $60,000
                    'cost_basis': 25000.0,  # Assuming 20% profit
                    'platform': 'Pionex'
                },
                {
                    'asset': 'ETH',
                    'quantity': 5.0,
                    'value_usd': 17500.0,  # Assuming ETH price of $3,500
                    'cost_basis': 15000.0,  # Assuming ~17% profit
                    'platform': 'Pionex'
                },
                {
                    'asset': 'BNB',
                    'quantity': 20.0,
                    'value_usd': 10000.0,  # Assuming BNB price of $500
                    'cost_basis': 8000.0,  # Assuming 25% profit
                    'platform': 'Pionex'
                },
                {
                    'asset': 'USDT',
                    'quantity': 5000.0,
                    'value_usd': 5000.0,  # USDT is pegged to USD
                    'cost_basis': 5000.0,  # No profit/loss for stablecoins
                    'platform': 'Pionex'
                }
            ]

            # Create DataFrame
            return pd.DataFrame(sample_holdings)
        except Exception as e:
            print(f"Error creating sample Pionex holdings: {e}")
            return pd.DataFrame()

    def _get_pionex_holdings(self):
        """
        Get current Pionex holdings with cost basis.

        :return: DataFrame with Pionex holdings.
        """
        try:
            # Get enhanced account balances that include bot/earn account estimates
            # This addresses the Pionex API limitation where only trading account balances are returned
            balances = self.pionex_service.get_enhanced_account_balances()

            # Check if balances are valid
            if not balances or not isinstance(balances, list):
                print("No valid Pionex balances available from API")
                return pd.DataFrame()

            # Process balances
            holdings = []
            for balance in balances:
                try:
                    # Check if balance is a dictionary
                    if not isinstance(balance, dict):
                        print(f"Skipping invalid balance: {balance}")
                        continue

                    # Get asset and amounts (Pionex API structure: {'coin': 'BTC', 'free': '0.***********', 'frozen': '0'})
                    asset = balance.get('coin')
                    if not asset:  # Skip if no asset name
                        continue

                    # Convert amounts to float safely
                    try:
                        free = float(balance.get('free', 0))
                    except (ValueError, TypeError):
                        free = 0

                    try:
                        frozen = float(balance.get('frozen', 0))
                    except (ValueError, TypeError):
                        frozen = 0

                    total = free + frozen

                    if total > 0:
                        try:
                            # Get current price using safe price fetching
                            price = self._get_safe_price(asset)

                            holdings.append({
                                'asset': asset,
                                'quantity': total,
                                'free': free,
                                'frozen': frozen,
                                'value_usd': total * price,
                                'platform': 'Pionex'
                            })
                        except Exception as e:
                            symbol_validator.log_error_once(asset, f"Error processing Pionex balance for {asset}: {e}")
                except Exception as e:
                    print(f"Error processing Pionex balance: {e}")

            if not holdings:
                print("No Pionex holdings with non-zero balances")
                return pd.DataFrame()

            # Create DataFrame
            df_holdings = pd.DataFrame(holdings)

            # For cost basis, we'll use a simple estimation since we don't have trade history
            # Estimate cost basis as 80% of current value (conservative estimate)
            df_holdings['cost_basis'] = df_holdings['value_usd'] * 0.8

            return df_holdings
        except Exception as e:
            print(f"Error getting Pionex holdings: {e}")
            return pd.DataFrame()

    def _calculate_binance_spot_cost_basis(self, holdings_df, deposits, withdrawals):
        """
        Calculate cost basis for Binance spot holdings based on deposit and withdrawal history.

        :param holdings_df: DataFrame with holdings.
        :param deposits: List of deposit transactions.
        :param withdrawals: List of withdrawal transactions.
        :return: DataFrame with cost basis added.
        """
        if holdings_df.empty:
            return holdings_df

        # Initialize cost basis column
        holdings_df['cost_basis'] = 0.0

        # Process deposits to calculate cost basis
        for asset in holdings_df['asset'].unique():
            asset_holdings = holdings_df[holdings_df['asset'] == asset]

            # Filter deposits for this asset
            asset_deposits = [d for d in deposits if d.get('coin') == asset]

            # Calculate total deposit amount and value
            total_deposit_amount = sum(float(d.get('amount', 0)) for d in asset_deposits)

            if total_deposit_amount > 0:
                # Simple cost basis calculation
                current_amount = asset_holdings['quantity'].sum()

                # Adjust for withdrawals
                asset_withdrawals = [w for w in withdrawals if w.get('coin') == asset]
                total_withdrawal_amount = sum(float(w.get('amount', 0)) for w in asset_withdrawals)

                # Calculate cost basis based on proportion of current holdings to total deposits
                if total_deposit_amount > total_withdrawal_amount:
                    # If we still have some of the deposited assets
                    remaining_ratio = min(1.0, current_amount / (total_deposit_amount - total_withdrawal_amount))

                    # Estimate cost basis based on current value and a reasonable ROI assumption
                    # This is a simplified approach - in a real system, you'd track actual purchase prices
                    estimated_roi = 0.0  # Assume 0% ROI as a conservative estimate
                    estimated_cost_basis = asset_holdings['value_usd'].sum() / (1 + estimated_roi)

                    # Update cost basis in the DataFrame
                    holdings_df.loc[holdings_df['asset'] == asset, 'cost_basis'] = estimated_cost_basis
                else:
                    # If withdrawals exceed deposits, use current value as cost basis
                    holdings_df.loc[holdings_df['asset'] == asset, 'cost_basis'] = asset_holdings['value_usd'].sum()
            else:
                # If no deposit history, use current value as cost basis
                holdings_df.loc[holdings_df['asset'] == asset, 'cost_basis'] = asset_holdings['value_usd'].sum()

        return holdings_df

    def _calculate_pionex_cost_basis(self, holdings_df, trades):
        """
        Calculate cost basis for Pionex holdings based on trade history.

        :param holdings_df: DataFrame with holdings.
        :param trades: List of trade transactions.
        :return: DataFrame with cost basis added.
        """
        try:
            if holdings_df.empty:
                return holdings_df

            # Check if trades is valid
            if not isinstance(trades, list):
                print(f"Invalid trades data: {type(trades)}")
                # Fallback: estimate cost basis as 80% of current value
                holdings_df['cost_basis'] = holdings_df['value_usd'] * 0.8
                return holdings_df

            # Initialize cost basis column
            holdings_df['cost_basis'] = 0.0

            # Process trades to calculate cost basis
            for asset in holdings_df['asset'].unique():
                try:
                    asset_holdings = holdings_df[holdings_df['asset'] == asset]

                    # We're only dealing with spot wallet balances now

                    # Filter trades for this asset
                    asset_trades = []
                    for t in trades:
                        try:
                            if isinstance(t, dict) and asset in t.get('symbol', ''):
                                asset_trades.append(t)
                        except Exception as e:
                            print(f"Error filtering trade for {asset}: {e}")

                    if asset_trades:
                        # Calculate average purchase price
                        buy_trades = []
                        for t in asset_trades:
                            try:
                                if t.get('side') == 'BUY':
                                    buy_trades.append(t)
                            except Exception as e:
                                print(f"Error filtering buy trade: {e}")

                        if buy_trades:
                            try:
                                total_quantity = 0
                                total_cost = 0

                                for t in buy_trades:
                                    try:
                                        size = float(t.get('size', 0))
                                        price = float(t.get('price', 0))
                                        total_quantity += size
                                        total_cost += price * size
                                    except (ValueError, TypeError) as e:
                                        print(f"Error calculating trade cost: {e}")

                                if total_quantity > 0:
                                    avg_price = total_cost / total_quantity

                                    # Calculate cost basis
                                    current_quantity = asset_holdings['quantity'].sum()
                                    cost_basis = current_quantity * avg_price

                                    # Update cost basis in the DataFrame for non-grid bot holdings
                                    holdings_df.loc[(holdings_df['asset'] == asset) & (holdings_df['platform'] == 'Pionex'), 'cost_basis'] = cost_basis
                                else:
                                    # If no buy trades with quantity, use current value
                                    holdings_df.loc[(holdings_df['asset'] == asset) & (holdings_df['platform'] == 'Pionex'), 'cost_basis'] = asset_holdings['value_usd'].sum() * 0.8
                            except Exception as e:
                                print(f"Error calculating cost basis for {asset}: {e}")
                                # Fallback to estimated cost basis
                                holdings_df.loc[(holdings_df['asset'] == asset) & (holdings_df['platform'] == 'Pionex'), 'cost_basis'] = asset_holdings['value_usd'].sum() * 0.8
                        else:
                            # If no buy trades, use current value
                            holdings_df.loc[(holdings_df['asset'] == asset) & (holdings_df['platform'] == 'Pionex'), 'cost_basis'] = asset_holdings['value_usd'].sum() * 0.8
                    else:
                        # If no trade history, use current value
                        holdings_df.loc[(holdings_df['asset'] == asset) & (holdings_df['platform'] == 'Pionex'), 'cost_basis'] = asset_holdings['value_usd'].sum() * 0.8
                except Exception as e:
                    print(f"Error processing asset {asset}: {e}")
                    # Fallback to estimated cost basis
                    holdings_df.loc[holdings_df['asset'] == asset, 'cost_basis'] = holdings_df.loc[holdings_df['asset'] == asset, 'value_usd'] * 0.8

            return holdings_df
        except Exception as e:
            print(f"Error calculating Pionex cost basis: {e}")
            # Fallback: estimate cost basis as 80% of current value
            holdings_df['cost_basis'] = holdings_df['value_usd'] * 0.8
            return holdings_df

    def get_active_operations(self):
        """
        Get active operations from all platforms.
        Uses cached data if available and not expired.

        :return: Dictionary with active operations from all platforms.
        """
        try:
            # Prepare cache key
            cache_params = {}

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_active_operations', cache_params)

            if cached_data is not None:
                print(f"Using cached active operations data")
                return cached_data

            # If not in cache, fetch and process the data
            # Get Pionex active operations
            pionex_operations = self.pionex_service.get_active_operations()

            # Get detailed spot balances from Pionex
            pionex_spot_balances = self.pionex_service.get_detailed_spot_balances()

            # Get Binance active orders (if available)
            binance_orders = []
            # Note: Binance active orders implementation would go here

            # Combine all active operations
            result = {
                "pionex": {
                    "grid_bots": pionex_operations.get("grid_bots", []),
                    "active_orders": pionex_operations.get("active_orders", []),
                    "spot_balances": pionex_spot_balances
                },
                "binance": {
                    "active_orders": binance_orders
                }
            }

            # Cache the result
            self.storage.save_data(
                'portfolio_active_operations',
                result,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return result
        except Exception as e:
            print(f"Error getting active operations: {e}")
            return {
                "pionex": {
                    "grid_bots": [],
                    "active_orders": [],
                    "spot_balances": []
                },
                "binance": {
                    "active_orders": []
                }
            }

    def get_bot_history(self, bot_id=None, days=30):
        """
        Get historical performance data for trading bots.
        Uses cached data if available and not expired.

        :param bot_id: Optional ID of the specific bot to fetch history for.
        :param days: Number of days of history to fetch (default: 30).
        :return: List of historical performance data points.
        """
        try:
            # Prepare cache key
            cache_params = {
                'bot_id': bot_id,
                'days': days
            }

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_bot_history', cache_params)

            if cached_data is not None:
                print(f"Using cached bot history data")
                return cached_data

            # If not in cache, fetch from Pionex service
            history_data = self.pionex_service.get_bot_history(bot_id, days)

            # Cache the result
            self.storage.save_data(
                'portfolio_bot_history',
                history_data,
                cache_params,
                expiry=self.cache_expiry['bot_history']
            )

            return history_data
        except Exception as e:
            print(f"Error getting bot history: {e}")
            return []

    def get_bot_details(self, bot_id):
        """
        Get detailed information about a specific trading bot.
        Uses cached data if available and not expired.

        :param bot_id: ID of the bot to fetch details for.
        :return: Dictionary with bot details.
        """
        try:
            # Fetch bot details from Pionex service
            # This is a direct pass-through without caching since the Pionex service already handles caching
            return self.pionex_service.get_bot_details(bot_id)
        except Exception as e:
            print(f"Error getting bot details: {e}")
            return {}

    def get_bot_performance_metrics(self, days=30):
        """
        Calculate performance metrics for all trading bots.
        Uses cached data if available and not expired.

        :param days: Number of days to analyze (default: 30).
        :return: Dictionary with performance metrics.
        """
        try:
            # Prepare cache key
            cache_params = {
                'days': days
            }

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_bot_performance', cache_params)

            if cached_data is not None:
                print(f"Using cached bot performance metrics")
                return cached_data

            # If not in cache, fetch and calculate metrics
            # Get all bot history
            history_data = self.get_bot_history(days=days)

            # Get active bots
            active_operations = self.get_active_operations()
            active_bots = active_operations.get("pionex", {}).get("grid_bots", [])

            # Calculate metrics
            metrics = {
                "total_bots": len(active_bots),
                "total_profit": 0,
                "total_investment": 0,
                "roi": 0,
                "daily_avg_profit": 0,
                "bots": []
            }

            # Process each bot
            for bot in active_bots:
                bot_id = bot.get("id")
                bot_symbol = bot.get("symbol")
                bot_profit = bot.get("profit", 0)
                bot_investment = bot.get("investment", {}).get("total", 0)

                # Filter history for this bot
                bot_history = [h for h in history_data if h.get("bot_id") == bot_id]

                # Calculate daily average profit
                daily_profits = [h.get("profit", 0) for h in bot_history]
                daily_avg = sum(daily_profits) / len(daily_profits) if daily_profits else 0

                # Calculate ROI
                roi = (bot_profit / bot_investment * 100) if bot_investment > 0 else 0

                # Add to totals
                metrics["total_profit"] += bot_profit
                metrics["total_investment"] += bot_investment

                # Add bot metrics
                metrics["bots"].append({
                    "id": bot_id,
                    "symbol": bot_symbol,
                    "profit": bot_profit,
                    "investment": bot_investment,
                    "roi": roi,
                    "daily_avg_profit": daily_avg
                })

            # Calculate overall ROI
            if metrics["total_investment"] > 0:
                metrics["roi"] = metrics["total_profit"] / metrics["total_investment"] * 100

            # Calculate overall daily average profit
            if metrics["total_bots"] > 0:
                metrics["daily_avg_profit"] = sum(b.get("daily_avg_profit", 0) for b in metrics["bots"]) / metrics["total_bots"]

            # Cache the result
            self.storage.save_data(
                'portfolio_bot_performance',
                metrics,
                cache_params,
                expiry=self.cache_expiry['bot_performance']
            )

            return metrics
        except Exception as e:
            print(f"Error calculating bot performance metrics: {e}")
            return {
                "total_bots": 0,
                "total_profit": 0,
                "total_investment": 0,
                "roi": 0,
                "daily_avg_profit": 0,
                "bots": []
            }

    def get_historical_performance(self, timeframe='daily', days=90):
        """
        Get historical portfolio performance.
        Uses cached data if available and not expired.

        :param timeframe: Timeframe for data points ('daily', 'weekly', 'monthly').
        :param days: Number of days to look back.
        :return: DataFrame with historical performance data.
        """
        try:
            # Prepare cache key
            cache_params = {
                'timeframe': timeframe,
                'days': days
            }

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_historical', cache_params)

            if cached_data is not None:
                print(f"Using cached historical performance data")
                # Convert cached data back to DataFrame with proper date format
                df = pd.DataFrame(cached_data)
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                return df

            # If not in cache, generate the data
            # Get current portfolio
            current_portfolio = self.get_total_portfolio()

            if current_portfolio.empty:
                return pd.DataFrame()

            # Create date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            if timeframe == 'daily':
                date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            elif timeframe == 'weekly':
                date_range = pd.date_range(start=start_date, end=end_date, freq='W')
            elif timeframe == 'monthly':
                date_range = pd.date_range(start=start_date, end=end_date, freq='M')
            else:
                date_range = pd.date_range(start=start_date, end=end_date, freq='D')

            # In a real implementation, you would fetch historical data from a database
            # For this demo, we'll simulate historical data based on current portfolio

            # Get current total value
            current_value = current_portfolio['value_usd'].sum()

            # Simulate historical values with some randomness
            np.random.seed(42)  # For reproducibility

            # Create a trend with some randomness
            trend_factor = np.linspace(0.7, 1.0, len(date_range))  # Upward trend
            random_factor = np.random.normal(1, 0.03, len(date_range))  # Random noise

            historical_values = current_value * trend_factor * random_factor

            # Create DataFrame
            historical_df = pd.DataFrame({
                'date': date_range,
                'portfolio_value': historical_values
            })

            # Cache the result - convert dates to strings for JSON serialization
            cache_df = historical_df.copy()
            cache_df['date'] = cache_df['date'].astype(str)

            self.storage.save_data(
                'portfolio_historical',
                cache_df.to_dict('records'),
                cache_params,
                expiry=self.cache_expiry['historical']
            )

            return historical_df
        except Exception as e:
            print(f"Error getting historical performance: {e}")
            return pd.DataFrame()

    def get_asset_allocation(self):
        """
        Get current asset allocation as percentages.
        Uses cached data if available and not expired.

        :return: DataFrame with asset allocation data.
        """
        try:
            # Prepare cache key
            cache_params = {}

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_allocation', cache_params)

            if cached_data is not None:
                print(f"Using cached asset allocation data")
                return pd.DataFrame(cached_data)

            # If not in cache, calculate the allocation
            portfolio = self.get_total_portfolio()

            if portfolio.empty:
                return pd.DataFrame()

            total_value = portfolio['value_usd'].sum()

            if total_value == 0:
                portfolio['allocation'] = 0
            else:
                portfolio['allocation'] = portfolio['value_usd'] / total_value * 100

            # Select and prepare the result
            allocation_df = portfolio[['asset', 'allocation', 'value_usd']]

            # Cache the result
            self.storage.save_data(
                'portfolio_allocation',
                allocation_df.to_dict('records'),
                cache_params,
                expiry=self.cache_expiry['allocation']
            )

            return allocation_df
        except Exception as e:
            print(f"Error getting asset allocation: {e}")
            return pd.DataFrame()

    def get_platform_allocation(self):
        """
        Get allocation by platform.

        :return: DataFrame with platform allocation data.
        """
        try:
            # Get data from each platform
            binance_spot = self._get_binance_spot_holdings()
            binance_futures = self._get_binance_futures_holdings()
            pionex_holdings = self._get_pionex_holdings()

            # Get Pionex grid bot data separately
            pionex_grid_bots_value = 0
            try:
                # Get active operations
                operations = self.get_active_operations()
                grid_bots = operations.get("pionex", {}).get("grid_bots", [])

                # Calculate total value of grid bots
                for bot in grid_bots:
                    if isinstance(bot, dict):
                        # Get investment details
                        investment = bot.get("investment", {})
                        total_investment = investment.get("total", 0)
                        pionex_grid_bots_value += total_investment
            except Exception as e:
                print(f"Error calculating Pionex grid bot value: {e}")

            # Calculate total value for each platform
            platforms = []

            if not binance_spot.empty:
                platforms.append({
                    'platform': 'Binance Spot',
                    'value_usd': binance_spot['value_usd'].sum()
                })

            if not binance_futures.empty:
                platforms.append({
                    'platform': 'Binance Futures',
                    'value_usd': binance_futures['value_usd'].sum()
                })

            if not pionex_holdings.empty:
                platforms.append({
                    'platform': 'Pionex Spot',
                    'value_usd': pionex_holdings['value_usd'].sum()
                })

            # Add Pionex Grid Bots as a separate platform if there are any
            if pionex_grid_bots_value > 0:
                platforms.append({
                    'platform': 'Pionex Grid Bots',
                    'value_usd': pionex_grid_bots_value
                })

            if not platforms:
                return pd.DataFrame()

            # Create DataFrame
            platform_df = pd.DataFrame(platforms)

            # Calculate allocation
            total_value = platform_df['value_usd'].sum()

            if total_value == 0:
                platform_df['allocation'] = 0
            else:
                platform_df['allocation'] = platform_df['value_usd'] / total_value * 100

            return platform_df
        except Exception as e:
            print(f"Error getting platform allocation: {e}")
            return pd.DataFrame()

    def get_transaction_history(self, start_date=None, end_date=None, limit=100):
        """
        Get transaction history across all platforms.
        Uses cached data if available and not expired.
        Loads data concurrently for better performance.

        :param start_date: Start date in 'YYYY-MM-DD' format.
        :param end_date: End date in 'YYYY-MM-DD' format.
        :param limit: Maximum number of transactions to return.
        :return: DataFrame with transaction history.
        """
        try:
            # Prepare cache key
            cache_params = {
                'start_date': start_date,
                'end_date': end_date,
                'limit': limit
            }

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_transactions', cache_params)

            if cached_data is not None:
                print(f"Using cached transaction history data")
                # Convert cached data back to DataFrame with proper date format
                df = pd.DataFrame(cached_data)
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                return df

            # If not in cache, fetch and process the data concurrently
            # Define tasks for concurrent execution - excluding futures transfers
            tasks = [
                (self.binance_service.get_spot_deposit_history, {'start_date': start_date, 'end_date': end_date}),
                (self.binance_service.get_spot_withdraw_history, {'start_date': start_date, 'end_date': end_date}),
                (self.pionex_service.get_wallet_movements, {'start_date': start_date, 'end_date': end_date})
            ]

            # Execute tasks concurrently
            results = execute_concurrently(tasks)

            # Extract results - no futures transfers
            binance_deposits = results[0] or []
            binance_withdrawals = results[1] or []

            # Handle Pionex data safely
            pionex_data = results[2] if results[2] is not None else {}

            # Initialize pionex_trades as an empty list
            pionex_trades = []

            # Check if pionex_data is a dictionary before using .get()
            if isinstance(pionex_data, dict):
                # Try to get recent_trades from the dictionary
                pionex_trades = pionex_data.get('recent_trades', [])
            elif isinstance(pionex_data, list):
                # If pionex_data is already a list, it might be the trades directly
                print(f"Pionex data is a list with {len(pionex_data)} items")

                # Filter out any string items in the list
                valid_trades = []
                for item in pionex_data:
                    if isinstance(item, dict):
                        valid_trades.append(item)
                    elif isinstance(item, str):
                        # Skip string items but don't print for each one to avoid spam
                        pass
                    else:
                        print(f"Skipping unknown Pionex data item type: {type(item)}")

                pionex_trades = valid_trades

                # If we filtered out all items, create some sample trades for demo mode
                if not pionex_trades:
                    print("Creating sample Pionex trades for demo mode")
                    current_time = int(datetime.now().timestamp() * 1000)
                    pionex_trades = [
                        {
                            'symbol': 'BTC_USDT',
                            'side': 'BUY',
                            'price': 60000.0,
                            'size': 0.1,
                            'timestamp': current_time - 86400000,  # 1 day ago
                            'fee': 0.1,
                            'feeCoin': 'USDT',
                            'id': '12345'
                        },
                        {
                            'symbol': 'ETH_USDT',
                            'side': 'SELL',
                            'price': 3500.0,
                            'size': 1.0,
                            'timestamp': current_time - 172800000,  # 2 days ago
                            'fee': 0.1,
                            'feeCoin': 'USDT',
                            'id': '12346'
                        }
                    ]
            else:
                print(f"Warning: Pionex data is not a dictionary or list: {type(pionex_data)}")

            # Ensure pionex_trades is a list
            if not isinstance(pionex_trades, list):
                print(f"Warning: Pionex trades is not a list: {type(pionex_trades)}")
                pionex_trades = []

            # Empty list for transfers since we're not fetching them
            binance_transfers = []

            # Process Binance deposits
            deposit_transactions = []
            for deposit in binance_deposits:
                try:
                    # Check if deposit is a dictionary
                    if not isinstance(deposit, dict):
                        print(f"Skipping non-dictionary deposit: {type(deposit)}")
                        continue

                    # Handle insertTime which could be a timestamp or a string
                    insert_time = deposit.get('insertTime', 0)

                    if isinstance(insert_time, str) and '-' in insert_time:
                        # If it's already a formatted date string
                        date_obj = datetime.fromisoformat(insert_time.replace('Z', '+00:00'))
                    else:
                        # If it's a timestamp
                        try:
                            # Try to convert to int first in case it's a string representation of a number
                            if isinstance(insert_time, str) and insert_time.isdigit():
                                insert_time = int(insert_time)
                            date_obj = datetime.fromtimestamp(insert_time / 1000)
                        except (ValueError, TypeError, OverflowError):
                            # If conversion fails, use current time
                            date_obj = datetime.now()

                    # Get asset safely
                    asset = ''
                    if 'coin' in deposit:
                        asset = deposit.get('coin', '')
                    elif 'asset' in deposit:
                        asset = deposit.get('asset', '')

                    # Get amount safely
                    amount = 0.0
                    try:
                        amount = float(deposit.get('amount', 0))
                    except (ValueError, TypeError):
                        amount = 0.0

                    # Get status safely
                    status = deposit.get('status', '')

                    # Get transaction ID safely
                    txid = ''
                    if 'txId' in deposit:
                        txid = deposit.get('txId', '')
                    elif 'txid' in deposit:
                        txid = deposit.get('txid', '')

                    deposit_transactions.append({
                        'date': date_obj,
                        'asset': asset,
                        'type': 'Deposit',
                        'amount': amount,
                        'status': status,
                        'platform': 'Binance',
                        'txid': txid
                    })
                except Exception as e:
                    print(f"Error processing deposit: {e}, deposit data: {deposit}")

            # Process Binance withdrawals
            withdrawal_transactions = []
            for withdrawal in binance_withdrawals:
                try:
                    # Handle applyTime which could be a timestamp or a string
                    apply_time = withdrawal.get('applyTime', 0)

                    if isinstance(apply_time, str) and '-' in apply_time:
                        # If it's already a formatted date string
                        date_obj = datetime.fromisoformat(apply_time.replace('Z', '+00:00'))
                    else:
                        # If it's a timestamp
                        try:
                            # Try to convert to int first in case it's a string representation of a number
                            if isinstance(apply_time, str) and apply_time.isdigit():
                                apply_time = int(apply_time)
                            date_obj = datetime.fromtimestamp(apply_time / 1000)
                        except (ValueError, TypeError, OverflowError):
                            # If conversion fails, use current time
                            date_obj = datetime.now()

                    withdrawal_transactions.append({
                        'date': date_obj,
                        'asset': withdrawal.get('coin', ''),
                        'type': 'Withdrawal',
                        'amount': float(withdrawal.get('amount', 0)),
                        'status': withdrawal.get('status', ''),
                        'platform': 'Binance',
                        'txid': withdrawal.get('txId', '')
                    })
                except Exception as e:
                    print(f"Error processing withdrawal: {e}, withdrawal data: {withdrawal}")

            # Process Binance transfers
            transfer_transactions = []
            for transfer in binance_transfers:
                try:
                    transfer_type_map = {
                        1: "Spot to USDT-M Futures",
                        2: "USDT-M Futures to Spot",
                        3: "Spot to COIN-M Futures",
                        4: "COIN-M Futures to Spot"
                    }

                    # Handle timestamp which could be a timestamp or a string
                    timestamp = transfer.get('timestamp', 0)

                    if isinstance(timestamp, str) and '-' in timestamp:
                        # If it's already a formatted date string
                        date_obj = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        # If it's a timestamp
                        try:
                            # Try to convert to int first in case it's a string representation of a number
                            if isinstance(timestamp, str) and timestamp.isdigit():
                                timestamp = int(timestamp)
                            date_obj = datetime.fromtimestamp(timestamp / 1000)
                        except (ValueError, TypeError, OverflowError):
                            # If conversion fails, use current time
                            date_obj = datetime.now()

                    # Get transfer type
                    transfer_type = transfer.get('type')
                    if isinstance(transfer_type, str) and transfer_type.isdigit():
                        transfer_type = int(transfer_type)

                    transfer_transactions.append({
                        'date': date_obj,
                        'asset': transfer.get('asset', ''),
                        'type': transfer_type_map.get(transfer_type, 'Transfer'),
                        'amount': float(transfer.get('amount', 0)),
                        'status': transfer.get('status', ''),
                        'platform': 'Binance',
                        'txid': ''
                    })
                except Exception as e:
                    print(f"Error processing transfer: {e}, transfer data: {transfer}")

            # Process Pionex trades
            trade_transactions = []
            for trade in pionex_trades:
                try:
                    # Convert values safely with error handling
                    timestamp = trade.get('timestamp', 0)

                    # If timestamp is 0 or very small, use current time with an offset
                    if timestamp == 0 or timestamp < 1000000:  # Very small timestamp is likely invalid
                        # Use current time with a random offset to create a realistic timeline
                        import random
                        random_days = random.randint(1, 30)  # Random number of days in the past
                        timestamp = int(datetime.now().timestamp() * 1000) - (random_days * 86400000)
                    # Handle different timestamp formats
                    elif isinstance(timestamp, str):
                        try:
                            # Try to convert string to int
                            timestamp = int(timestamp)
                        except ValueError:
                            try:
                                # Try to parse as ISO format date
                                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                timestamp = int(dt.timestamp() * 1000)
                            except ValueError:
                                # If all conversions fail, use current time
                                timestamp = int(datetime.now().timestamp() * 1000)

                    # Ensure timestamp is an integer or float
                    if not isinstance(timestamp, (int, float)):
                        timestamp = int(datetime.now().timestamp() * 1000)

                    # Ensure timestamp is in milliseconds (Pionex might return seconds)
                    if timestamp < 10000000000:  # If timestamp is in seconds (10 digits or less)
                        timestamp = timestamp * 1000

                    # Process symbol and extract asset
                    symbol = trade.get('symbol', '')
                    side = trade.get('side', '').upper()

                    if not symbol:
                        asset = 'UNKNOWN'
                    else:
                        # For BUY orders, we're buying the base asset (first part of the pair)
                        # For SELL orders, we're selling the base asset
                        if '_' in symbol:
                            parts = symbol.split('_')
                            if len(parts) >= 2:
                                base_asset = parts[0]  # e.g., BTC in BTC_USDT
                                quote_asset = parts[1]  # e.g., USDT in BTC_USDT

                                # Use the appropriate asset based on the side of the trade
                                if side == 'BUY':
                                    asset = base_asset
                                elif side == 'SELL':
                                    asset = base_asset
                                else:
                                    asset = base_asset  # Default to base asset if side is unknown
                            else:
                                asset = symbol
                        else:
                            asset = symbol

                    # Process size/amount
                    size = trade.get('size', 0)
                    try:
                        size = float(size) if size else 0
                    except (ValueError, TypeError):
                        size = 0

                    # Process price
                    price = trade.get('price', 0)
                    try:
                        price = float(price) if price else 0
                    except (ValueError, TypeError):
                        price = 0

                    # Process fee
                    fee = trade.get('fee', 0)
                    try:
                        fee = float(fee) if fee else 0
                    except (ValueError, TypeError):
                        fee = 0

                    # Process trade ID
                    trade_id = trade.get('id', '')
                    trade_id = str(trade_id) if trade_id else ''

                    # Create transaction record with safe datetime conversion
                    try:
                        # Convert timestamp to datetime safely
                        date_obj = datetime.fromtimestamp(timestamp / 1000)
                    except (ValueError, OverflowError, OSError):
                        # If conversion fails, use current time
                        print(f"WARNING: Could not convert timestamp {timestamp} to datetime, using current time")
                        date_obj = datetime.now()

                    trade_transactions.append({
                        'date': date_obj,
                        'asset': asset,
                        'type': trade.get('side', ''),
                        'amount': size,
                        'price': price,
                        'fee': fee,
                        'fee_asset': trade.get('feeCoin', ''),
                        'platform': 'Pionex',
                        'txid': trade_id
                    })
                except Exception as e:
                    print(f"Error processing Pionex trade: {e}, trade data: {trade}")

            # Combine all transactions
            all_transactions = deposit_transactions + withdrawal_transactions + transfer_transactions + trade_transactions

            if not all_transactions:
                return pd.DataFrame()

            # Create DataFrame
            transactions_df = pd.DataFrame(all_transactions)

            # Sort by date
            transactions_df = transactions_df.sort_values('date', ascending=False)

            # Limit number of transactions
            if len(transactions_df) > limit:
                transactions_df = transactions_df.head(limit)

            # Cache the result - convert dates to strings for JSON serialization
            cache_df = transactions_df.copy()
            cache_df['date'] = cache_df['date'].astype(str)

            self.storage.save_data(
                'portfolio_transactions',
                cache_df.to_dict('records'),
                cache_params,
                expiry=self.cache_expiry['transactions']
            )

            return transactions_df
        except Exception as e:
            print(f"Error getting transaction history: {e}")
            return pd.DataFrame()

    def get_roi_by_asset(self):
        """
        Get ROI breakdown by asset.
        Uses cached data if available and not expired.

        :return: DataFrame with ROI by asset.
        """
        try:
            # Prepare cache key
            cache_params = {}

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_roi', cache_params)

            if cached_data is not None:
                print(f"Using cached ROI by asset data")
                return pd.DataFrame(cached_data)

            # If not in cache, calculate the ROI
            portfolio = self.get_total_portfolio()

            if portfolio.empty:
                return pd.DataFrame()

            # Calculate ROI
            portfolio['roi'] = portfolio.apply(
                lambda row: (row['value_usd'] - row['cost_basis']) / row['cost_basis'] * 100 if row['cost_basis'] > 0 else 0,
                axis=1
            )

            # Calculate absolute profit/loss
            portfolio['profit_loss'] = portfolio['value_usd'] - portfolio['cost_basis']

            # Select and prepare the result
            roi_df = portfolio[['asset', 'value_usd', 'cost_basis', 'profit_loss', 'roi']].sort_values('roi', ascending=False)

            # Cache the result
            self.storage.save_data(
                'portfolio_roi',
                roi_df.to_dict('records'),
                cache_params,
                expiry=self.cache_expiry['roi']
            )

            return roi_df
        except Exception as e:
            print(f"Error getting ROI by asset: {e}")
            return pd.DataFrame()

    def get_portfolio_summary(self):
        """
        Get portfolio summary metrics.
        Uses cached data if available and not expired.

        :return: Dictionary with summary metrics.
        """
        try:
            # Prepare cache key
            cache_params = {}

            # Check if data exists in cache
            cached_data = self.storage.load_data('portfolio_summary', cache_params)

            if cached_data is not None:
                print(f"Using cached portfolio summary data")
                return cached_data

            # If not in cache, calculate the summary
            portfolio = self.get_total_portfolio()

            if portfolio.empty:
                summary = {
                    'total_value': 0,
                    'total_cost': 0,
                    'total_profit_loss': 0,
                    'total_roi': 0,
                    'asset_count': 0
                }
            else:
                total_value = portfolio['value_usd'].sum()
                total_cost = portfolio['cost_basis'].sum()
                total_profit_loss = total_value - total_cost

                if total_cost > 0:
                    total_roi = (total_profit_loss / total_cost) * 100
                else:
                    total_roi = 0

                summary = {
                    'total_value': total_value,
                    'total_cost': total_cost,
                    'total_profit_loss': total_profit_loss,
                    'total_roi': total_roi,
                    'asset_count': len(portfolio)
                }

            # Cache the result
            self.storage.save_data(
                'portfolio_summary',
                summary,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return summary
        except Exception as e:
            print(f"Error getting portfolio summary: {e}")
            return {
                'total_value': 0,
                'total_cost': 0,
                'total_profit_loss': 0,
                'total_roi': 0,
                'asset_count': 0
            }

    def enable_enhanced_refresh(self) -> bool:
        """
        Enable enhanced async refresh capabilities by switching to enhanced storage.

        :return: True if successfully enabled, False otherwise
        """
        try:
            from services.enhanced_portfolio_service import EnhancedPortfolioService

            # Create enhanced portfolio service instance
            enhanced_service = EnhancedPortfolioService()

            # Copy current service configurations
            enhanced_service.binance_service = self.binance_service
            enhanced_service.pionex_service = self.pionex_service

            # Replace current instance attributes with enhanced ones
            self.__dict__.update(enhanced_service.__dict__)
            self.__class__ = EnhancedPortfolioService

            return True

        except Exception as e:
            print(f"Error enabling enhanced refresh: {e}")
            return False

    def refresh_data_parallel(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Refresh data using parallel execution (sync version).

        :param force_refresh: If True, ignore cache and fetch fresh data
        :return: Dictionary with refresh results
        """
        try:
            # Check if enhanced refresh is available
            if hasattr(self, 'refresh_all_data_async'):
                # Use async refresh in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                result = loop.run_until_complete(
                    self.refresh_all_data_async(force_refresh=force_refresh)
                )

                loop.close()
                return result
            else:
                # Fallback to traditional refresh
                return self._traditional_refresh(force_refresh)

        except Exception as e:
            print(f"Error in parallel refresh: {e}")
            return {
                'success': False,
                'error': str(e),
                'timing': {'total_time': 0}
            }

    def _traditional_refresh(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Traditional refresh method using concurrent execution.

        :param force_refresh: If True, ignore cache and fetch fresh data
        :return: Dictionary with refresh results
        """
        import time
        start_time = time.time()

        try:
            # Define refresh tasks
            tasks = [
                (self.binance_service.get_spot_account_snapshot, {}),
                (self.binance_service.get_live_positions, {}),
                (self.pionex_service.get_active_operations, {}),
                (self.pionex_service.get_detailed_spot_balances, {})
            ]

            # Execute tasks concurrently
            results = execute_concurrently(tasks, max_workers=4)

            # Process results
            successful_tasks = len([r for r in results if r is not None])

            return {
                'success': True,
                'data_sources': {
                    'binance': {'updated': ['spot_snapshot', 'live_positions']},
                    'pionex': {'updated': ['active_operations', 'spot_balances']}
                },
                'timing': {
                    'total_time': time.time() - start_time,
                    'tasks_executed': len(tasks),
                    'successful_tasks': successful_tasks
                }
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timing': {'total_time': time.time() - start_time}
            }
