import asyncio
import concurrent.futures
import time
from typing import Any, Dict, List, Optional, Callable, Tuple
from datetime import datetime, timedelta
import threading
from dataclasses import dataclass
from enum import Enum
from utils.symbol_validator import symbol_validator

class DataPriority(Enum):
    """Priority levels for data fetching operations."""
    HIGH = 1      # Real-time data (prices, active orders)
    MEDIUM = 2    # Account data (balances, positions)
    LOW = 3       # Historical data (closed orders, transactions)

class RateLimiter:
    """Rate limiter to respect API limits."""

    def __init__(self, max_requests: int, time_window: int):
        """
        Initialize rate limiter.

        :param max_requests: Maximum requests allowed in time window
        :param time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self._lock = threading.Lock()

    def can_make_request(self) -> bool:
        """Check if a request can be made without exceeding rate limits."""
        with self._lock:
            current_time = time.time()
            # Remove old requests outside the time window
            self.requests = [req_time for req_time in self.requests
                           if current_time - req_time < self.time_window]

            return len(self.requests) < self.max_requests

    def record_request(self):
        """Record a request timestamp."""
        with self._lock:
            self.requests.append(time.time())

    async def wait_if_needed(self):
        """Wait if rate limit would be exceeded."""
        while not self.can_make_request():
            await asyncio.sleep(0.1)
        self.record_request()

@dataclass
class DataFetchTask:
    """Represents a data fetching task."""
    name: str
    func: Callable
    kwargs: Dict[str, Any]
    priority: DataPriority
    timeout: Optional[int] = None
    retry_count: int = 3
    rate_limiter: Optional[RateLimiter] = None

class AsyncDataFetcher:
    """
    Async data fetcher with parallel execution, rate limiting, and error handling.
    """

    def __init__(self, max_concurrent_tasks: int = 10):
        """
        Initialize async data fetcher.

        :param max_concurrent_tasks: Maximum number of concurrent tasks
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent_tasks)

        # Rate limiters for different APIs
        self.rate_limiters = {
            'binance': RateLimiter(max_requests=1200, time_window=60),  # 1200 requests per minute
            'pionex': RateLimiter(max_requests=600, time_window=60),    # 600 requests per minute
            'default': RateLimiter(max_requests=100, time_window=60)    # Conservative default
        }

    async def fetch_data_parallel(self, tasks: List[DataFetchTask]) -> Dict[str, Any]:
        """
        Fetch data from multiple sources in parallel with proper error handling.

        :param tasks: List of data fetch tasks
        :return: Dictionary with results keyed by task name
        """
        # Sort tasks by priority
        tasks.sort(key=lambda x: x.priority.value)

        # Group tasks by priority for staged execution
        priority_groups = {}
        for task in tasks:
            if task.priority not in priority_groups:
                priority_groups[task.priority] = []
            priority_groups[task.priority].append(task)

        results = {}

        # Execute tasks by priority groups
        for priority in [DataPriority.HIGH, DataPriority.MEDIUM, DataPriority.LOW]:
            if priority in priority_groups:
                group_results = await self._execute_task_group(priority_groups[priority])
                results.update(group_results)

        return results

    async def _execute_task_group(self, tasks: List[DataFetchTask]) -> Dict[str, Any]:
        """Execute a group of tasks with the same priority."""
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)

        async def execute_single_task(task: DataFetchTask) -> Tuple[str, Any]:
            async with semaphore:
                return await self._execute_task_with_retry(task)

        # Execute all tasks in the group concurrently
        task_coroutines = [execute_single_task(task) for task in tasks]
        completed_tasks = await asyncio.gather(*task_coroutines, return_exceptions=True)

        # Process results
        results = {}
        for i, result in enumerate(completed_tasks):
            task_name = tasks[i].name
            if isinstance(result, Exception):
                print(f"Task {task_name} failed with exception: {result}")
                results[task_name] = None
            else:
                task_name, task_result = result
                results[task_name] = task_result

        return results

    async def _execute_task_with_retry(self, task: DataFetchTask) -> Tuple[str, Any]:
        """Execute a single task with retry logic."""
        last_exception = None

        for attempt in range(task.retry_count + 1):
            try:
                # Apply rate limiting if specified
                if task.rate_limiter:
                    await task.rate_limiter.wait_if_needed()

                # Execute the task
                if asyncio.iscoroutinefunction(task.func):
                    # If it's already an async function
                    if task.timeout:
                        result = await asyncio.wait_for(
                            task.func(**task.kwargs),
                            timeout=task.timeout
                        )
                    else:
                        result = await task.func(**task.kwargs)
                else:
                    # Run sync function in executor
                    if task.timeout:
                        result = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                self.executor,
                                lambda: task.func(**task.kwargs)
                            ),
                            timeout=task.timeout
                        )
                    else:
                        result = await asyncio.get_event_loop().run_in_executor(
                            self.executor,
                            lambda: task.func(**task.kwargs)
                        )

                return task.name, result

            except Exception as e:
                last_exception = e
                if attempt < task.retry_count:
                    # Exponential backoff
                    wait_time = 2 ** attempt
                    print(f"Task {task.name} failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"Task {task.name} failed after {task.retry_count + 1} attempts: {e}")

        # If all retries failed, return the task name with None result
        return task.name, None

    def create_binance_tasks(self, binance_service) -> List[DataFetchTask]:
        """Create data fetch tasks for Binance API."""
        tasks = []

        # High priority tasks (real-time data)
        tasks.append(DataFetchTask(
            name="binance_live_positions",
            func=binance_service.get_live_positions,
            kwargs={},
            priority=DataPriority.HIGH,
            timeout=10,
            rate_limiter=self.rate_limiters['binance']
        ))

        # Medium priority tasks (account data)
        tasks.append(DataFetchTask(
            name="binance_spot_snapshot",
            func=binance_service.get_spot_account_snapshot,
            kwargs={},
            priority=DataPriority.MEDIUM,
            timeout=15,
            rate_limiter=self.rate_limiters['binance']
        ))

        tasks.append(DataFetchTask(
            name="binance_futures_account",
            func=binance_service.get_futures_account_info,
            kwargs={},
            priority=DataPriority.MEDIUM,
            timeout=15,
            rate_limiter=self.rate_limiters['binance']
        ))

        # Low priority tasks (historical data)
        tasks.append(DataFetchTask(
            name="binance_spot_deposits",
            func=binance_service.get_spot_deposit_history,
            kwargs={},
            priority=DataPriority.LOW,
            timeout=30,
            rate_limiter=self.rate_limiters['binance']
        ))

        tasks.append(DataFetchTask(
            name="binance_spot_withdrawals",
            func=binance_service.get_spot_withdraw_history,
            kwargs={},
            priority=DataPriority.LOW,
            timeout=30,
            rate_limiter=self.rate_limiters['binance']
        ))

        return tasks

    def create_pionex_tasks(self, pionex_service) -> List[DataFetchTask]:
        """Create data fetch tasks for Pionex API."""
        tasks = []

        # High priority tasks
        tasks.append(DataFetchTask(
            name="pionex_active_operations",
            func=pionex_service.get_active_operations,
            kwargs={},
            priority=DataPriority.HIGH,
            timeout=10,
            rate_limiter=self.rate_limiters['pionex']
        ))

        # Medium priority tasks
        tasks.append(DataFetchTask(
            name="pionex_spot_balances",
            func=pionex_service.get_detailed_spot_balances,
            kwargs={},
            priority=DataPriority.MEDIUM,
            timeout=15,
            rate_limiter=self.rate_limiters['pionex']
        ))

        # Low priority tasks
        tasks.append(DataFetchTask(
            name="pionex_wallet_movements",
            func=pionex_service.get_wallet_movements,
            kwargs={},
            priority=DataPriority.LOW,
            timeout=30,
            rate_limiter=self.rate_limiters['pionex']
        ))

        return tasks

    def create_price_tasks(self, binance_service, symbols: List[str]) -> List[DataFetchTask]:
        """Create parallel price fetching tasks."""
        tasks = []

        # Group symbols into batches to avoid overwhelming the API
        batch_size = 5
        symbol_batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]

        for i, batch in enumerate(symbol_batches):
            tasks.append(DataFetchTask(
                name=f"price_batch_{i}",
                func=self._fetch_price_batch,
                kwargs={"binance_service": binance_service, "symbols": batch},
                priority=DataPriority.HIGH,
                timeout=10,
                rate_limiter=self.rate_limiters['binance']
            ))

        return tasks

    def _fetch_price_batch(self, binance_service, symbols: List[str]) -> Dict[str, float]:
        """
        Fetch prices for a batch of symbols with improved error handling and validation.
        """
        prices = {}
        processed_symbols = set()  # Track processed symbols to avoid duplicates

        for symbol in symbols:
            # Skip if already processed
            if symbol in processed_symbols:
                continue

            processed_symbols.add(symbol)

            # Validate symbol before attempting to fetch
            if not symbol_validator.is_valid_symbol(symbol):
                # Skip invalid symbols silently (already logged by validator)
                prices[symbol] = None
                continue

            try:
                # Check cache first to avoid redundant API calls
                if hasattr(binance_service, 'storage'):
                    cached_price = binance_service.storage.load_data('binance_price', {'symbol': symbol})
                    if cached_price is not None:
                        prices[symbol] = cached_price
                        continue

                # Fetch price with timeout
                price = binance_service.get_current_price(symbol, timeout=5)
                prices[symbol] = price

                # Record result in validator
                if price is not None:
                    symbol_validator.record_successful_lookup(symbol)
                else:
                    symbol_validator.record_failed_lookup(symbol, "price_fetch_failed")

            except Exception as e:
                # Record failure and set price to None
                symbol_validator.record_failed_lookup(symbol, "batch_fetch_error")
                symbol_validator.log_error_once(symbol, f"Batch fetch error: {str(e)}")
                prices[symbol] = None

        return prices

    async def fetch_all_data(self, binance_service, pionex_service,
                           price_symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Fetch all data from all sources in parallel.

        :param binance_service: Binance service instance
        :param pionex_service: Pionex service instance
        :param price_symbols: List of symbols to fetch prices for
        :return: Dictionary with all fetched data
        """
        all_tasks = []

        # Add Binance tasks
        all_tasks.extend(self.create_binance_tasks(binance_service))

        # Add Pionex tasks
        all_tasks.extend(self.create_pionex_tasks(pionex_service))

        # Add price tasks if symbols provided
        if price_symbols:
            all_tasks.extend(self.create_price_tasks(binance_service, price_symbols))

        # Execute all tasks in parallel
        start_time = time.time()
        results = await self.fetch_data_parallel(all_tasks)
        end_time = time.time()

        # Add timing information
        results['_fetch_metadata'] = {
            'total_time': end_time - start_time,
            'task_count': len(all_tasks),
            'timestamp': datetime.now().isoformat(),
            'successful_tasks': len([r for r in results.values() if r is not None])
        }

        return results

    def __del__(self):
        """Cleanup executor on deletion."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
