def calculate_pnl(order, current_price):
    executed_qty = float(order.get("executedQty", 0))
    avg_price = float(order.get("avgPrice", 0))
    side = order.get("side")

    if executed_qty == 0 or avg_price == 0 or current_price is None:
        return None, None

    invested_usd = executed_qty * avg_price

    pnl = (current_price - avg_price) * executed_qty if side == "BUY" else (avg_price - current_price) * executed_qty
    roi = (pnl / invested_usd) * 100

    return pnl, roi
