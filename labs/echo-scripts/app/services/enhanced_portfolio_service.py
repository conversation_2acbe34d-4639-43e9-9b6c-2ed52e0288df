import asyncio
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import time

from .portfolio_service import PortfolioService
from .binance_service import BinanceService
from .pionex_service import PionexService
from .storage.crypto_storage import CryptoStorage
from .async_data_fetcher import Async<PERSON><PERSON><PERSON>et<PERSON>, DataFetchTask, DataPriority
from utils.symbol_validator import symbol_validator

class EnhancedPortfolioService(PortfolioService):
    """
    Enhanced portfolio service with parallel data fetching and non-destructive updates.
    Extends the base PortfolioService with async capabilities and smart caching.
    """

    def __init__(self):
        super().__init__()
        self.async_fetcher = AsyncDataFetcher(max_concurrent_tasks=15)

        # Enhanced cache expiry times with different frequencies
        self.smart_cache_expiry = {
            'prices': 30,           # 30 seconds - very frequent updates
            'active_operations': 60, # 1 minute - frequent updates
            'account_balances': 300, # 5 minutes - moderate updates
            'positions': 300,        # 5 minutes - moderate updates
            'historical_data': 1800, # 30 minutes - infrequent updates
            'transactions': 3600,    # 1 hour - rare updates
            'bot_history': 3600      # 1 hour - rare updates
        }

    async def refresh_all_data_async(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Refresh all portfolio data using parallel async fetching.

        :param force_refresh: If True, ignore cache and fetch fresh data
        :return: Dictionary with refresh results and timing information
        """
        start_time = time.time()
        refresh_results = {
            'success': False,
            'start_time': datetime.now().isoformat(),
            'data_sources': {},
            'errors': [],
            'timing': {}
        }

        try:
            # Determine what data needs refreshing
            refresh_plan = self._create_refresh_plan(force_refresh)

            # Get symbols for price fetching
            price_symbols = self._get_price_symbols()

            # Fetch all data in parallel
            fetch_start = time.time()
            all_data = await self.async_fetcher.fetch_all_data(
                self.binance_service,
                self.pionex_service,
                price_symbols if refresh_plan['prices'] else None
            )
            fetch_time = time.time() - fetch_start

            # Process and store the fetched data
            process_start = time.time()
            processed_results = await self._process_fetched_data(all_data, refresh_plan)
            process_time = time.time() - process_start

            # Update refresh results
            refresh_results.update({
                'success': True,
                'data_sources': processed_results,
                'timing': {
                    'total_time': time.time() - start_time,
                    'fetch_time': fetch_time,
                    'process_time': process_time,
                    'tasks_executed': all_data.get('_fetch_metadata', {}).get('task_count', 0),
                    'successful_tasks': all_data.get('_fetch_metadata', {}).get('successful_tasks', 0)
                }
            })

        except Exception as e:
            refresh_results['errors'].append(f"Async refresh failed: {str(e)}")
            print(f"Error in async refresh: {e}")

        refresh_results['end_time'] = datetime.now().isoformat()
        return refresh_results

    def _create_refresh_plan(self, force_refresh: bool) -> Dict[str, bool]:
        """
        Create a plan for what data needs to be refreshed based on cache freshness.

        :param force_refresh: If True, refresh everything
        :return: Dictionary indicating what data types need refreshing
        """
        if force_refresh:
            return {
                'prices': True,
                'active_operations': True,
                'account_balances': True,
                'positions': True,
                'historical_data': True,
                'transactions': True
            }

        plan = {}

        # Check each data type's freshness
        for data_type, max_age in self.smart_cache_expiry.items():
            # Check if we have fresh data for this type
            needs_refresh = True

            if data_type == 'prices':
                # Check a sample of price data
                sample_symbols = ['BTCUSDT', 'ETHUSDT']
                for symbol in sample_symbols:
                    if self.storage.is_data_fresh('binance_price', {'symbol': symbol}, max_age):
                        needs_refresh = False
                        break

            elif data_type == 'active_operations':
                needs_refresh = not self.storage.is_data_fresh(
                    'pionex_active_operations', {}, max_age
                )

            elif data_type == 'account_balances':
                needs_refresh = not self.storage.is_data_fresh(
                    'binance_spot_snapshot', {}, max_age
                )

            elif data_type == 'positions':
                needs_refresh = not self.storage.is_data_fresh(
                    'binance_live_positions', {}, max_age
                )

            elif data_type == 'historical_data':
                needs_refresh = not self.storage.is_data_fresh(
                    'binance_spot_deposits', {}, max_age
                )

            elif data_type == 'transactions':
                needs_refresh = not self.storage.is_data_fresh(
                    'binance_income', {}, max_age
                )

            plan[data_type] = needs_refresh

        return plan

    def _get_price_symbols(self) -> List[str]:
        """Get list of symbols that need price updates, filtered for validity."""
        # Standard symbols - keep it simple to avoid infinite loops
        candidate_symbols = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT',
            'INJUSDT', 'SUIUSDT', 'ADAUSDT', 'XRPUSDT'
        ]

        # Filter out invalid symbols using the validator
        valid_symbols = []
        for symbol in candidate_symbols:
            if symbol_validator.is_valid_symbol(symbol):
                valid_symbols.append(symbol)
            else:
                # Log why symbol was filtered out (only once per session)
                symbol_info = symbol_validator.get_symbol_info(symbol)
                if symbol_info['is_liquid_swap']:
                    symbol_validator.log_error_once(symbol, "Skipped Liquid Swap token")
                elif symbol_info['is_blacklisted']:
                    symbol_validator.log_error_once(symbol, "Skipped blacklisted symbol")

        return valid_symbols

    async def _process_fetched_data(self, all_data: Dict[str, Any],
                                  refresh_plan: Dict[str, bool]) -> Dict[str, Any]:
        """
        Process and store the fetched data using incremental updates.

        :param all_data: Data fetched from APIs
        :param refresh_plan: Plan indicating what was refreshed
        :return: Processing results
        """
        results = {
            'binance': {'updated': [], 'errors': []},
            'pionex': {'updated': [], 'errors': []},
            'prices': {'updated': 0, 'errors': []},
            'cache_operations': {'saves': 0, 'merges': 0, 'errors': 0}
        }

        try:
            # Process Binance data
            if 'binance_live_positions' in all_data and all_data['binance_live_positions']:
                success = self.storage.save_incremental_data(
                    'binance_live_positions',
                    all_data['binance_live_positions'],
                    {},
                    merge_strategy="update",
                    expiry=self.smart_cache_expiry['positions']
                )
                if success:
                    results['binance']['updated'].append('live_positions')
                    results['cache_operations']['saves'] += 1
                else:
                    results['binance']['errors'].append('Failed to save live positions')
                    results['cache_operations']['errors'] += 1

            if 'binance_spot_snapshot' in all_data and all_data['binance_spot_snapshot']:
                success = self.storage.save_incremental_data(
                    'binance_spot_snapshot',
                    all_data['binance_spot_snapshot'],
                    {},
                    merge_strategy="append",
                    expiry=self.smart_cache_expiry['account_balances']
                )
                if success:
                    results['binance']['updated'].append('spot_snapshot')
                    results['cache_operations']['saves'] += 1
                else:
                    results['binance']['errors'].append('Failed to save spot snapshot')
                    results['cache_operations']['errors'] += 1

            if 'binance_futures_account' in all_data and all_data['binance_futures_account']:
                success = self.storage.save_incremental_data(
                    'binance_futures_account',
                    all_data['binance_futures_account'],
                    {},
                    merge_strategy="update",
                    expiry=self.smart_cache_expiry['account_balances']
                )
                if success:
                    results['binance']['updated'].append('futures_account')
                    results['cache_operations']['saves'] += 1

            # Process historical data with append strategy
            for data_key in ['binance_spot_deposits', 'binance_spot_withdrawals']:
                if data_key in all_data and all_data[data_key]:
                    success = self.storage.save_incremental_data(
                        data_key,
                        all_data[data_key],
                        {},
                        merge_strategy="append",
                        expiry=self.smart_cache_expiry['historical_data']
                    )
                    if success:
                        results['binance']['updated'].append(data_key.replace('binance_', ''))
                        results['cache_operations']['merges'] += 1

            # Process Pionex data
            if 'pionex_active_operations' in all_data and all_data['pionex_active_operations']:
                success = self.storage.save_incremental_data(
                    'pionex_active_operations',
                    all_data['pionex_active_operations'],
                    {},
                    merge_strategy="update",
                    expiry=self.smart_cache_expiry['active_operations']
                )
                if success:
                    results['pionex']['updated'].append('active_operations')
                    results['cache_operations']['saves'] += 1
                else:
                    results['pionex']['errors'].append('Failed to save active operations')
                    results['cache_operations']['errors'] += 1

            if 'pionex_spot_balances' in all_data and all_data['pionex_spot_balances']:
                success = self.storage.save_incremental_data(
                    'pionex_detailed_balances',
                    all_data['pionex_spot_balances'],
                    {},
                    merge_strategy="update",
                    expiry=self.smart_cache_expiry['account_balances']
                )
                if success:
                    results['pionex']['updated'].append('spot_balances')
                    results['cache_operations']['saves'] += 1

            if 'pionex_wallet_movements' in all_data and all_data['pionex_wallet_movements']:
                success = self.storage.save_incremental_data(
                    'pionex_wallet_movements',
                    all_data['pionex_wallet_movements'],
                    {},
                    merge_strategy="append",
                    expiry=self.smart_cache_expiry['historical_data']
                )
                if success:
                    results['pionex']['updated'].append('wallet_movements')
                    results['cache_operations']['merges'] += 1

            # Process price data
            price_count = 0
            for key, value in all_data.items():
                if key.startswith('price_batch_') and value:
                    for symbol, price in value.items():
                        if price is not None:
                            success = self.storage.save_data(
                                'binance_price',
                                price,
                                {'symbol': symbol},
                                expiry=self.smart_cache_expiry['prices']
                            )
                            if success:
                                price_count += 1
                            else:
                                results['prices']['errors'].append(f'Failed to save price for {symbol}')

            results['prices']['updated'] = price_count

        except Exception as e:
            results['cache_operations']['errors'] += 1
            print(f"Error processing fetched data: {e}")

        return results

    def get_refresh_status(self) -> Dict[str, Any]:
        """
        Get the current refresh status and cache health.

        :return: Dictionary with refresh status information
        """
        status = {
            'cache_health': {},
            'data_freshness': {},
            'storage_stats': {},
            'recommendations': []
        }

        try:
            # Get storage statistics
            status['storage_stats'] = self.storage.get_storage_stats()

            # Check data freshness for each category
            for data_type, max_age in self.smart_cache_expiry.items():
                age_info = {}

                if data_type == 'prices':
                    # Check sample price data
                    sample_age = self.storage.get_data_age('binance_price', {'symbol': 'BTCUSDT'})
                    age_info = {
                        'age_seconds': sample_age,
                        'is_fresh': sample_age is not None and sample_age < max_age,
                        'max_age_seconds': max_age
                    }

                elif data_type == 'active_operations':
                    age = self.storage.get_data_age('pionex_active_operations', {})
                    age_info = {
                        'age_seconds': age,
                        'is_fresh': age is not None and age < max_age,
                        'max_age_seconds': max_age
                    }

                # Add more data type checks as needed
                status['data_freshness'][data_type] = age_info

                # Add recommendations
                if age_info.get('age_seconds') and age_info['age_seconds'] > max_age:
                    status['recommendations'].append(f"{data_type} data is stale (age: {age_info['age_seconds']}s)")

            # Cache health assessment
            total_files = status['storage_stats'].get('total_files', 0)
            corrupted_files = status['storage_stats'].get('corrupted_files', 0)

            status['cache_health'] = {
                'total_files': total_files,
                'corrupted_files': corrupted_files,
                'health_percentage': ((total_files - corrupted_files) / max(total_files, 1)) * 100,
                'backup_count': status['storage_stats'].get('backup_count', 0)
            }

            if corrupted_files > 0:
                status['recommendations'].append(f"{corrupted_files} corrupted files detected - consider cache cleanup")

        except Exception as e:
            status['error'] = f"Error getting refresh status: {e}"

        return status

    def cleanup_cache(self) -> Dict[str, int]:
        """
        Clean up expired and corrupted cache data.

        :return: Dictionary with cleanup statistics
        """
        cleanup_stats = {
            'expired_cleaned': 0,
            'corrupted_cleaned': 0,
            'backups_cleaned': 0,
            'errors': 0
        }

        try:
            # Clean up expired data
            cleanup_stats['expired_cleaned'] = self.storage.cleanup_expired_data()

            # If using enhanced storage, clean up old backups
            if hasattr(self.storage.storage, '_cleanup_old_backups'):
                # This would be implemented in the enhanced storage
                pass

        except Exception as e:
            cleanup_stats['errors'] += 1
            print(f"Error during cache cleanup: {e}")

        return cleanup_stats
