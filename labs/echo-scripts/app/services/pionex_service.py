import time
import hmac
import hashlib
import requests
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlencode
from config import PIONEX_API_KEY, PIONEX_API_SECRET
from services.storage import CryptoStorage

class PionexService:
    """
    Service to interact with Pionex API for fetching wallet movements and account data.
    Uses a storage system to cache API responses and reduce API calls.
    """
    def __init__(self):
        self.api_key = PIONEX_API_KEY
        self.api_secret = PIONEX_API_SECRET
        self.base_url = "https://api.pionex.com"
        self.storage = CryptoStorage()

        # Default cache expiry times (in seconds)
        self.cache_expiry = {
            'default': 300,  # 5 minutes
            'balances': 300,  # 5 minutes
            'trades': 600,    # 10 minutes
            'orders': 600,    # 10 minutes
            'movements': 1800,  # 30 minutes
            'bot_history': 1800,  # 30 minutes
            'bot_details': 300  # 5 minutes
        }

        # Print API key status once at initialization
        if not self.api_key or not self.api_secret:
            print("Pionex API keys are not configured. Using cached data if available.")
        elif len(self.api_key) < 10 or len(self.api_secret) < 10:  # Basic validation
            print("Pionex API keys appear to be invalid (too short). Please check your configuration.")

    def _generate_signature(self, method, endpoint, params):
        """
        Generate signature for Pionex API authentication following their documentation.

        :param method: HTTP method (GET, POST, DELETE)
        :param endpoint: API endpoint path
        :param params: Request parameters
        :return: Signature string
        """
        # Step 1: Get timestamp (already in params)
        timestamp = params.get('timestamp')

        # Step 2: Set query parameters as key-value pairs
        # Step 3: Sort parameters by key in ascending ASCII order
        sorted_params = sorted(params.items())

        # Step 4: Create query string
        query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])

        # Step 5: Create PATH_URL
        path_url = f"{endpoint}?{query_string}"

        # Step 6: Concatenate METHOD and PATH_URL
        message = f"{method}{path_url}"

        # Step 7: Generate HMAC SHA256 signature
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def _is_api_configured(self):
        """
        Check if the Pionex API is properly configured.

        :return: True if API keys are configured, False otherwise
        """
        # Check if API key and secret are available and valid
        if not self.api_key or not self.api_secret:
            return False

        # Basic validation - keys should be of reasonable length
        if len(self.api_key) < 10 or len(self.api_secret) < 10:
            return False

        # Check if we've already encountered an API key error
        if self.storage.data_exists('pionex_api_key_error', {}):
            return False

        return True

    def _make_request(self, method, endpoint, params=None):
        """
        Make a request to the Pionex API.
        Handles API key errors gracefully.

        :param method: HTTP method (GET, POST, etc.).
        :param endpoint: API endpoint.
        :param params: Optional parameters.
        :return: Response data.
        """
        # Check if API key and secret are available
        if not self._is_api_configured():
            # Don't print a message here, we already print at initialization
            return {}

        url = f"{self.base_url}{endpoint}"

        if params is None:
            params = {}

        # Add timestamp
        params['timestamp'] = int(time.time() * 1000)

        # Generate signature using the correct method
        signature = self._generate_signature(method, endpoint, params)

        # Set up headers according to Pionex API documentation
        headers = {
            'PIONEX-KEY': self.api_key,
            'PIONEX-SIGNATURE': signature,
            'Content-Type': 'application/json'
        }

        try:
            if method == 'GET':
                response = requests.get(url, params=params, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=params, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            data = response.json()

            if data.get('result') is True:
                return data.get('data', {})
            else:
                # Only print the error once, not repeatedly
                error_code = data.get('code', '')
                if error_code in ['APIKEY_LOST', 'INVALID_APIKEY', 'INVALIE_APIKEY', 'INVALID_SIGNATURE']:
                    # Create a marker to avoid repeated messages
                    if not self.storage.data_exists('pionex_api_key_error', {}):
                        print(f"Pionex API authentication error: {error_code}. Please check your API key configuration.")
                        # Create a marker to avoid repeated messages
                        self.storage.save_data('pionex_api_key_error', True, {}, expiry=86400)  # Cache for 24 hours
                else:
                    print(f"Pionex API error: {data}")
                return {}

        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return {}
        except Exception as e:
            print(f"Error making request to Pionex API: {e}")
            return {}

    def get_account_balances(self):
        """
        Fetches account balances from Pionex.
        Uses cached data if available and not expired.

        :return: List of account balances.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return []

        # Check if data exists in cache
        cache_params = {}
        cached_data = self.storage.load_data('pionex_balances', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex account balances data")
            return cached_data

        # If not in cache, fetch from API
        endpoint = "/api/v1/account/balances"
        data = self._make_request('GET', endpoint)
        balances = data.get('balances', [])

        # Cache the result
        self.storage.save_data(
            'pionex_balances',
            balances,
            cache_params,
            expiry=self.cache_expiry['balances']
        )

        return balances

    def get_enhanced_account_balances(self):
        """
        Get enhanced account balances that include bot and earn account estimates.

        This method addresses the Pionex API limitation where only trading account
        balances are returned, excluding bot and earn account balances.

        :return: List of enhanced account balances including estimated bot/earn balances.
        """
        # Get standard trading account balances from API
        trading_balances = self.get_account_balances()

        # Load balance adjustment data
        import os
        adjustment_file = "data/pionex_balance_adjustment.json"
        if not os.path.exists(adjustment_file):
            # Return standard balances if no adjustment data
            return trading_balances

        try:
            with open(adjustment_file, 'r') as f:
                import json
                adjustment_data = json.load(f)

            # Create enhanced balances
            enhanced_balances = []

            # Process each trading balance and add adjustments
            for balance in trading_balances:
                if isinstance(balance, dict):
                    coin = balance.get('coin', '')
                    trading_free = float(balance.get('free', 0))
                    trading_frozen = float(balance.get('frozen', 0))

                    # Get bot/earn adjustment for this coin
                    bot_earn_amount = adjustment_data.get('bot_earn_account', {}).get(coin, 0)

                    # Create enhanced balance
                    enhanced_balance = {
                        'coin': coin,
                        'free': str(trading_free + bot_earn_amount),  # Add bot/earn to free
                        'frozen': balance.get('frozen', '0'),  # Keep frozen as is
                        'trading_account': {
                            'free': balance.get('free', '0'),
                            'frozen': balance.get('frozen', '0')
                        },
                        'bot_earn_account': str(bot_earn_amount),
                        'enhanced': True
                    }
                    enhanced_balances.append(enhanced_balance)

            print(f"✅ Applied Pionex balance adjustments (bot/earn accounts included)")
            return enhanced_balances

        except Exception as e:
            print(f"Error applying balance adjustment: {e}")
            # Return standard balances on error
            return trading_balances

    def get_trade_fills(self, symbol, start_date=None, end_date=None):
        """
        Fetches trade fills (executed orders) from Pionex.
        Uses cached data if available and not expired.

        :param symbol: Trading pair symbol (e.g., 'BTC_USDT').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :return: List of trade fills.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return []

        # Prepare parameters for API call and cache key
        params = {"symbol": symbol}
        cache_params = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date
        }

        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")

        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_trades', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex trade fills data for {symbol}")
            return cached_data

        # If not in cache, fetch from API
        endpoint = "/api/v1/trade/fills"
        data = self._make_request('GET', endpoint, params)
        fills = data.get('fills', [])

        # Cache the result
        self.storage.save_data(
            'pionex_trades',
            fills,
            cache_params,
            expiry=self.cache_expiry['trades']
        )

        return fills

    def get_active_operations(self):
        """
        Fetches all active operations from Pionex including grid bots, spot orders, etc.
        Uses cached data if available and not expired.

        :return: Dictionary with active operations data.
        """
        # Check if API is configured
        if not self._is_api_configured():
            # Return empty data if API is not configured (no sample data)
            return {
                "grid_bots": [],
                "active_orders": []
            }

        # Prepare cache key
        cache_params = {}

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_active_operations', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex active operations data")
            return cached_data

        # If not in cache, fetch from API
        try:
            # Note: Pionex API doesn't have specific grid bot endpoints
            # Grid bots are managed through the web interface, not API
            # Since we can't fetch real grid bot data, we'll return empty list

            # Get active spot orders for multiple symbols
            symbols = ["BTC_USDT", "ETH_USDT", "BNB_USDT", "INJ_USDT", "SUI_USDT"]
            active_orders = []

            for symbol in symbols:
                try:
                    endpoint = "/api/v1/trade/openOrders"
                    params = {"symbol": symbol}
                    orders_data = self._make_request('GET', endpoint, params)
                    orders = orders_data.get('orders', []) if isinstance(orders_data, dict) else []
                    if isinstance(orders, list):
                        active_orders.extend(orders)
                except Exception as e:
                    print(f"Error fetching active orders for {symbol}: {e}")

            # Return empty grid bots since API doesn't support them and we have no real data
            # This matches the actual state shown in the screenshots (no active grid bots)
            grid_bots = []

            # Combine all active operations
            result = {
                "grid_bots": grid_bots,
                "active_orders": active_orders
            }

            # Cache the result
            self.storage.save_data(
                'pionex_active_operations',
                result,
                cache_params,
                expiry=self.cache_expiry['default']
            )

            return result
        except Exception as e:
            print(f"Error getting Pionex active operations: {e}")
            # Return empty data on error (no sample data)
            return {
                "grid_bots": [],
                "active_orders": []
            }

    def _create_sample_active_operations(self):
        """
        Create sample active operations data for demonstration purposes.

        :return: Dictionary with sample active operations.
        """
        current_time = int(datetime.now().timestamp() * 1000)

        # Sample grid bots
        sample_grid_bots = [
            {
                "id": "12345",
                "name": "BTC-USDT Grid Bot",
                "symbol": "BTC_USDT",
                "status": "RUNNING",
                "created_at": current_time - 7 * ********,  # 7 days ago
                "type": "ARITHMETIC",
                "upper_price": 65000.0,
                "lower_price": 55000.0,
                "grid_num": 20,
                "profit": 125.75,
                "profit_currency": "USDT",
                "investment": {
                    "total": 1000.0,
                    "base_currency": 0.01,  # BTC
                    "quote_currency": 400.0  # USDT
                }
            },
            {
                "id": "12346",
                "name": "ETH-USDT Grid Bot",
                "symbol": "ETH_USDT",
                "status": "RUNNING",
                "created_at": current_time - 14 * ********,  # 14 days ago
                "type": "GEOMETRIC",
                "upper_price": 4000.0,
                "lower_price": 3000.0,
                "grid_num": 15,
                "profit": 87.32,
                "profit_currency": "USDT",
                "investment": {
                    "total": 2000.0,
                    "base_currency": 0.3,  # ETH
                    "quote_currency": 950.0  # USDT
                }
            },
            {
                "id": "12347",
                "name": "BNB-USDT Grid Bot",
                "symbol": "BNB_USDT",
                "status": "RUNNING",
                "created_at": current_time - 5 * ********,  # 5 days ago
                "type": "ARITHMETIC",
                "upper_price": 600.0,
                "lower_price": 400.0,
                "grid_num": 10,
                "profit": 45.18,
                "profit_currency": "USDT",
                "investment": {
                    "total": 1500.0,
                    "base_currency": 1.5,  # BNB
                    "quote_currency": 750.0  # USDT
                }
            }
        ]

        # Sample active orders
        sample_active_orders = [
            {
                "id": "98765",
                "symbol": "BTC_USDT",
                "side": "BUY",
                "type": "LIMIT",
                "price": 58500.0,
                "size": 0.05,
                "status": "NEW",
                "created_at": current_time - 1 * ********  # 1 day ago
            },
            {
                "id": "98766",
                "symbol": "ETH_USDT",
                "side": "SELL",
                "type": "LIMIT",
                "price": 3800.0,
                "size": 0.5,
                "status": "NEW",
                "created_at": current_time - 2 * ********  # 2 days ago
            },
            {
                "id": "98767",
                "symbol": "SUI_USDT",
                "side": "BUY",
                "type": "LIMIT",
                "price": 1.2,
                "size": 500,
                "status": "NEW",
                "created_at": current_time - 12 * 3600000  # 12 hours ago
            }
        ]

        return {
            "grid_bots": sample_grid_bots,
            "active_orders": sample_active_orders
        }

    def get_all_orders(self, symbol, start_date=None, end_date=None):
        """
        Fetches all orders from Pionex.
        Uses cached data if available and not expired.

        :param symbol: Trading pair symbol (e.g., 'BTC_USDT').
        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :return: List of orders.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return []

        # Prepare parameters for API call and cache key
        params = {"symbol": symbol}
        cache_params = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date
        }

        if start_date:
            try:
                params["startTime"] = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['startTime'] = params["startTime"]
            except Exception as e:
                print(f"Error parsing start_date: {e}")

        if end_date:
            try:
                params["endTime"] = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
                cache_params['endTime'] = params["endTime"]
            except Exception as e:
                print(f"Error parsing end_date: {e}")

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_orders', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex orders data for {symbol}")
            return cached_data

        # If not in cache, fetch from API
        endpoint = "/api/v1/trade/allOrders"
        data = self._make_request('GET', endpoint, params)
        orders = data.get('orders', [])

        # Cache the result
        self.storage.save_data(
            'pionex_orders',
            orders,
            cache_params,
            expiry=self.cache_expiry['orders']
        )

        return orders

    def get_wallet_movements(self, start_date=None, end_date=None):
        """
        Combines account balances and trade fills to provide a view of wallet movements.
        This is a workaround since Pionex doesn't have a direct API for wallet movements.
        Uses cached data if available and not expired.

        :param start_date: Optional start date in 'YYYY-MM-DD' format.
        :param end_date: Optional end date in 'YYYY-MM-DD' format.
        :return: Dictionary with balances and recent trades, or a list of trades if that's all that's available.
        """
        # Check if API key and secret are configured
        if not self._is_api_configured():
            # Return empty data if API is not configured
            return {
                "balances": [],
                "recent_trades": []
            }

        # Prepare cache key
        cache_params = {
            'start_date': start_date,
            'end_date': end_date
        }

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_movements', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex wallet movements data")
            return cached_data

        # If not in cache, fetch from API
        try:
            # Get current balances
            balances = self.get_account_balances()

            # Get trades for major pairs
            symbols = ["BTC_USDT", "ETH_USDT", "BNB_USDT", "INJ_USDT", "SUI_USDT", "MUBARAK_USDT"]
            all_trades = []

            # If we're in demo mode or API is not configured, create some sample trades
            if not self._is_api_configured():
                print("Creating sample Pionex trades for demo mode")
                # Create sample trades for demonstration purposes
                current_time = int(datetime.now().timestamp() * 1000)
                sample_trades = [
                    {
                        'symbol': 'BTC_USDT',
                        'side': 'BUY',
                        'price': 60000.0,
                        'size': 0.1,
                        'timestamp': current_time - ********,  # 1 day ago
                        'fee': 0.1,
                        'feeCoin': 'USDT',
                        'id': '12345'
                    },
                    {
                        'symbol': 'ETH_USDT',
                        'side': 'SELL',
                        'price': 3500.0,
                        'size': 1.0,
                        'timestamp': current_time - 172800000,  # 2 days ago
                        'fee': 0.1,
                        'feeCoin': 'USDT',
                        'id': '12346'
                    },
                    {
                        'symbol': 'BNB_USDT',
                        'side': 'BUY',
                        'price': 500.0,
                        'size': 5.0,
                        'timestamp': current_time - 259200000,  # 3 days ago
                        'fee': 0.1,
                        'feeCoin': 'USDT',
                        'id': '12347'
                    }
                ]
                all_trades.extend(sample_trades)
            else:
                # Try to get real trades from API
                for symbol in symbols:
                    try:
                        trades = self.get_trade_fills(symbol, start_date, end_date)
                        if isinstance(trades, list):
                            # Filter out any string items in the list
                            valid_trades = [t for t in trades if isinstance(t, dict)]
                            all_trades.extend(valid_trades)
                        else:
                            print(f"Unexpected trades format for {symbol}: {type(trades)}")
                    except Exception as e:
                        print(f"Error fetching Pionex trades for {symbol}: {e}")

            # Sort trades by timestamp in descending order
            try:
                # Make sure all trades have a timestamp
                valid_trades = []
                for trade in all_trades:
                    if isinstance(trade, dict) and 'timestamp' in trade:
                        valid_trades.append(trade)
                    elif isinstance(trade, dict):
                        # If no timestamp, add current time
                        trade['timestamp'] = int(datetime.now().timestamp() * 1000)
                        valid_trades.append(trade)

                # Sort the valid trades
                valid_trades.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
                all_trades = valid_trades
            except Exception as e:
                print(f"Error sorting Pionex trades: {e}")

            # Limit to most recent 100 trades
            recent_trades = all_trades[:100]

            # Create result
            result = {
                "balances": balances,
                "recent_trades": recent_trades
            }

            # Cache the result
            self.storage.save_data(
                'pionex_movements',
                result,
                cache_params,
                expiry=self.cache_expiry['movements']
            )

            # If balances is empty but we have trades, return just the trades
            if not balances and recent_trades:
                print("No balances available, returning only trades")
                return recent_trades

            return result
        except Exception as e:
            print(f"Error getting Pionex wallet movements: {e}")
            # Return empty data on error
            return {
                "balances": [],
                "recent_trades": []
            }

    def get_bot_details(self, bot_id):
        """
        Fetches detailed information about a specific grid bot.
        Uses cached data if available and not expired.

        :param bot_id: The ID of the grid bot to fetch details for.
        :return: Dictionary with bot details.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return self._create_sample_bot_details(bot_id)

        # Prepare cache key
        cache_params = {
            'bot_id': bot_id
        }

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_bot_details', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex bot details data for bot {bot_id}")
            return cached_data

        # If not in cache, fetch from API
        try:
            endpoint = f"/api/v1/grid/bots/{bot_id}"
            bot_details = self._make_request('GET', endpoint)

            # Cache the result
            self.storage.save_data(
                'pionex_bot_details',
                bot_details,
                cache_params,
                expiry=self.cache_expiry['bot_details']
            )

            return bot_details
        except Exception as e:
            print(f"Error getting Pionex bot details for bot {bot_id}: {e}")
            # Return sample data on error
            return self._create_sample_bot_details(bot_id)

    def _create_sample_bot_details(self, bot_id):
        """
        Create sample bot details data for demonstration purposes.

        :param bot_id: The ID of the bot to create sample details for.
        :return: Dictionary with sample bot details.
        """
        current_time = int(datetime.now().timestamp() * 1000)

        # Create a sample bot based on the bot_id
        if bot_id == "12345" or bot_id.endswith("5"):
            return {
                "id": bot_id,
                "name": "BTC-USDT Grid Bot",
                "symbol": "BTC_USDT",
                "status": "RUNNING",
                "created_at": current_time - 7 * ********,  # 7 days ago
                "type": "ARITHMETIC",
                "upper_price": 65000.0,
                "lower_price": 55000.0,
                "grid_num": 20,
                "profit": 125.75,
                "profit_currency": "USDT",
                "investment": {
                    "total": 1000.0,
                    "base_currency": 0.01,  # BTC
                    "quote_currency": 400.0  # USDT
                },
                "grid_orders": [
                    {"price": 55000.0, "side": "BUY", "status": "FILLED"},
                    {"price": 56500.0, "side": "SELL", "status": "NEW"},
                    {"price": 58000.0, "side": "SELL", "status": "NEW"},
                    {"price": 59500.0, "side": "SELL", "status": "NEW"},
                    {"price": 61000.0, "side": "SELL", "status": "NEW"}
                ],
                "performance": {
                    "daily_profit": 5.25,
                    "weekly_profit": 32.75,
                    "monthly_profit": 125.75,
                    "total_trades": 42,
                    "successful_trades": 38
                }
            }
        elif bot_id == "12346" or bot_id.endswith("6"):
            return {
                "id": bot_id,
                "name": "ETH-USDT Grid Bot",
                "symbol": "ETH_USDT",
                "status": "RUNNING",
                "created_at": current_time - 14 * ********,  # 14 days ago
                "type": "GEOMETRIC",
                "upper_price": 4000.0,
                "lower_price": 3000.0,
                "grid_num": 15,
                "profit": 87.32,
                "profit_currency": "USDT",
                "investment": {
                    "total": 2000.0,
                    "base_currency": 0.3,  # ETH
                    "quote_currency": 950.0  # USDT
                },
                "grid_orders": [
                    {"price": 3000.0, "side": "BUY", "status": "NEW"},
                    {"price": 3200.0, "side": "BUY", "status": "NEW"},
                    {"price": 3400.0, "side": "BUY", "status": "FILLED"},
                    {"price": 3600.0, "side": "SELL", "status": "NEW"},
                    {"price": 3800.0, "side": "SELL", "status": "NEW"}
                ],
                "performance": {
                    "daily_profit": 3.12,
                    "weekly_profit": 21.84,
                    "monthly_profit": 87.32,
                    "total_trades": 35,
                    "successful_trades": 32
                }
            }
        else:
            return {
                "id": bot_id,
                "name": "BNB-USDT Grid Bot",
                "symbol": "BNB_USDT",
                "status": "RUNNING",
                "created_at": current_time - 5 * ********,  # 5 days ago
                "type": "ARITHMETIC",
                "upper_price": 600.0,
                "lower_price": 400.0,
                "grid_num": 10,
                "profit": 45.18,
                "profit_currency": "USDT",
                "investment": {
                    "total": 1500.0,
                    "base_currency": 1.5,  # BNB
                    "quote_currency": 750.0  # USDT
                },
                "grid_orders": [
                    {"price": 400.0, "side": "BUY", "status": "NEW"},
                    {"price": 450.0, "side": "BUY", "status": "NEW"},
                    {"price": 500.0, "side": "SELL", "status": "FILLED"},
                    {"price": 550.0, "side": "SELL", "status": "NEW"},
                    {"price": 600.0, "side": "SELL", "status": "NEW"}
                ],
                "performance": {
                    "daily_profit": 4.52,
                    "weekly_profit": 31.64,
                    "monthly_profit": 45.18,
                    "total_trades": 28,
                    "successful_trades": 25
                }
            }

    def get_bot_history(self, bot_id=None, days=30):
        """
        Fetches historical performance data for grid bots.
        If bot_id is provided, returns history for that specific bot.
        Otherwise, returns history for all bots.
        Uses cached data if available and not expired.

        :param bot_id: Optional ID of the specific bot to fetch history for.
        :param days: Number of days of history to fetch (default: 30).
        :return: Dictionary with bot history data.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return self._create_sample_bot_history(bot_id, days)

        # Prepare cache key
        cache_params = {
            'bot_id': bot_id,
            'days': days
        }

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_bot_history', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex bot history data")
            return cached_data

        # If not in cache, fetch from API
        try:
            # In a real implementation, we would fetch from the API
            # Since we don't have direct access to the Pionex API documentation for this endpoint,
            # we'll use sample data for now

            # For a real implementation, the endpoint might look like:
            # endpoint = f"/api/v1/grid/bots/{bot_id}/history" if bot_id else "/api/v1/grid/bots/history"
            # params = {"days": days}
            # history_data = self._make_request('GET', endpoint, params)

            # For now, use sample data
            history_data = self._create_sample_bot_history(bot_id, days)

            # Cache the result
            self.storage.save_data(
                'pionex_bot_history',
                history_data,
                cache_params,
                expiry=self.cache_expiry['bot_history']
            )

            return history_data
        except Exception as e:
            print(f"Error getting Pionex bot history: {e}")
            # Return sample data on error
            return self._create_sample_bot_history(bot_id, days)

    def _create_sample_bot_history(self, bot_id=None, days=30):
        """
        Create sample bot history data for demonstration purposes.

        :param bot_id: Optional ID of the specific bot to create history for.
        :param days: Number of days of history to create.
        :return: Dictionary with sample bot history data.
        """
        current_time = int(datetime.now().timestamp() * 1000)

        # Create sample history data
        history_data = []

        # If bot_id is provided, create history for just that bot
        if bot_id:
            # Determine which bot we're creating history for
            if bot_id == "12345" or bot_id.endswith("5"):
                symbol = "BTC_USDT"
                base_profit = 4.0
                volatility = 1.5
            elif bot_id == "12346" or bot_id.endswith("6"):
                symbol = "ETH_USDT"
                base_profit = 2.5
                volatility = 1.2
            else:
                symbol = "BNB_USDT"
                base_profit = 1.5
                volatility = 0.8

            # Create daily history entries
            for i in range(days):
                day_timestamp = current_time - (days - i) * ********
                daily_profit = base_profit + (np.random.random() - 0.5) * volatility
                history_data.append({
                    "bot_id": bot_id,
                    "symbol": symbol,
                    "timestamp": day_timestamp,
                    "date": datetime.fromtimestamp(day_timestamp / 1000).strftime("%Y-%m-%d"),
                    "profit": daily_profit,
                    "profit_currency": "USDT",
                    "trades": int(5 + np.random.random() * 10),
                    "cumulative_profit": sum([base_profit + (np.random.random() - 0.5) * volatility for _ in range(i+1)])
                })
        else:
            # Create history for all bots
            bot_configs = [
                {"bot_id": "12345", "symbol": "BTC_USDT", "base_profit": 4.0, "volatility": 1.5},
                {"bot_id": "12346", "symbol": "ETH_USDT", "base_profit": 2.5, "volatility": 1.2},
                {"bot_id": "12347", "symbol": "BNB_USDT", "base_profit": 1.5, "volatility": 0.8}
            ]

            for config in bot_configs:
                bot_history = []
                for i in range(days):
                    day_timestamp = current_time - (days - i) * ********
                    daily_profit = config["base_profit"] + (np.random.random() - 0.5) * config["volatility"]
                    bot_history.append({
                        "bot_id": config["bot_id"],
                        "symbol": config["symbol"],
                        "timestamp": day_timestamp,
                        "date": datetime.fromtimestamp(day_timestamp / 1000).strftime("%Y-%m-%d"),
                        "profit": daily_profit,
                        "profit_currency": "USDT",
                        "trades": int(5 + np.random.random() * 10),
                        "cumulative_profit": sum([config["base_profit"] + (np.random.random() - 0.5) * config["volatility"] for _ in range(i+1)])
                    })
                history_data.extend(bot_history)

        return history_data

    def get_detailed_spot_balances(self):
        """
        Fetches detailed spot wallet balances from Pionex with additional information.
        Uses cached data if available and not expired.

        :return: List of detailed account balances with USD values.
        """
        # Check if API is configured
        if not self._is_api_configured():
            return self._create_sample_detailed_balances()

        # Prepare cache key
        cache_params = {}

        # Check if data exists in cache
        cached_data = self.storage.load_data('pionex_detailed_balances', cache_params)

        if cached_data is not None:
            print(f"Using cached Pionex detailed balances data")
            return cached_data

        # If not in cache, fetch from API
        try:
            # Get basic balances
            balances = self.get_account_balances()

            # Enhance balances with USD values
            # In a real implementation, we would fetch current prices from the API
            # For now, we'll use some hardcoded prices for demonstration
            prices = {
                "BTC": 60000.0,
                "ETH": 3500.0,
                "BNB": 500.0,
                "INJ": 20.0,
                "SUI": 1.5,
                "MUBARAK": 0.5,
                "USDT": 1.0
            }

            detailed_balances = []
            for balance in balances:
                asset = balance.get("asset", "")
                free = float(balance.get("free", 0))
                locked = float(balance.get("locked", 0))
                total = free + locked

                # Calculate USD value
                usd_value = total * prices.get(asset, 0)

                detailed_balances.append({
                    "asset": asset,
                    "free": free,
                    "locked": locked,
                    "total": total,
                    "usd_value": usd_value
                })

            # Sort by USD value (descending)
            detailed_balances.sort(key=lambda x: x.get("usd_value", 0), reverse=True)

            # Cache the result
            self.storage.save_data(
                'pionex_detailed_balances',
                detailed_balances,
                cache_params,
                expiry=self.cache_expiry['balances']
            )

            return detailed_balances
        except Exception as e:
            print(f"Error getting Pionex detailed balances: {e}")
            # Return sample data on error
            return self._create_sample_detailed_balances()

    def _create_sample_detailed_balances(self):
        """
        Create sample detailed balances data for demonstration purposes.

        :return: List of sample detailed balances.
        """
        return [
            {
                "asset": "BTC",
                "free": 0.05,
                "locked": 0.01,
                "total": 0.06,
                "usd_value": 3600.0
            },
            {
                "asset": "ETH",
                "free": 1.2,
                "locked": 0.3,
                "total": 1.5,
                "usd_value": 5250.0
            },
            {
                "asset": "BNB",
                "free": 10.0,
                "locked": 2.0,
                "total": 12.0,
                "usd_value": 6000.0
            },
            {
                "asset": "INJ",
                "free": 100.0,
                "locked": 50.0,
                "total": 150.0,
                "usd_value": 3000.0
            },
            {
                "asset": "SUI",
                "free": 2000.0,
                "locked": 500.0,
                "total": 2500.0,
                "usd_value": 3750.0
            },
            {
                "asset": "MUBARAK",
                "free": 5000.0,
                "locked": 1000.0,
                "total": 6000.0,
                "usd_value": 3000.0
            },
            {
                "asset": "USDT",
                "free": 10000.0,
                "locked": 2000.0,
                "total": 12000.0,
                "usd_value": 12000.0
            }
        ]