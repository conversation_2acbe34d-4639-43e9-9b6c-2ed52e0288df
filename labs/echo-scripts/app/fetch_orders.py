import abc
import time
import os
from binance.client import Client
from binance.exceptions import Binance<PERSON>IException, BinanceRequestException
from tabulate import tabulate
from dotenv import load_dotenv
from services.portfolio_service import PortfolioService
from services.pionex_service import PionexService
from services.binance_service import BinanceService

# Load environment variables from .env file
load_dotenv()

class CryptoExchange(abc.ABC):
    """
    Abstract base class to fetch closed futures orders from different exchanges.
    """
    def __init__(self, api_key, api_secret):
        self.api_key = api_key
        self.api_secret = api_secret

    @abc.abstractmethod
    def get_closed_futures_orders(self, symbols, max_pages=20, page_size=100):
        pass

    @abc.abstractmethod
    def get_current_price(self, symbol):
        pass

class BinanceExchange(CryptoExchange):
    """
    Implementation to fetch closed futures orders from Binance using python-binance.
    """
    def __init__(self, api_key, api_secret):
        super().__init__(api_key, api_secret)
        self.client = Client(api_key, api_secret)

    def get_closed_futures_orders(self, symbols, max_pages=20, page_size=100):
        closed_orders = []
        for symbol in symbols:
            try:
                orders = []
                last_order_id = None
                for _ in range(max_pages):
                    if last_order_id:
                        new_orders = self.client.futures_get_all_orders(
                            symbol=symbol,
                            limit=page_size,
                            orderId=last_order_id,
                        )
                    else:
                        new_orders = self.client.futures_get_all_orders(
                            symbol=symbol,
                            limit=page_size,
                        )
                    if not new_orders:
                        break
                    orders.extend(new_orders)
                    last_order_id = new_orders[-1]['orderId'] + 1
                    time.sleep(0.2)  # To respect rate limits
                closed_orders.extend(orders)
            except BinanceAPIException as e:
                print(f"Binance API Exception for {symbol}: {e}")
            except BinanceRequestException as e:
                print(f"Binance Request Exception for {symbol}: {e}")
            except Exception as e:
                print(f"General Exception for {symbol}: {e}")
        return closed_orders

    def get_current_price(self, symbol):
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except BinanceAPIException as e:
            print(f"Binance API Exception for {symbol}: {e}")
        except BinanceRequestException as e:
            print(f"Binance Request Exception for {symbol}: {e}")
        except Exception as e:
            print(f"General Exception for {symbol}: {e}")
        return None

class CryptoOrderFetcher:
    """
    Class to handle multiple exchanges and display closed orders in a table.
    """
    def __init__(self, exchanges, symbols, max_pages=20, page_size=100):
        self.exchanges = exchanges
        self.symbols = symbols
        self.max_pages = max_pages
        self.page_size = page_size

    def display_closed_orders(self):
        all_orders = []
        for exchange in self.exchanges:
            try:
                orders = exchange.get_closed_futures_orders(
                    self.symbols,
                    max_pages=self.max_pages,
                    page_size=self.page_size
                )
                for order in orders:
                    symbol = order.get("symbol")
                    executed_qty = float(order.get("executedQty", 0))
                    avg_price = float(order.get("avgPrice", 0))
                    side = order.get("side")
                    current_price = exchange.get_current_price(symbol)

                    if executed_qty == 0 or avg_price == 0 or current_price is None:
                        continue

                    invested_usd = executed_qty * avg_price
                    if side == "BUY":
                        pnl = (current_price - avg_price) * executed_qty
                    else:  # SELL
                        pnl = (avg_price - current_price) * executed_qty
                    roi = (pnl / invested_usd) * 100

                    all_orders.append([
                        exchange.__class__.__name__,
                        symbol,
                        order.get("orderId"),
                        side,
                        avg_price,
                        executed_qty,
                        invested_usd,
                        pnl,
                        roi,
                        order.get("status")
                    ])
            except Exception as e:
                print(f"Error with {exchange.__class__.__name__}: {e}")
        if all_orders:
            print(tabulate(all_orders, headers=[
                "Exchange", "Symbol", "Order ID", "Side", "Avg Price", "Quantity",
                "Invested USD", "PnL", "ROI (%)", "Status"
            ], tablefmt="grid"))
        else:
            print("No closed orders found for the specified symbols.")

def fetch_pionex_data():
    """
    Fetch and display Pionex active operations data.
    """
    print("\n" + "="*80)
    print("FETCHING PIONEX DATA")
    print("="*80)

    try:
        # Initialize Pionex service
        pionex_service = PionexService()

        # Fetch active operations (grid bots and active orders)
        print("Fetching Pionex active operations...")
        active_operations = pionex_service.get_active_operations()

        # Display grid bots
        grid_bots = active_operations.get("grid_bots", [])
        print(f"\nFound {len(grid_bots)} active grid bots:")

        if grid_bots:
            grid_bot_data = []
            for bot in grid_bots:
                investment = bot.get("investment", {})
                grid_bot_data.append([
                    bot.get("id", "N/A"),
                    bot.get("name", "N/A"),
                    bot.get("symbol", "N/A").replace("_", "/"),
                    bot.get("status", "N/A"),
                    f"${bot.get('profit', 0):,.2f}",
                    f"${investment.get('total', 0):,.2f}",
                    bot.get("grid_num", 0)
                ])

            print(tabulate(grid_bot_data, headers=[
                "Bot ID", "Name", "Symbol", "Status", "Profit", "Investment", "Grids"
            ], tablefmt="grid"))
        else:
            print("No active grid bots found.")

        # Display active orders
        active_orders = active_operations.get("active_orders", [])
        print(f"\nFound {len(active_orders)} active orders:")

        if active_orders:
            order_data = []
            for order in active_orders:
                order_data.append([
                    order.get("id", "N/A"),
                    order.get("symbol", "N/A").replace("_", "/"),
                    order.get("side", "N/A"),
                    order.get("type", "N/A"),
                    f"${order.get('price', 0):,.2f}",
                    order.get("size", 0),
                    order.get("status", "N/A")
                ])

            print(tabulate(order_data, headers=[
                "Order ID", "Symbol", "Side", "Type", "Price", "Size", "Status"
            ], tablefmt="grid"))
        else:
            print("No active orders found.")

        # Fetch wallet balances
        print("\nFetching Pionex wallet balances...")
        balances = pionex_service.get_detailed_spot_balances()

        if balances:
            # Filter out zero balances
            non_zero_balances = [b for b in balances if b.get("total", 0) > 0]
            print(f"Found {len(non_zero_balances)} assets with non-zero balances:")

            if non_zero_balances:
                balance_data = []
                for balance in non_zero_balances:
                    balance_data.append([
                        balance.get("coin", "N/A"),
                        balance.get("free", 0),
                        balance.get("frozen", 0),
                        balance.get("total", 0),
                        f"${balance.get('usd_value', 0):,.2f}"
                    ])

                print(tabulate(balance_data, headers=[
                    "Asset", "Free", "Frozen", "Total", "USD Value"
                ], tablefmt="grid"))
            else:
                print("No assets with non-zero balances found.")
        else:
            print("No wallet balances found.")

    except Exception as e:
        print(f"Error fetching Pionex data: {e}")

def fetch_portfolio_summary():
    """
    Fetch and display portfolio summary using the portfolio service.
    """
    print("\n" + "="*80)
    print("PORTFOLIO SUMMARY")
    print("="*80)

    try:
        # Initialize portfolio service
        portfolio_service = PortfolioService()

        # Get portfolio summary
        summary = portfolio_service.get_portfolio_summary()

        print(f"Total Portfolio Value: ${summary['total_value']:,.2f}")
        print(f"Total Invested: ${summary['total_cost']:,.2f}")
        print(f"Total P&L: ${summary['total_profit_loss']:,.2f}")
        print(f"Total ROI: {summary['total_roi']:.2f}%")
        print(f"Number of Assets: {summary['asset_count']}")

        # Get active operations summary
        active_ops = portfolio_service.get_active_operations()
        pionex_grid_bots = active_ops.get("pionex", {}).get("grid_bots", [])
        pionex_orders = active_ops.get("pionex", {}).get("active_orders", [])

        print(f"\nActive Operations:")
        print(f"  Pionex Grid Bots: {len(pionex_grid_bots)}")
        print(f"  Pionex Active Orders: {len(pionex_orders)}")

        if pionex_grid_bots:
            total_bot_profit = sum(bot.get("profit", 0) for bot in pionex_grid_bots)
            total_bot_investment = sum(bot.get("investment", {}).get("total", 0) for bot in pionex_grid_bots)
            print(f"  Total Grid Bot Profit: ${total_bot_profit:,.2f}")
            print(f"  Total Grid Bot Investment: ${total_bot_investment:,.2f}")
            if total_bot_investment > 0:
                bot_roi = (total_bot_profit / total_bot_investment) * 100
                print(f"  Grid Bot ROI: {bot_roi:.2f}%")

    except Exception as e:
        print(f"Error fetching portfolio summary: {e}")

if __name__ == "__main__":
    # Load API credentials from environment variables
    binance_api_key = os.getenv("BINANCE_API_KEY")
    binance_api_secret = os.getenv("BINANCE_API_SECRET")

    # Define the symbols to fetch orders for
    binance_symbols = ["BTCUSDC", "SOLUSDC", "ETHUSDC", "SUSDC", "HBARUSDC", "XRPUSDC", "ADAUSDC", "INJUSDT", "SUIUSDT", "MUBARAKUSDT"]

    # Initialize exchange instances with injected API credentials
    binance = BinanceExchange(api_key=binance_api_key, api_secret=binance_api_secret)

    # Create an instance of the handler class for Binance
    binance_fetcher = CryptoOrderFetcher([binance], binance_symbols)
    print("Binance Closed Orders:")
    binance_fetcher.display_closed_orders()

    # Fetch Pionex data
    fetch_pionex_data()

    # Fetch portfolio summary
    fetch_portfolio_summary()



