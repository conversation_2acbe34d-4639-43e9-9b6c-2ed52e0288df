import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
from services.binance_service import BinanceService
from services.pionex_service import PionexService

def show_wallet_movements(start_date, end_date):
    """
    Display wallet movements for Binance (Futures and Spot) and Pionex.

    :param start_date: Start date in 'YYYY-MM-DD' format.
    :param end_date: End date in 'YYYY-MM-DD' format.
    """
    st.header("Wallet Movements")

    # Check if running in demo mode
    from config import BINANCE_API_KEY, BINANCE_API_SECRET, PIONEX_API_KEY, PIONEX_API_SECRET

    # Check API key configurations with basic validation
    binance_configured = BINANCE_API_KEY and BINANCE_API_SECRET and len(BINANCE_API_KEY) > 10 and len(BINANCE_API_SECRET) > 10
    pionex_configured = PIONEX_API_KEY and PIONEX_API_SECRET and len(PIONEX_API_KEY) > 10 and len(PIONEX_API_SECRET) > 10

    if not binance_configured and not pionex_configured:
        st.warning("⚠️ Running in demo mode: No API keys configured. Some features may be limited and data may be simulated.")
    elif not binance_configured:
        st.info("ℹ️ Binance API is not properly configured. Binance data will not be available. Please check your .env file.")
    elif not pionex_configured:
        st.info("ℹ️ Pionex API is not properly configured. Pionex data will not be available. Please check your .env file.")

    # Check if date range is within 90 days for Binance API
    from datetime import datetime, timedelta
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
    date_diff = (end_datetime - start_datetime).days

    if date_diff > 90:
        st.warning(f"⚠️ The selected date range ({date_diff} days) exceeds Binance's 90-day limit. Some data may not be available.")

    # Initialize services
    binance_service = BinanceService()
    pionex_service = PionexService()

    # Create tabs for different exchanges
    tabs = st.tabs(["Binance Spot", "Binance Futures", "Pionex"])

    # Binance Spot tab
    with tabs[0]:
        st.subheader("Binance Spot Wallet Movements")

        # Fetch deposit and withdrawal history
        with st.spinner("Fetching Binance Spot deposit history..."):
            deposits = binance_service.get_spot_deposit_history(start_date=start_date, end_date=end_date)

        with st.spinner("Fetching Binance Spot withdrawal history..."):
            withdrawals = binance_service.get_spot_withdraw_history(start_date=start_date, end_date=end_date)

        # Display deposits
        st.subheader("Deposits")
        if deposits:
            df_deposits = pd.DataFrame(deposits)
            # Convert timestamp to datetime
            if 'insertTime' in df_deposits.columns:
                # Check if insertTime is already a string date or a timestamp
                sample_value = df_deposits['insertTime'].iloc[0] if not df_deposits.empty else None

                if sample_value and isinstance(sample_value, str) and '-' in sample_value:
                    # If it's already a formatted date string
                    df_deposits['insertTime'] = pd.to_datetime(df_deposits['insertTime'])
                else:
                    # If it's a timestamp in milliseconds
                    try:
                        df_deposits['insertTime'] = pd.to_datetime(df_deposits['insertTime'], unit='ms')
                    except ValueError:
                        # If conversion fails, try without specifying unit
                        df_deposits['insertTime'] = pd.to_datetime(df_deposits['insertTime'])

                df_deposits.rename(columns={'insertTime': 'Date'}, inplace=True)

            # Select and reorder columns
            columns_to_display = ['Date', 'coin', 'amount', 'status', 'address', 'txId']
            df_deposits = df_deposits[[col for col in columns_to_display if col in df_deposits.columns]]

            st.dataframe(df_deposits)
        else:
            st.info("No deposit history found for the selected period.")

        # Display withdrawals
        st.subheader("Withdrawals")
        if withdrawals:
            df_withdrawals = pd.DataFrame(withdrawals)
            # Convert timestamp to datetime
            if 'applyTime' in df_withdrawals.columns:
                # Check if applyTime is already a string date or a timestamp
                sample_value = df_withdrawals['applyTime'].iloc[0] if not df_withdrawals.empty else None

                if sample_value and isinstance(sample_value, str) and '-' in sample_value:
                    # If it's already a formatted date string
                    df_withdrawals['applyTime'] = pd.to_datetime(df_withdrawals['applyTime'])
                else:
                    # If it's a timestamp in milliseconds
                    try:
                        df_withdrawals['applyTime'] = pd.to_datetime(df_withdrawals['applyTime'], unit='ms')
                    except ValueError:
                        # If conversion fails, try without specifying unit
                        df_withdrawals['applyTime'] = pd.to_datetime(df_withdrawals['applyTime'])

                df_withdrawals.rename(columns={'applyTime': 'Date'}, inplace=True)

            # Select and reorder columns
            columns_to_display = ['Date', 'coin', 'amount', 'status', 'address', 'txId']
            df_withdrawals = df_withdrawals[[col for col in columns_to_display if col in df_withdrawals.columns]]

            st.dataframe(df_withdrawals)
        else:
            st.info("No withdrawal history found for the selected period.")

        # Get account snapshot
        with st.spinner("Fetching Binance Spot account snapshot..."):
            snapshots = binance_service.get_spot_account_snapshot(start_date=start_date, end_date=end_date)

        # Display account snapshot
        st.subheader("Account Snapshot")
        if snapshots:
            # Extract balances from snapshots
            all_balances = []
            for snapshot in snapshots:
                # Handle updateTime which could be a timestamp or a string
                update_time = snapshot.get('updateTime', 0)

                if isinstance(update_time, str) and '-' in update_time:
                    # If it's already a formatted date string
                    snapshot_time = datetime.fromisoformat(update_time.replace('Z', '+00:00'))
                else:
                    # If it's a timestamp
                    try:
                        # Try to convert to int first in case it's a string representation of a number
                        if isinstance(update_time, str):
                            update_time = int(update_time)
                        snapshot_time = datetime.fromtimestamp(update_time / 1000)
                    except (ValueError, TypeError):
                        # If conversion fails, use current time
                        snapshot_time = datetime.now()
                balances = snapshot.get('data', {}).get('balances', [])
                for balance in balances:
                    if float(balance.get('free', 0)) > 0 or float(balance.get('locked', 0)) > 0:
                        all_balances.append({
                            'Date': snapshot_time,
                            'Coin': balance.get('asset', ''),
                            'Free': float(balance.get('free', 0)),
                            'Locked': float(balance.get('locked', 0)),
                            'Total': float(balance.get('free', 0)) + float(balance.get('locked', 0))
                        })

            if all_balances:
                df_balances = pd.DataFrame(all_balances)
                st.dataframe(df_balances)
            else:
                st.info("No balance data found in the snapshots.")
        else:
            st.info("No account snapshots found for the selected period.")

    # Binance Futures tab
    with tabs[1]:
        st.subheader("Binance Futures Wallet Movements")

        # Fetch futures wallet transfers
        with st.spinner("Fetching Binance Futures wallet transfers..."):
            transfers = binance_service.get_futures_wallet_transfers(start_date=start_date, end_date=end_date)

        # Display transfers
        st.subheader("Spot-Futures Transfers")
        if transfers:
            df_transfers = pd.DataFrame(transfers)
            # Convert timestamp to datetime
            if 'timestamp' in df_transfers.columns:
                # Check if timestamp is already a string date or a timestamp
                sample_value = df_transfers['timestamp'].iloc[0] if not df_transfers.empty else None

                if sample_value and isinstance(sample_value, str) and '-' in sample_value:
                    # If it's already a formatted date string
                    df_transfers['timestamp'] = pd.to_datetime(df_transfers['timestamp'])
                else:
                    # If it's a timestamp in milliseconds
                    try:
                        df_transfers['timestamp'] = pd.to_datetime(df_transfers['timestamp'], unit='ms')
                    except ValueError:
                        # If conversion fails, try without specifying unit
                        df_transfers['timestamp'] = pd.to_datetime(df_transfers['timestamp'])

                df_transfers.rename(columns={'timestamp': 'Date'}, inplace=True)

            # Map transfer type to readable format
            type_map = {
                1: "Spot to USDT-M Futures",
                2: "USDT-M Futures to Spot",
                3: "Spot to COIN-M Futures",
                4: "COIN-M Futures to Spot"
            }
            if 'type' in df_transfers.columns:
                df_transfers['type'] = df_transfers['type'].map(lambda x: type_map.get(x, x))
                df_transfers.rename(columns={'type': 'Transfer Type'}, inplace=True)

            # Select and reorder columns
            columns_to_display = ['Date', 'asset', 'amount', 'Transfer Type', 'status']
            df_transfers = df_transfers[[col for col in columns_to_display if col in df_transfers.columns]]

            st.dataframe(df_transfers)
        else:
            st.info("No futures wallet transfers found for the selected period.")

        # Get futures income history
        with st.spinner("Fetching Binance Futures income history..."):
            income = binance_service.get_income_history(start_date=start_date, end_date=end_date)

        # Display income history
        st.subheader("Income History")
        if income:
            df_income = pd.DataFrame(income)
            # Convert timestamp to datetime
            if 'time' in df_income.columns:
                # Check if time is already a string date or a timestamp
                sample_value = df_income['time'].iloc[0] if not df_income.empty else None

                if sample_value and isinstance(sample_value, str) and '-' in sample_value:
                    # If it's already a formatted date string
                    df_income['time'] = pd.to_datetime(df_income['time'])
                else:
                    # If it's a timestamp in milliseconds
                    try:
                        df_income['time'] = pd.to_datetime(df_income['time'], unit='ms')
                    except ValueError:
                        # If conversion fails, try without specifying unit
                        df_income['time'] = pd.to_datetime(df_income['time'])

                df_income.rename(columns={'time': 'Date'}, inplace=True)

            # Select and reorder columns
            columns_to_display = ['Date', 'symbol', 'incomeType', 'income', 'asset']
            df_income = df_income[[col for col in columns_to_display if col in df_income.columns]]

            st.dataframe(df_income)
        else:
            st.info("No income history found for the selected period.")

        # Get futures account snapshot
        with st.spinner("Fetching Binance Futures account snapshot..."):
            futures_snapshots = binance_service.get_futures_account_snapshot(start_date=start_date, end_date=end_date)

        # Display futures account snapshot
        st.subheader("Futures Account Snapshot")
        if futures_snapshots:
            # Extract assets from snapshots
            all_assets = []
            for snapshot in futures_snapshots:
                # Handle updateTime which could be a timestamp or a string
                update_time = snapshot.get('updateTime', 0)

                if isinstance(update_time, str) and '-' in update_time:
                    # If it's already a formatted date string
                    snapshot_time = datetime.fromisoformat(update_time.replace('Z', '+00:00'))
                else:
                    # If it's a timestamp
                    try:
                        # Try to convert to int first in case it's a string representation of a number
                        if isinstance(update_time, str):
                            update_time = int(update_time)
                        snapshot_time = datetime.fromtimestamp(update_time / 1000)
                    except (ValueError, TypeError):
                        # If conversion fails, use current time
                        snapshot_time = datetime.now()
                assets = snapshot.get('data', {}).get('assets', [])
                for asset in assets:
                    if float(asset.get('walletBalance', 0)) > 0:
                        all_assets.append({
                            'Date': snapshot_time,
                            'Asset': asset.get('asset', ''),
                            'Wallet Balance': float(asset.get('walletBalance', 0)),
                            'Unrealized PNL': float(asset.get('unrealizedProfit', 0)),
                            'Margin Balance': float(asset.get('marginBalance', 0))
                        })

            if all_assets:
                df_assets = pd.DataFrame(all_assets)
                st.dataframe(df_assets)
            else:
                st.info("No asset data found in the futures snapshots.")
        else:
            st.info("No futures account snapshots found for the selected period.")

    # Pionex tab
    with tabs[2]:
        st.subheader("Pionex Wallet Movements")

        # Fetch Pionex wallet movements
        with st.spinner("Fetching Pionex wallet movements..."):
            pionex_movements = pionex_service.get_wallet_movements(start_date=start_date, end_date=end_date)

        # Display balances
        st.subheader("Current Balances")
        balances = pionex_movements.get('balances', [])
        if balances:
            df_balances = pd.DataFrame(balances)
            # Convert string values to float
            for col in ['free', 'frozen']:
                if col in df_balances.columns:
                    df_balances[col] = df_balances[col].astype(float)

            # Add total column
            if 'free' in df_balances.columns and 'frozen' in df_balances.columns:
                df_balances['total'] = df_balances['free'] + df_balances['frozen']

            # Filter out zero balances
            df_balances = df_balances[(df_balances['free'] > 0) | (df_balances['frozen'] > 0)]

            st.dataframe(df_balances)
        else:
            st.info("No balance data found.")

        # Display recent trades
        st.subheader("Recent Trades")
        trades = pionex_movements.get('recent_trades', [])
        if trades:
            df_trades = pd.DataFrame(trades)
            # Convert timestamp to datetime
            if 'timestamp' in df_trades.columns:
                # Check if timestamp is already a string date or a timestamp
                sample_value = df_trades['timestamp'].iloc[0] if not df_trades.empty else None

                if sample_value and isinstance(sample_value, str) and '-' in sample_value:
                    # If it's already a formatted date string
                    df_trades['timestamp'] = pd.to_datetime(df_trades['timestamp'])
                else:
                    # If it's a timestamp in milliseconds
                    try:
                        df_trades['timestamp'] = pd.to_datetime(df_trades['timestamp'], unit='ms')
                    except ValueError:
                        # If conversion fails, try without specifying unit
                        df_trades['timestamp'] = pd.to_datetime(df_trades['timestamp'])

                df_trades.rename(columns={'timestamp': 'Date'}, inplace=True)

            # Convert string values to float
            for col in ['price', 'size', 'fee']:
                if col in df_trades.columns:
                    df_trades[col] = df_trades[col].astype(float)

            # Select and reorder columns
            columns_to_display = ['Date', 'symbol', 'side', 'role', 'price', 'size', 'fee', 'feeCoin']
            df_trades = df_trades[[col for col in columns_to_display if col in df_trades.columns]]

            st.dataframe(df_trades)
        else:
            st.info("No recent trades found for the selected period.")
