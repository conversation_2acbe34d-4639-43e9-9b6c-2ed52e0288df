import streamlit as st
import pandas as pd
import plotly.express as px
from services.binance_service import BinanceService
from services.data_processor import calculate_pnl


def plot_cumulative_pnl(df):
    """Plots the cumulative PnL over time ensuring continuous lines and better time spacing."""
    df_sorted = df.sort_values(by="Close Time")
    df_sorted["Cumulative PnL"] = df_sorted["PnL"].cumsum()

    # Convert timestamps to numeric for better spacing
    df_sorted["Time Numeric"] = df_sorted["Close Time"].astype(int) / 10 ** 9  # Convert to seconds

    # Create an interactive line chart with continuous lines
    fig = px.line(
        df_sorted,
        x="Close Time",
        y="Cumulative PnL",
        title="Cumulative PnL Over Time (Closed Orders)",
        markers=True,
        line_shape="linear"  # Ensure continuous straight lines
    )

    # Customize layout
    fig.update_traces(line=dict(color="green"))
    fig.update_layout(
        xaxis_title="Close Time",
        yaxis_title="Cumulative PnL (USD)",
        xaxis=dict(
            showgrid=True,
            tickformat="%b %d, %H:%M",  # Formatting timestamps
            tickangle=-45,
            tickmode="auto"
        ),
        yaxis=dict(showgrid=True)
    )

    st.plotly_chart(fig)


def show_closed_orders(start_date, end_date):
    st.header("Closed Orders")

    binance = BinanceService()
    symbols = ["BTCUSDC", "SOLUSDC", "ETHUSDC", "HBARUSDC", "XRPUSDC", "ADAUSDC", "INJUSDT", "SUIUSDT", "MUBARAKUSDT"]

    all_income_records = []

    # Optionally, specify the end date; if omitted, it defaults to the current date
    end_date = '2025-03-16'

    closed_orders = binance.get_closed_futures_orders(symbols, start_date, end_date)

    if not closed_orders:
        st.warning("No closed orders found.")
        return

    order_data = []
    for order in closed_orders:
        symbol = order.get("symbol")
        current_price = binance.get_current_price(symbol)
        pnl, roi = calculate_pnl(order, current_price)

        order_data.append([
            symbol,
            order.get("orderId"),
            order.get("side"),
            float(order.get("avgPrice", 0)),
            float(order.get("executedQty", 0)),
            pnl,
            roi,
            order.get("status"),
            order.get("updateTime"),
        ])

    df = pd.DataFrame(order_data, columns=[
        "Symbol", "Order ID", "Side", "Avg Price", "Quantity", "PnL", "ROI (%)", "Status", "Close Time"
    ])
    df["Close Time"] = pd.to_datetime(df["Close Time"], unit="ms")
    df = df.sort_values(by="Close Time", ascending=False)

    # Show plot first
    plot_cumulative_pnl(df)

    # Display dataframe
    st.dataframe(df)
