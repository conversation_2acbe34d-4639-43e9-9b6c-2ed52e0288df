import streamlit as st
import pandas as pd
from datetime import datetime
from services.portfolio_service import PortfolioService

def show_active_operations():
    """
    Display active operations from all platforms.
    """
    st.header("Active Operations")

    # Initialize services
    portfolio_service = PortfolioService()

    # Get active operations
    operations = portfolio_service.get_active_operations()

    # Display Pionex Grid Bots
    st.subheader("Pionex Grid Bots")

    pionex_grid_bots = operations.get("pionex", {}).get("grid_bots", [])

    if not pionex_grid_bots:
        st.info("No active grid bots found.")
    else:
        # Create a DataFrame for grid bots
        grid_bot_data = []
        for bot in pionex_grid_bots:
            created_at = datetime.fromtimestamp(bot.get("created_at", 0) / 1000).strftime("%Y-%m-%d") if bot.get("created_at") else "Unknown"

            # Extract investment details
            investment = bot.get("investment", {})
            total_investment = investment.get("total", 0)

            grid_bot_data.append({
                "Name": bot.get("name", "Unknown"),
                "Symbol": bot.get("symbol", "Unknown").replace("_", "/"),
                "Status": bot.get("status", "Unknown"),
                "Type": bot.get("type", "Unknown").capitalize(),
                "Upper Price": f"${bot.get('upper_price', 0):,.2f}",
                "Lower Price": f"${bot.get('lower_price', 0):,.2f}",
                "Grids": bot.get("grid_num", 0),
                "Profit": f"${bot.get('profit', 0):,.2f}",
                "Investment": f"${total_investment:,.2f}",
                "Created": created_at
            })

        # Display grid bots as a table
        df_grid_bots = pd.DataFrame(grid_bot_data)
        st.dataframe(df_grid_bots, use_container_width=True)

        # Calculate total profit and investment
        total_profit = sum(bot.get("profit", 0) for bot in pionex_grid_bots)
        total_investment = sum(bot.get("investment", {}).get("total", 0) for bot in pionex_grid_bots)

        # Display summary metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Grid Bots", len(pionex_grid_bots))
        with col2:
            st.metric("Total Profit", f"${total_profit:,.2f}")
        with col3:
            if total_investment > 0:
                roi = (total_profit / total_investment) * 100
                st.metric("ROI", f"{roi:.2f}%")
            else:
                st.metric("ROI", "N/A")

    # Display Pionex Active Orders
    st.subheader("Pionex Active Orders")

    pionex_orders = operations.get("pionex", {}).get("active_orders", [])

    if not pionex_orders:
        st.info("No active orders found.")
    else:
        # Create a DataFrame for active orders
        order_data = []
        for order in pionex_orders:
            created_at = datetime.fromtimestamp(order.get("created_at", 0) / 1000).strftime("%Y-%m-%d %H:%M") if order.get("created_at") else "Unknown"

            # Calculate total value
            price = order.get("price", 0)
            size = order.get("size", 0)
            total_value = price * size

            order_data.append({
                "Symbol": order.get("symbol", "Unknown").replace("_", "/"),
                "Side": order.get("side", "Unknown"),
                "Type": order.get("type", "Unknown"),
                "Price": f"${price:,.2f}",
                "Size": size,
                "Total": f"${total_value:,.2f}",
                "Status": order.get("status", "Unknown"),
                "Created": created_at
            })

        # Display active orders as a table
        df_orders = pd.DataFrame(order_data)
        st.dataframe(df_orders, use_container_width=True)

        # Calculate total order value
        total_order_value = sum(order.get("price", 0) * order.get("size", 0) for order in pionex_orders)

        # Display summary metrics
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Total Active Orders", len(pionex_orders))
        with col2:
            st.metric("Total Order Value", f"${total_order_value:,.2f}")

    # Display Binance Active Orders (if implemented)
    binance_orders = operations.get("binance", {}).get("active_orders", [])

    if binance_orders:
        st.subheader("Binance Active Orders")
        # Implementation for Binance orders would go here

    # Add refresh button
    if st.button("Refresh Data", type="primary"):
        st.rerun()
