import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import os
from datetime import datetime, timedelta
from services.portfolio_service import PortfolioService
from services.storage import CryptoStorage

def is_demo_mode():
    """
    Check if the dashboard is running in demo mode (without API keys).

    :return: True if in demo mode, False otherwise
    """
    from config import BINANCE_API_KEY, BINANCE_API_SECRET, PIONEX_API_KEY, PIONEX_API_SECRET

    binance_configured = BINANCE_API_KEY and BINANCE_API_SECRET
    pionex_configured = PIONEX_API_KEY and PIONEX_API_SECRET

    return not (binance_configured or pionex_configured)

def show_dashboard(start_date, end_date):
    """
    Display the main dashboard with portfolio overview and analytics.

    :param start_date: Start date in 'YYYY-MM-DD' format.
    :param end_date: End date in 'YYYY-MM-DD' format.
    """
    st.header("Crypto Investment Dashboard")

    # Check if running in demo mode
    demo_mode = is_demo_mode()
    if demo_mode:
        st.warning("⚠️ Running in demo mode: No API keys configured. Sample data is being used for demonstration purposes.")
        st.info("The dashboard is fully functional with sample data. You can explore all features as if you were using real data.")

    # Check for specific API configurations
    from config import BINANCE_API_KEY, BINANCE_API_SECRET, PIONEX_API_KEY, PIONEX_API_SECRET

    # Check API key configurations
    pionex_configured = PIONEX_API_KEY and PIONEX_API_SECRET and len(PIONEX_API_KEY) > 10 and len(PIONEX_API_SECRET) > 10
    binance_configured = BINANCE_API_KEY and BINANCE_API_SECRET and len(BINANCE_API_KEY) > 10 and len(BINANCE_API_SECRET) > 10

    if not pionex_configured:
        st.info("ℹ️ Pionex API is not properly configured. Pionex data will not be available. Please check your .env file.")

    if not binance_configured:
        st.info("ℹ️ Binance API is not properly configured. Binance data will not be available. Please check your .env file.")

    # Initialize services
    portfolio_service = PortfolioService()
    storage = CryptoStorage()

    # Add cache management in the sidebar
    with st.sidebar:
        st.subheader("Cache Management")
        st.caption("The dashboard uses a local cache to reduce API calls and improve performance.")

        # Get cache statistics
        cache_categories = storage.get_categories()
        cache_keys = []
        for category in cache_categories:
            keys = storage.storage.list_keys(prefix=f"{category}_")
            cache_keys.extend(keys)

        # Display cache statistics
        st.info(f"Cache contains {len(cache_keys)} entries across {len(cache_categories)} categories.")

        # Add cache management buttons
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Clear All Cache", type="primary"):
                storage.clear_all()
                st.success("Cache cleared successfully!")
                st.rerun()

        with col2:
            if st.button("Refresh Data", type="secondary"):
                st.success("Refreshing data...")
                st.rerun()

        # Add API configuration status
        st.subheader("API Configuration")
        from config import BINANCE_API_KEY, BINANCE_API_SECRET, PIONEX_API_KEY, PIONEX_API_SECRET

        binance_status = "✅ Configured" if BINANCE_API_KEY and BINANCE_API_SECRET else "❌ Not configured"
        pionex_status = "✅ Configured" if PIONEX_API_KEY and PIONEX_API_SECRET else "❌ Not configured"

        st.markdown(f"**Binance API:** {binance_status}")
        st.markdown(f"**Pionex API:** {pionex_status}")

        if not (BINANCE_API_KEY and BINANCE_API_SECRET) or not (PIONEX_API_KEY and PIONEX_API_SECRET):
            st.caption("To configure API keys, edit the .env file in the project root directory.")

    # Get portfolio summary
    summary = portfolio_service.get_portfolio_summary()

    # Display summary metrics in a row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Total Portfolio Value",
            f"${summary['total_value']:,.2f}"
        )

    with col2:
        st.metric(
            "Total Invested",
            f"${summary['total_cost']:,.2f}"
        )

    with col3:
        st.metric(
            "Total P&L",
            f"${summary['total_profit_loss']:,.2f}",
            delta=f"{summary['total_roi']:.2f}%"
        )

    with col4:
        st.metric(
            "Assets",
            summary['asset_count']
        )

    # Create tabs for different dashboard sections
    tabs = st.tabs([
        "Portfolio Overview",
        "Asset Allocation",
        "Performance",
        "Transactions",
        "Active Operations",
        "Trading Bots"
    ])

    # Portfolio Overview Tab
    with tabs[0]:
        show_portfolio_overview(portfolio_service)

    # Asset Allocation Tab
    with tabs[1]:
        show_asset_allocation(portfolio_service)

    # Performance Tab
    with tabs[2]:
        show_performance(portfolio_service, start_date, end_date)

    # Transactions Tab
    with tabs[3]:
        show_transactions(portfolio_service, start_date, end_date, tabs)

def show_portfolio_overview(portfolio_service):
    """
    Display portfolio overview section.

    :param portfolio_service: Instance of PortfolioService.
    """
    st.subheader("Portfolio Overview")

    # Get portfolio data
    portfolio = portfolio_service.get_total_portfolio()

    if portfolio.empty:
        st.info("No portfolio data available. Please check your API connections.")
        return

    # Display portfolio table
    st.dataframe(
        portfolio[['asset', 'quantity', 'value_usd', 'cost_basis', 'roi', 'platform']].sort_values('value_usd', ascending=False),
        column_config={
            'asset': "Asset",
            'quantity': st.column_config.NumberColumn("Quantity", format="%.6f"),
            'value_usd': st.column_config.NumberColumn("Current Value", format="$%.2f"),
            'cost_basis': st.column_config.NumberColumn("Cost Basis", format="$%.2f"),
            'roi': st.column_config.NumberColumn("ROI", format="%.2f%%"),
            'platform': "Platform"
        },
        hide_index=True
    )

    # Display platform allocation
    st.subheader("Platform Allocation")
    platform_allocation = portfolio_service.get_platform_allocation()

    if not platform_allocation.empty:
        fig = px.pie(
            platform_allocation,
            values='value_usd',
            names='platform',
            title='Value Distribution by Platform',
            hole=0.4,
            color_discrete_sequence=px.colors.qualitative.Pastel
        )
        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No platform allocation data available.")

def show_asset_allocation(portfolio_service):
    """
    Display asset allocation section.

    :param portfolio_service: Instance of PortfolioService.
    """
    st.subheader("Asset Allocation")

    # Get asset allocation
    allocation = portfolio_service.get_asset_allocation()

    if allocation.empty:
        st.info("No asset allocation data available.")
        return

    # Create columns for charts
    col1, col2 = st.columns(2)

    with col1:
        # Asset allocation pie chart
        fig = px.pie(
            allocation,
            values='value_usd',
            names='asset',
            title='Asset Allocation by Value',
            color_discrete_sequence=px.colors.qualitative.Bold
        )
        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        # Asset allocation bar chart
        fig = px.bar(
            allocation.sort_values('value_usd', ascending=False).head(10),
            x='asset',
            y='value_usd',
            title='Top 10 Assets by Value',
            color='asset',
            color_discrete_sequence=px.colors.qualitative.Bold
        )
        fig.update_layout(
            xaxis_title="Asset",
            yaxis_title="Value (USD)",
            showlegend=False
        )
        st.plotly_chart(fig, use_container_width=True)

    # ROI by asset
    st.subheader("Performance by Asset")
    roi_by_asset = portfolio_service.get_roi_by_asset()

    if not roi_by_asset.empty:
        # Sort by absolute profit/loss
        roi_by_asset = roi_by_asset.sort_values('profit_loss', ascending=False)

        # Create a bar chart for ROI
        fig = px.bar(
            roi_by_asset,
            x='asset',
            y='roi',
            title='ROI by Asset (%)',
            color='roi',
            color_continuous_scale=['red', 'yellow', 'green'],
            range_color=[-50, 50]
        )
        fig.update_layout(
            xaxis_title="Asset",
            yaxis_title="ROI (%)",
            yaxis=dict(ticksuffix="%")
        )
        st.plotly_chart(fig, use_container_width=True)

        # Create a bar chart for absolute profit/loss
        fig = px.bar(
            roi_by_asset,
            x='asset',
            y='profit_loss',
            title='Profit/Loss by Asset (USD)',
            color='profit_loss',
            color_continuous_scale=['red', 'yellow', 'green'],
        )
        fig.update_layout(
            xaxis_title="Asset",
            yaxis_title="Profit/Loss (USD)",
            yaxis=dict(tickprefix="$")
        )
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No ROI data available.")

def show_performance(portfolio_service, start_date, end_date):
    """
    Display performance section.

    :param portfolio_service: Instance of PortfolioService.
    :param start_date: Start date in 'YYYY-MM-DD' format.
    :param end_date: End date in 'YYYY-MM-DD' format.
    """
    st.subheader("Portfolio Performance")

    # Time period selector
    time_period = st.radio(
        "Select Time Period",
        ["30 Days", "90 Days", "180 Days", "1 Year"],
        horizontal=True
    )

    # Convert time period to days
    if time_period == "30 Days":
        days = 30
    elif time_period == "90 Days":
        days = 90
    elif time_period == "180 Days":
        days = 180
    else:  # 1 Year
        days = 365

    # Get historical performance
    historical_data = portfolio_service.get_historical_performance(days=days)

    if historical_data.empty:
        st.info("No historical performance data available.")
        return

    # Create a line chart for portfolio value over time
    fig = px.line(
        historical_data,
        x='date',
        y='portfolio_value',
        title=f'Portfolio Value Over Time (Last {days} Days)',
        markers=True
    )
    fig.update_layout(
        xaxis_title="Date",
        yaxis_title="Portfolio Value (USD)",
        yaxis=dict(tickprefix="$"),
        hovermode="x unified"
    )
    st.plotly_chart(fig, use_container_width=True)

    # Calculate and display performance metrics
    if len(historical_data) > 1:
        start_value = historical_data['portfolio_value'].iloc[0]
        end_value = historical_data['portfolio_value'].iloc[-1]
        absolute_change = end_value - start_value
        percent_change = (absolute_change / start_value) * 100 if start_value > 0 else 0

        # Display metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                f"Starting Value ({days} days ago)",
                f"${start_value:,.2f}"
            )

        with col2:
            st.metric(
                "Current Value",
                f"${end_value:,.2f}"
            )

        with col3:
            st.metric(
                "Change",
                f"${absolute_change:,.2f}",
                delta=f"{percent_change:.2f}%"
            )

    # Use real production data

def show_transactions(portfolio_service, start_date, end_date, tabs=None):
    """
    Display transactions section.

    :param portfolio_service: Instance of PortfolioService.
    :param start_date: Start date in 'YYYY-MM-DD' format.
    :param end_date: End date in 'YYYY-MM-DD' format.
    """
    st.subheader("Transaction History")

    # Get transaction history
    transactions = portfolio_service.get_transaction_history(
        start_date=start_date,
        end_date=end_date
    )

    if transactions.empty:
        st.info("No transaction history available for the selected period.")
        return

    # Add filters
    col1, col2, col3 = st.columns(3)

    with col1:
        platform_filter = st.multiselect(
            "Platform",
            options=transactions['platform'].unique(),
            default=transactions['platform'].unique()
        )

    with col2:
        type_filter = st.multiselect(
            "Transaction Type",
            options=transactions['type'].unique(),
            default=transactions['type'].unique()
        )

    with col3:
        asset_filter = st.multiselect(
            "Asset",
            options=transactions['asset'].unique(),
            default=[]
        )

    # Apply filters
    filtered_transactions = transactions

    if platform_filter:
        filtered_transactions = filtered_transactions[filtered_transactions['platform'].isin(platform_filter)]

    if type_filter:
        filtered_transactions = filtered_transactions[filtered_transactions['type'].isin(type_filter)]

    if asset_filter:
        filtered_transactions = filtered_transactions[filtered_transactions['asset'].isin(asset_filter)]

    # Display transactions
    if not filtered_transactions.empty:
        # Select columns to display
        display_columns = ['date', 'asset', 'type', 'amount', 'platform']

        # Add price and fee columns if they exist
        if 'price' in filtered_transactions.columns:
            display_columns.append('price')

        if 'fee' in filtered_transactions.columns:
            display_columns.append('fee')

        if 'fee_asset' in filtered_transactions.columns:
            display_columns.append('fee_asset')

        # Add status and txid
        if 'status' in filtered_transactions.columns:
            display_columns.append('status')

        if 'txid' in filtered_transactions.columns:
            display_columns.append('txid')

        # Create column config
        column_config = {
            'date': st.column_config.DatetimeColumn("Date", format="D MMM YYYY, h:mm a"),
            'asset': "Asset",
            'type': "Type",
            'amount': st.column_config.NumberColumn("Amount", format="%.6f"),
            'platform': "Platform"
        }

        if 'price' in display_columns:
            column_config['price'] = st.column_config.NumberColumn("Price", format="$%.2f")

        if 'fee' in display_columns:
            column_config['fee'] = st.column_config.NumberColumn("Fee", format="%.6f")

        if 'fee_asset' in display_columns:
            column_config['fee_asset'] = "Fee Asset"

        if 'status' in display_columns:
            column_config['status'] = "Status"

        if 'txid' in display_columns:
            column_config['txid'] = "Transaction ID"

        # Display dataframe
        st.dataframe(
            filtered_transactions[display_columns],
            column_config=column_config,
            hide_index=True
        )

        # Add download button
        csv = filtered_transactions.to_csv(index=False).encode('utf-8')
        st.download_button(
            "Download Transactions as CSV",
            csv,
            "crypto_transactions.csv",
            "text/csv",
            key='download-csv'
        )
    else:
        st.info("No transactions match the selected filters.")

    # Only proceed with Active Operations Tab if tabs is provided
    if tabs is not None:
        # Active Operations Tab
        with tabs[4]:
            # Get active operations
            operations = portfolio_service.get_active_operations()

            # Display Pionex Grid Bots
            st.subheader("Pionex Grid Bots")

            pionex_grid_bots = operations.get("pionex", {}).get("grid_bots", [])

            if not pionex_grid_bots:
                st.info("No active grid bots found.")
            else:
                # Create a DataFrame for grid bots
                grid_bot_data = []
                for bot in pionex_grid_bots:
                    created_at = datetime.fromtimestamp(bot.get("created_at", 0) / 1000).strftime("%Y-%m-%d") if bot.get("created_at") else "Unknown"

                    # Extract investment details
                    investment = bot.get("investment", {})
                    total_investment = investment.get("total", 0)

                    # Calculate ROI
                    profit = bot.get("profit", 0)
                    roi = (profit / total_investment * 100) if total_investment > 0 else 0

                    grid_bot_data.append({
                        "Name": bot.get("name", "Unknown"),
                        "Symbol": bot.get("symbol", "Unknown").replace("_", "/"),
                        "Status": bot.get("status", "Unknown"),
                        "Type": bot.get("type", "Unknown").capitalize(),
                        "Upper Price": f"${bot.get('upper_price', 0):,.2f}",
                        "Lower Price": f"${bot.get('lower_price', 0):,.2f}",
                        "Grids": bot.get("grid_num", 0),
                        "Profit": f"${profit:,.2f}",
                        "Investment": f"${total_investment:,.2f}",
                        "ROI": f"{roi:.2f}%",
                        "Created": created_at,
                        "ID": bot.get("id", "")
                    })

                # Display grid bots as a table with a link to view details
                df_grid_bots = pd.DataFrame(grid_bot_data)
                # Add a column for actions
                df_grid_bots["Actions"] = "View Details"

                # Display grid bots as a table
                st.dataframe(
                    df_grid_bots,
                    use_container_width=True
                )

                # Calculate total profit and investment
                total_profit = sum(bot.get("profit", 0) for bot in pionex_grid_bots)
                total_investment = sum(bot.get("investment", {}).get("total", 0) for bot in pionex_grid_bots)

                # Display summary metrics
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Total Grid Bots", len(pionex_grid_bots))
                with col2:
                    st.metric("Total Profit", f"${total_profit:,.2f}")
                with col3:
                    if total_investment > 0:
                        roi = (total_profit / total_investment) * 100
                        st.metric("ROI", f"{roi:.2f}%")
                    else:
                        st.metric("ROI", "N/A")

            # Display Pionex Spot Balances (separate from grid bots)
            st.subheader("Pionex Spot Wallet Balances")

            pionex_balances = operations.get("pionex", {}).get("spot_balances", [])

            if not pionex_balances:
                st.info("No spot wallet balances found.")
            else:
                # Filter out zero balances and ensure we're only showing spot wallet balances
                non_zero_balances = [b for b in pionex_balances if b.get("total", 0) > 0]

                # Create a DataFrame for balances
                balance_data = []
                for balance in non_zero_balances:
                    balance_data.append({
                        "Asset": balance.get("asset", "Unknown"),
                        "Free": f"{balance.get('free', 0):,.8f}",
                        "Locked": f"{balance.get('locked', 0):,.8f}",
                        "Total": f"{balance.get('total', 0):,.8f}",
                        "USD Value": f"${balance.get('usd_value', 0):,.2f}"
                    })

                # Display balances as a table
                df_balances = pd.DataFrame(balance_data)
                st.dataframe(df_balances, use_container_width=True)

                # Calculate total USD value
                total_usd_value = sum(balance.get("usd_value", 0) for balance in non_zero_balances)

                # Display summary metrics
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Total Assets", len(non_zero_balances))
                with col2:
                    st.metric("Total USD Value", f"${total_usd_value:,.2f}")

            # Display Pionex Active Orders
            st.subheader("Pionex Active Orders")

            pionex_orders = operations.get("pionex", {}).get("active_orders", [])

            if not pionex_orders:
                st.info("No active orders found.")
            else:
                # Create a DataFrame for active orders
                order_data = []
                for order in pionex_orders:
                    created_at = datetime.fromtimestamp(order.get("created_at", 0) / 1000).strftime("%Y-%m-%d %H:%M") if order.get("created_at") else "Unknown"

                    # Calculate total value
                    price = order.get("price", 0)
                    size = order.get("size", 0)
                    total_value = price * size

                    order_data.append({
                        "Symbol": order.get("symbol", "Unknown").replace("_", "/"),
                        "Side": order.get("side", "Unknown"),
                        "Type": order.get("type", "Unknown"),
                        "Price": f"${price:,.2f}",
                        "Size": size,
                        "Total": f"${total_value:,.2f}",
                        "Status": order.get("status", "Unknown"),
                        "Created": created_at
                    })

                # Display active orders as a table
                df_orders = pd.DataFrame(order_data)
                st.dataframe(df_orders, use_container_width=True)

            # Add link to full Active Operations page
            st.markdown("For more details, go to the [Active Operations](#active-operations) page.")

        # Trading Bots Tab
        with tabs[5]:
            st.subheader("Pionex Trading Bot Performance")

            # Get bot performance metrics
            bot_metrics = portfolio_service.get_bot_performance_metrics(days=30)

            # Display summary metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Bots", bot_metrics.get("total_bots", 0))
            with col2:
                st.metric("Total Profit", f"${bot_metrics.get('total_profit', 0):,.2f}")
            with col3:
                st.metric("Total Investment", f"${bot_metrics.get('total_investment', 0):,.2f}")
            with col4:
                st.metric("Overall ROI", f"{bot_metrics.get('roi', 0):.2f}%")

            # Get bot history for all bots
            bot_history = portfolio_service.get_bot_history(days=30)

            if not bot_history:
                st.info("No bot history data available.")
            else:
                # Create a DataFrame for the history data
                history_df = pd.DataFrame(bot_history)

                # Group by date and bot_id, then sum the profits
                if not history_df.empty and 'date' in history_df.columns and 'bot_id' in history_df.columns:
                    # Convert date strings to datetime if needed
                    if history_df['date'].dtype == 'object':
                        history_df['date'] = pd.to_datetime(history_df['date'])

                    # Create a line chart for daily profits by bot
                    st.subheader("Daily Profit by Bot")
                    fig = px.line(
                        history_df,
                        x='date',
                        y='profit',
                        color='symbol',
                        title='Daily Profit by Trading Bot',
                        markers=True
                    )
                    fig.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Profit (USDT)",
                        yaxis=dict(tickprefix="$"),
                        hovermode="x unified"
                    )
                    st.plotly_chart(fig, use_container_width=True)

                    # Create a line chart for cumulative profits by bot
                    st.subheader("Cumulative Profit by Bot")
                    fig = px.line(
                        history_df,
                        x='date',
                        y='cumulative_profit',
                        color='symbol',
                        title='Cumulative Profit by Trading Bot',
                        markers=True
                    )
                    fig.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Cumulative Profit (USDT)",
                        yaxis=dict(tickprefix="$"),
                        hovermode="x unified"
                    )
                    st.plotly_chart(fig, use_container_width=True)

            # Display individual bot performance
            st.subheader("Individual Bot Performance")

            # Get active bots
            operations = portfolio_service.get_active_operations()
            pionex_grid_bots = operations.get("pionex", {}).get("grid_bots", [])

            if not pionex_grid_bots:
                st.info("No active grid bots found.")
            else:
                # Create a selectbox to choose a bot
                bot_options = [f"{bot.get('name', 'Unknown')} ({bot.get('symbol', 'Unknown').replace('_', '/')})" for bot in pionex_grid_bots]
                bot_ids = [bot.get("id", "") for bot in pionex_grid_bots]

                selected_index = st.selectbox("Select a bot to view details", range(len(bot_options)), format_func=lambda i: bot_options[i])
                selected_bot_id = bot_ids[selected_index]

                # Get detailed bot information
                bot_details = portfolio_service.get_bot_details(selected_bot_id)

                if bot_details:
                    # Display bot details
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Bot Name", bot_details.get("name", "Unknown"))
                    with col2:
                        st.metric("Symbol", bot_details.get("symbol", "Unknown").replace("_", "/"))
                    with col3:
                        st.metric("Status", bot_details.get("status", "Unknown"))

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Type", bot_details.get("type", "Unknown").capitalize())
                    with col2:
                        st.metric("Upper Price", f"${bot_details.get('upper_price', 0):,.2f}")
                    with col3:
                        st.metric("Lower Price", f"${bot_details.get('lower_price', 0):,.2f}")

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Profit", f"${bot_details.get('profit', 0):,.2f}")
                    with col2:
                        investment = bot_details.get("investment", {})
                        total_investment = investment.get("total", 0)
                        st.metric("Investment", f"${total_investment:,.2f}")
                    with col3:
                        if total_investment > 0:
                            roi = (bot_details.get("profit", 0) / total_investment) * 100
                            st.metric("ROI", f"{roi:.2f}%")
                        else:
                            st.metric("ROI", "N/A")

                    # Display grid orders if available
                    grid_orders = bot_details.get("grid_orders", [])
                    if grid_orders:
                        st.subheader("Grid Orders")

                        # Create a DataFrame for grid orders
                        order_data = []
                        for order in grid_orders:
                            order_data.append({
                                "Price": f"${order.get('price', 0):,.2f}",
                                "Side": order.get("side", "Unknown"),
                                "Status": order.get("status", "Unknown")
                            })

                        # Display grid orders as a table
                        df_orders = pd.DataFrame(order_data)
                        st.dataframe(df_orders, use_container_width=True)

                    # Display performance metrics if available
                    performance = bot_details.get("performance", {})
                    if performance:
                        st.subheader("Performance Metrics")

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Daily Profit", f"${performance.get('daily_profit', 0):,.2f}")
                        with col2:
                            st.metric("Weekly Profit", f"${performance.get('weekly_profit', 0):,.2f}")
                        with col3:
                            st.metric("Monthly Profit", f"${performance.get('monthly_profit', 0):,.2f}")

                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Total Trades", performance.get("total_trades", 0))
                        with col2:
                            st.metric("Successful Trades", performance.get("successful_trades", 0))

                    # Get historical data for this bot
                    bot_history = portfolio_service.get_bot_history(bot_id=selected_bot_id, days=30)

                    if bot_history:
                        st.subheader("Historical Performance")

                        # Create a DataFrame for the history data
                        history_df = pd.DataFrame(bot_history)

                        # Convert date strings to datetime if needed
                        if history_df['date'].dtype == 'object':
                            history_df['date'] = pd.to_datetime(history_df['date'])

                        # Create a line chart for daily profits
                        fig = px.line(
                            history_df,
                            x='date',
                            y='profit',
                            title='Daily Profit',
                            markers=True
                        )
                        fig.update_layout(
                            xaxis_title="Date",
                            yaxis_title="Profit (USDT)",
                            yaxis=dict(tickprefix="$"),
                            hovermode="x unified"
                        )
                        st.plotly_chart(fig, use_container_width=True)

                        # Create a line chart for cumulative profits
                        fig = px.line(
                            history_df,
                            x='date',
                            y='cumulative_profit',
                            title='Cumulative Profit',
                            markers=True
                        )
                        fig.update_layout(
                            xaxis_title="Date",
                            yaxis_title="Cumulative Profit (USDT)",
                            yaxis=dict(tickprefix="$"),
                            hovermode="x unified"
                        )
                        st.plotly_chart(fig, use_container_width=True)
