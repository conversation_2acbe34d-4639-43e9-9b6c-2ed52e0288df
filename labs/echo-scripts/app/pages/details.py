import streamlit as st
from services.binance_service import BinanceService


def show_order_details(start_date, end_date):
    st.header("Order Details")

    binance = BinanceService()
    symbols = ["BTCUSDC", "SOLUSDC", "ETHUSDC", "HBARUSDC", "XRPUSDC", "ADAUSDC", "INJUSDT", "SUIUSDT", "MUBARAKUSDT"]
    orders = binance.get_closed_futures_orders(symbols, start_date, end_date)

    if not orders:
        st.warning("No orders available for details.")
        return

    order_id = st.selectbox("Select Order ID", [order["orderId"] for order in orders])
    selected_order = next(order for order in orders if order["orderId"] == order_id)

    st.json(selected_order)
