import streamlit as st
import asyncio
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time
import json

from services.enhanced_portfolio_service import EnhancedPortfolioService
from services.storage.crypto_storage import CryptoStorage

def show_data_refresh():
    """
    Display the data refresh management page with async refresh capabilities.
    """
    st.header("Data Refresh Management")
    st.markdown("Manage and monitor data refresh operations with parallel fetching and smart caching.")
    
    # Initialize services
    enhanced_portfolio = EnhancedPortfolioService()
    storage = CryptoStorage()
    
    # Create tabs for different refresh management sections
    tabs = st.tabs([
        "Refresh Control",
        "Cache Status", 
        "Refresh History",
        "Performance Metrics",
        "Storage Management"
    ])
    
    # Refresh Control Tab
    with tabs[0]:
        show_refresh_control(enhanced_portfolio)
    
    # Cache Status Tab
    with tabs[1]:
        show_cache_status(enhanced_portfolio, storage)
    
    # Refresh History Tab
    with tabs[2]:
        show_refresh_history(storage)
    
    # Performance Metrics Tab
    with tabs[3]:
        show_performance_metrics(storage)
    
    # Storage Management Tab
    with tabs[4]:
        show_storage_management(enhanced_portfolio, storage)

def show_refresh_control(enhanced_portfolio):
    """Display refresh control interface."""
    st.subheader("Data Refresh Control")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Quick Actions")
        
        # Force refresh button
        if st.button("🔄 Force Full Refresh", type="primary", help="Refresh all data ignoring cache"):
            with st.spinner("Performing full data refresh..."):
                try:
                    # Run async refresh in sync context
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    refresh_result = loop.run_until_complete(
                        enhanced_portfolio.refresh_all_data_async(force_refresh=True)
                    )
                    
                    loop.close()
                    
                    if refresh_result['success']:
                        st.success(f"✅ Full refresh completed in {refresh_result['timing']['total_time']:.2f}s")
                        
                        # Show detailed results
                        with st.expander("Refresh Details"):
                            st.json(refresh_result)
                    else:
                        st.error("❌ Refresh failed")
                        if refresh_result['errors']:
                            for error in refresh_result['errors']:
                                st.error(error)
                                
                except Exception as e:
                    st.error(f"Error during refresh: {e}")
        
        # Smart refresh button
        if st.button("🧠 Smart Refresh", help="Refresh only stale data based on cache age"):
            with st.spinner("Performing smart data refresh..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    refresh_result = loop.run_until_complete(
                        enhanced_portfolio.refresh_all_data_async(force_refresh=False)
                    )
                    
                    loop.close()
                    
                    if refresh_result['success']:
                        st.success(f"✅ Smart refresh completed in {refresh_result['timing']['total_time']:.2f}s")
                        
                        # Show what was updated
                        updated_sources = []
                        for source, data in refresh_result['data_sources'].items():
                            if data.get('updated'):
                                updated_sources.append(f"{source}: {len(data['updated'])} items")
                        
                        if updated_sources:
                            st.info("Updated: " + ", ".join(updated_sources))
                        else:
                            st.info("No data needed refreshing - all cache is fresh!")
                    else:
                        st.error("❌ Smart refresh failed")
                        
                except Exception as e:
                    st.error(f"Error during smart refresh: {e}")
    
    with col2:
        st.markdown("### Refresh Status")
        
        # Get current refresh status
        try:
            status = enhanced_portfolio.get_refresh_status()
            
            # Display cache health
            health = status.get('cache_health', {})
            health_pct = health.get('health_percentage', 0)
            
            # Health indicator
            if health_pct >= 95:
                health_color = "green"
                health_icon = "✅"
            elif health_pct >= 80:
                health_color = "orange"
                health_icon = "⚠️"
            else:
                health_color = "red"
                health_icon = "❌"
            
            st.markdown(f"**Cache Health:** {health_icon} {health_pct:.1f}%")
            
            # Display recommendations
            recommendations = status.get('recommendations', [])
            if recommendations:
                st.markdown("**Recommendations:**")
                for rec in recommendations:
                    st.warning(rec)
            else:
                st.success("All data is fresh and healthy!")
                
        except Exception as e:
            st.error(f"Error getting refresh status: {e}")

def show_cache_status(enhanced_portfolio, storage):
    """Display cache status and freshness information."""
    st.subheader("Cache Status & Data Freshness")
    
    try:
        status = enhanced_portfolio.get_refresh_status()
        freshness_data = status.get('data_freshness', {})
        
        if freshness_data:
            # Create freshness visualization
            freshness_df = []
            for data_type, info in freshness_data.items():
                age_seconds = info.get('age_seconds')
                max_age = info.get('max_age_seconds', 0)
                is_fresh = info.get('is_fresh', False)
                
                if age_seconds is not None:
                    freshness_df.append({
                        'Data Type': data_type.replace('_', ' ').title(),
                        'Age (seconds)': age_seconds,
                        'Max Age (seconds)': max_age,
                        'Status': 'Fresh' if is_fresh else 'Stale',
                        'Freshness %': min(100, (max_age - age_seconds) / max_age * 100) if max_age > 0 else 0
                    })
            
            if freshness_df:
                df = pd.DataFrame(freshness_df)
                
                # Display as table
                st.dataframe(df, use_container_width=True)
                
                # Create freshness chart
                fig = px.bar(
                    df, 
                    x='Data Type', 
                    y='Freshness %',
                    color='Status',
                    color_discrete_map={'Fresh': 'green', 'Stale': 'red'},
                    title='Data Freshness by Type'
                )
                fig.update_layout(yaxis_title="Freshness Percentage")
                st.plotly_chart(fig, use_container_width=True)
        
        # Storage statistics
        st.markdown("### Storage Statistics")
        storage_stats = status.get('storage_stats', {})
        
        if storage_stats:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Files", storage_stats.get('total_files', 0))
            
            with col2:
                st.metric("Total Size", f"{storage_stats.get('total_size', 0) / 1024 / 1024:.1f} MB")
            
            with col3:
                st.metric("Backup Count", storage_stats.get('backup_count', 0))
            
            with col4:
                corrupted = storage_stats.get('corrupted_files', 0)
                st.metric("Corrupted Files", corrupted, delta=f"{'⚠️' if corrupted > 0 else '✅'}")
        
    except Exception as e:
        st.error(f"Error displaying cache status: {e}")

def show_refresh_history(storage):
    """Display refresh history and patterns."""
    st.subheader("Refresh History")
    
    # This would be implemented with actual refresh history tracking
    st.info("Refresh history tracking will be implemented with actual refresh operations.")
    
    # Placeholder for refresh history visualization
    # In a real implementation, you would store refresh events and display them here
    sample_history = [
        {'timestamp': datetime.now() - timedelta(minutes=5), 'type': 'Smart Refresh', 'duration': 2.3, 'items_updated': 15},
        {'timestamp': datetime.now() - timedelta(minutes=15), 'type': 'Force Refresh', 'duration': 8.7, 'items_updated': 45},
        {'timestamp': datetime.now() - timedelta(hours=1), 'type': 'Smart Refresh', 'duration': 1.8, 'items_updated': 8},
    ]
    
    if sample_history:
        df = pd.DataFrame(sample_history)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Display recent refreshes
        st.dataframe(df, use_container_width=True)
        
        # Create timeline chart
        fig = px.scatter(
            df, 
            x='timestamp', 
            y='duration',
            size='items_updated',
            color='type',
            title='Refresh Performance Over Time'
        )
        fig.update_layout(
            xaxis_title="Time",
            yaxis_title="Duration (seconds)"
        )
        st.plotly_chart(fig, use_container_width=True)

def show_performance_metrics(storage):
    """Display performance metrics and optimization suggestions."""
    st.subheader("Performance Metrics")
    
    # Cache hit rate simulation (would be real data in production)
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Cache Hit Rate", "87.3%", delta="2.1%")
    
    with col2:
        st.metric("Avg Refresh Time", "3.2s", delta="-0.8s")
    
    with col3:
        st.metric("API Calls Saved", "1,247", delta="156")
    
    # Performance trends (placeholder)
    st.markdown("### Performance Trends")
    
    # Generate sample performance data
    dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='H')
    performance_data = pd.DataFrame({
        'timestamp': dates,
        'refresh_time': [2 + (i % 10) * 0.5 for i in range(len(dates))],
        'cache_hit_rate': [80 + (i % 20) for i in range(len(dates))],
        'api_calls': [50 + (i % 30) for i in range(len(dates))]
    })
    
    # Refresh time trend
    fig = px.line(
        performance_data, 
        x='timestamp', 
        y='refresh_time',
        title='Refresh Time Trend'
    )
    fig.update_layout(yaxis_title="Refresh Time (seconds)")
    st.plotly_chart(fig, use_container_width=True)
    
    # Cache hit rate trend
    fig = px.line(
        performance_data, 
        x='timestamp', 
        y='cache_hit_rate',
        title='Cache Hit Rate Trend'
    )
    fig.update_layout(yaxis_title="Cache Hit Rate (%)")
    st.plotly_chart(fig, use_container_width=True)

def show_storage_management(enhanced_portfolio, storage):
    """Display storage management options."""
    st.subheader("Storage Management")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Cache Operations")
        
        # Cache cleanup
        if st.button("🧹 Clean Expired Data", help="Remove expired cache entries"):
            with st.spinner("Cleaning expired data..."):
                try:
                    cleanup_stats = enhanced_portfolio.cleanup_cache()
                    
                    if cleanup_stats['expired_cleaned'] > 0:
                        st.success(f"✅ Cleaned {cleanup_stats['expired_cleaned']} expired entries")
                    else:
                        st.info("No expired data found")
                    
                    if cleanup_stats['errors'] > 0:
                        st.warning(f"⚠️ {cleanup_stats['errors']} errors during cleanup")
                        
                except Exception as e:
                    st.error(f"Error during cleanup: {e}")
        
        # Cache statistics refresh
        if st.button("📊 Refresh Statistics"):
            st.rerun()
    
    with col2:
        st.markdown("### Storage Information")
        
        try:
            # Get storage stats
            stats = storage.get_storage_stats()
            
            if stats:
                st.json(stats)
            else:
                st.info("No storage statistics available")
                
        except Exception as e:
            st.error(f"Error getting storage stats: {e}")
    
    # Advanced options
    with st.expander("Advanced Options"):
        st.warning("⚠️ Advanced operations can affect data integrity")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🗑️ Clear All Cache", type="secondary"):
                if st.checkbox("I understand this will clear all cached data"):
                    try:
                        storage.clear_all()
                        st.success("✅ All cache cleared")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error clearing cache: {e}")
        
        with col2:
            # Export cache data
            if st.button("📤 Export Cache Data"):
                try:
                    # This would export cache data for backup
                    st.info("Cache export functionality would be implemented here")
                except Exception as e:
                    st.error(f"Error exporting cache: {e}")
