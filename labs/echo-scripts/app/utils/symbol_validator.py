"""
Symbol validation utility for crypto trading pairs.
Handles invalid symbols, blacklists, and symbol normalization.
"""

import re
from typing import Set, Optional, List, Dict
import time
from threading import Lock

class SymbolValidator:
    """
    Validates and normalizes trading symbols, maintains blacklists for invalid symbols.
    """
    
    def __init__(self):
        # Thread-safe lock for blacklist operations
        self._lock = Lock()
        
        # Permanent blacklist for symbols that should never be looked up
        self.permanent_blacklist: Set[str] = {
            # Binance Liquid Swap tokens (LD prefix)
            'LDBTC', 'LDETH', 'LDBNB', 'LDADA', 'LDDOT', 'LDLINK', 'LDLTC',
            'LDXRP', 'LDSOL', 'LDMATIC', 'LDAVAX', 'LDATOM', 'LDNEAR',
            'LDUSDC', 'LDUSDT', 'LDBUSD', 'LDDAI', 'LDTUSD',
            
            # Other known invalid symbols
            'ETHW',  # Ethereum PoW (not supported on Binance)
            'LUNA2', # Terra Luna 2.0 (different symbol on Binance)
            'LUNC',  # Terra Luna Classic (different symbol on Binance)
            
            # Add more as discovered
        }
        
        # Dynamic blacklist for symbols that have failed multiple times
        self.dynamic_blacklist: Dict[str, Dict] = {}
        
        # Failed lookup cache with timestamps
        self.failed_lookups: Dict[str, float] = {}
        
        # Session-based error tracking to avoid repeated messages
        self.logged_errors: Set[str] = set()
        
        # Cache for symbol corrections
        self.symbol_corrections: Dict[str, str] = {
            'ETHWUSDT': 'ETHUSDT',
            'ETHWUSDC': 'ETHUSDC',
            'ETHWBUSD': 'ETHBUSD',
            # Add more corrections as needed
        }
    
    def is_valid_symbol(self, symbol: str) -> bool:
        """
        Check if a symbol is valid for price lookup.
        
        :param symbol: Trading pair symbol
        :return: True if symbol should be looked up, False otherwise
        """
        if not symbol or not isinstance(symbol, str):
            return False
        
        symbol = symbol.upper().strip()
        
        # Check permanent blacklist
        base_symbol = self._extract_base_symbol(symbol)
        if base_symbol in self.permanent_blacklist:
            return False
        
        # Check if symbol starts with LD (Liquid Swap tokens)
        if symbol.startswith('LD'):
            return False
        
        # Check dynamic blacklist
        with self._lock:
            if symbol in self.dynamic_blacklist:
                blacklist_entry = self.dynamic_blacklist[symbol]
                # Remove from blacklist after 24 hours
                if time.time() - blacklist_entry['timestamp'] > 86400:
                    del self.dynamic_blacklist[symbol]
                else:
                    return False
        
        # Check recent failed lookups (avoid retrying for 1 hour)
        if symbol in self.failed_lookups:
            if time.time() - self.failed_lookups[symbol] < 3600:  # 1 hour
                return False
            else:
                # Remove old failed lookup
                del self.failed_lookups[symbol]
        
        return True
    
    def _extract_base_symbol(self, symbol: str) -> str:
        """Extract base currency from trading pair symbol."""
        # Remove common quote currencies
        for quote in ['USDT', 'USDC', 'BUSD', 'BTC', 'ETH', 'BNB']:
            if symbol.endswith(quote):
                return symbol[:-len(quote)]
        return symbol
    
    def normalize_symbol(self, symbol: str) -> Optional[str]:
        """
        Normalize and correct common symbol issues.
        
        :param symbol: Original symbol
        :return: Corrected symbol or None if invalid
        """
        if not self.is_valid_symbol(symbol):
            return None
        
        symbol = symbol.upper().strip()
        
        # Apply known corrections
        if symbol in self.symbol_corrections:
            corrected = self.symbol_corrections[symbol]
            self._log_once(f"Corrected invalid symbol {symbol} to {corrected}")
            return corrected
        
        return symbol
    
    def record_failed_lookup(self, symbol: str, error_type: str = "not_found"):
        """
        Record a failed symbol lookup.
        
        :param symbol: Symbol that failed
        :param error_type: Type of error (not_found, invalid, timeout, etc.)
        """
        symbol = symbol.upper().strip()
        current_time = time.time()
        
        # Record failed lookup
        self.failed_lookups[symbol] = current_time
        
        # Add to dynamic blacklist if it fails multiple times
        with self._lock:
            if symbol in self.dynamic_blacklist:
                self.dynamic_blacklist[symbol]['count'] += 1
                self.dynamic_blacklist[symbol]['last_error'] = error_type
            else:
                self.dynamic_blacklist[symbol] = {
                    'count': 1,
                    'timestamp': current_time,
                    'last_error': error_type
                }
            
            # If symbol has failed 3+ times, add to permanent session blacklist
            if self.dynamic_blacklist[symbol]['count'] >= 3:
                self._log_once(f"Symbol {symbol} added to blacklist after {self.dynamic_blacklist[symbol]['count']} failures")
    
    def record_successful_lookup(self, symbol: str):
        """
        Record a successful symbol lookup (removes from failed cache).
        
        :param symbol: Symbol that succeeded
        """
        symbol = symbol.upper().strip()
        
        # Remove from failed lookups
        if symbol in self.failed_lookups:
            del self.failed_lookups[symbol]
        
        # Remove from dynamic blacklist
        with self._lock:
            if symbol in self.dynamic_blacklist:
                del self.dynamic_blacklist[symbol]
    
    def _log_once(self, message: str):
        """Log a message only once per session."""
        if message not in self.logged_errors:
            print(message)
            self.logged_errors.add(message)
    
    def get_alternative_symbols(self, symbol: str, max_alternatives: int = 3) -> List[str]:
        """
        Get alternative trading pairs for a symbol.
        
        :param symbol: Original symbol
        :param max_alternatives: Maximum number of alternatives to return
        :return: List of alternative symbols to try
        """
        if not self.is_valid_symbol(symbol):
            return []
        
        symbol = symbol.upper().strip()
        alternatives = []
        
        # Extract base currency
        base = self._extract_base_symbol(symbol)
        if not base:
            return []
        
        # Define quote currencies in order of preference
        quote_preferences = ['USDT', 'USDC', 'BUSD', 'BTC']
        
        # Current quote currency
        current_quote = symbol[len(base):] if len(symbol) > len(base) else ''
        
        # Generate alternatives
        for quote in quote_preferences:
            if quote != current_quote:
                alt_symbol = f"{base}{quote}"
                if self.is_valid_symbol(alt_symbol) and len(alternatives) < max_alternatives:
                    alternatives.append(alt_symbol)
        
        return alternatives
    
    def should_log_error(self, symbol: str, error_message: str) -> bool:
        """
        Determine if an error should be logged (avoid spam).
        
        :param symbol: Symbol that failed
        :param error_message: Error message
        :return: True if should log, False otherwise
        """
        error_key = f"{symbol}:{error_message}"
        return error_key not in self.logged_errors
    
    def log_error_once(self, symbol: str, error_message: str):
        """
        Log an error message once per symbol per session.
        
        :param symbol: Symbol that failed
        :param error_message: Error message to log
        """
        error_key = f"{symbol}:{error_message}"
        if error_key not in self.logged_errors:
            print(f"Price lookup failed for {symbol}: {error_message}")
            self.logged_errors.add(error_key)
    
    def get_blacklist_stats(self) -> Dict[str, int]:
        """Get statistics about blacklisted symbols."""
        with self._lock:
            return {
                'permanent_blacklist': len(self.permanent_blacklist),
                'dynamic_blacklist': len(self.dynamic_blacklist),
                'failed_lookups': len(self.failed_lookups),
                'logged_errors': len(self.logged_errors)
            }
    
    def clear_session_data(self):
        """Clear session-specific data (for testing or reset)."""
        with self._lock:
            self.dynamic_blacklist.clear()
            self.failed_lookups.clear()
            self.logged_errors.clear()
    
    def add_permanent_blacklist(self, symbols: List[str]):
        """Add symbols to permanent blacklist."""
        for symbol in symbols:
            self.permanent_blacklist.add(symbol.upper().strip())
    
    def is_liquid_swap_token(self, symbol: str) -> bool:
        """Check if symbol is a Binance Liquid Swap token."""
        return symbol.upper().startswith('LD')
    
    def get_symbol_info(self, symbol: str) -> Dict[str, any]:
        """Get detailed information about a symbol."""
        symbol = symbol.upper().strip()
        base = self._extract_base_symbol(symbol)
        
        return {
            'symbol': symbol,
            'base': base,
            'is_valid': self.is_valid_symbol(symbol),
            'is_liquid_swap': self.is_liquid_swap_token(symbol),
            'is_blacklisted': (
                base in self.permanent_blacklist or 
                symbol in self.dynamic_blacklist
            ),
            'has_failed_recently': symbol in self.failed_lookups,
            'alternatives': self.get_alternative_symbols(symbol)
        }

# Global instance
symbol_validator = SymbolValidator()
