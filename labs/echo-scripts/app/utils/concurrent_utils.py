import concurrent.futures
from typing import List, Callable, Any, Dict, <PERSON><PERSON>

def execute_concurrently(tasks: List[Tuple[Callable, Dict[str, Any]]], max_workers: int = 10) -> List[Any]:
    """
    Execute multiple tasks concurrently.
    
    :param tasks: List of tuples containing (function, kwargs) pairs
    :param max_workers: Maximum number of worker threads
    :return: List of results in the same order as the tasks
    """
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        futures = [executor.submit(func, **kwargs) for func, kwargs in tasks]
        
        # Collect results as they complete
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                # Add None for failed tasks
                results.append(None)
                print(f"Error in concurrent execution: {e}")
    
    return results
