import streamlit as st
import datetime

st.set_page_config(
    page_title="Crypto Investment Dashboard",
    layout="wide",
    initial_sidebar_state="expanded"
)

st.title("Crypto Investment Dashboard")

st.sidebar.header("Navigation")
page = st.sidebar.radio(
    "Go to",
    ["Dashboard", "Wallet Movements", "Active Operations", "Closed Orders", "Live Orders", "Order Details", "Data Refresh"]
)

# Set default dates
now = datetime.date.today()
ninety_days_ago = now - datetime.timedelta(days=90)

# Add date pickers to sidebar
st.sidebar.subheader("Date Range")
start_date_value = st.sidebar.date_input("Start Date", value=ninety_days_ago, max_value=now)
end_date_value = st.sidebar.date_input("End Date", value=now, min_value=start_date_value, max_value=now)

# Convert to string format
start_date = start_date_value.strftime('%Y-%m-%d')
end_date = end_date_value.strftime('%Y-%m-%d')

# Add a note about Binance API limitations
st.sidebar.info("Note: Binance API restricts searches to the most recent 90 days.")

# Add additional trading pairs to monitor
st.sidebar.subheader("Monitored Trading Pairs")
st.sidebar.caption("The following trading pairs are being monitored:")
trading_pairs = [
    "BTCUSDC", "SOLUSDC", "ETHUSDC", "HBARUSDC", "XRPUSDC", "ADAUSDC",
    "INJUSDT", "SUIUSDT", "MUBARAKUSDT"
]
for pair in trading_pairs:
    st.sidebar.code(pair, language=None)

# Display the selected page
if page == "Dashboard":
    from pages.dashboard import show_dashboard
    show_dashboard(start_date, end_date)
elif page == "Wallet Movements":
    from pages.wallet_movements import show_wallet_movements
    show_wallet_movements(start_date, end_date)
elif page == "Active Operations":
    from pages.active_operations import show_active_operations
    show_active_operations()
elif page == "Closed Orders":
    from pages.closed_orders import show_closed_orders
    show_closed_orders(start_date, end_date)
elif page == "Live Orders":
    from pages.live_orders import show_live_orders
    show_live_orders()
elif page == "Order Details":
    from pages.details import show_order_details
    show_order_details(start_date, end_date)
elif page == "Data Refresh":
    from pages.data_refresh import show_data_refresh
    show_data_refresh()
