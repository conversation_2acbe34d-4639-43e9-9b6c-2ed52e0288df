from services.binance_service import BinanceService
from services.data_processor import calculate_pnl
binance_service = BinanceService()

symbols = ["BTCUSDC", "INJUSDT", "SUIUSDT", "MUBARAKUSDT"]

# Specify the start date in 'YYYY-MM-DD' format
start_date = '2024-12-12'

# Optionally, specify the end date; if omitted, it defaults to the current date
end_date = '2025-03-16'

#closed_orders = binance_service.get_closed_futures_orders(symbols, start_date, end_date)

income = binance_service.get_income_history(start_date=start_date, end_date=end_date, income_type="REALIZED_PNL")


# API Docs (dani check this docs):
# https://developers.binance.com/docs/derivatives/usds-margined-futures/trade/rest-api/All-Orders

a = 2
print(income)

#order = closed_orders[0]
#pnl = calculate_pnl(order, order.get("entryPrice"))
#$print(closed_orders)