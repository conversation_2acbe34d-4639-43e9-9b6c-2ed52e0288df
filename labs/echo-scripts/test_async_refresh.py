#!/usr/bin/env python3
"""
Test script for the enhanced data refresh functionality.
Tests parallel data fetching, non-destructive updates, and performance optimization.
"""

import asyncio
import time
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.enhanced_portfolio_service import EnhancedPortfolioService
from services.portfolio_service import PortfolioService
from services.storage.crypto_storage import CryptoStorage

def test_traditional_refresh():
    """Test traditional refresh method."""
    print("="*60)
    print("TESTING TRADITIONAL REFRESH")
    print("="*60)
    
    portfolio = PortfolioService()
    
    start_time = time.time()
    result = portfolio.refresh_data_parallel(force_refresh=False)
    end_time = time.time()
    
    print(f"Traditional refresh completed in {end_time - start_time:.2f} seconds")
    print(f"Success: {result.get('success', False)}")
    print(f"Tasks executed: {result.get('timing', {}).get('tasks_executed', 0)}")
    print(f"Successful tasks: {result.get('timing', {}).get('successful_tasks', 0)}")
    
    if result.get('error'):
        print(f"Error: {result['error']}")
    
    return result

async def test_enhanced_refresh():
    """Test enhanced async refresh method."""
    print("\n" + "="*60)
    print("TESTING ENHANCED ASYNC REFRESH")
    print("="*60)
    
    enhanced_portfolio = EnhancedPortfolioService()
    
    # Test smart refresh (only refresh stale data)
    print("\n--- Smart Refresh Test ---")
    start_time = time.time()
    result = await enhanced_portfolio.refresh_all_data_async(force_refresh=False)
    end_time = time.time()
    
    print(f"Smart refresh completed in {end_time - start_time:.2f} seconds")
    print(f"Success: {result.get('success', False)}")
    print(f"Total time: {result.get('timing', {}).get('total_time', 0):.2f}s")
    print(f"Fetch time: {result.get('timing', {}).get('fetch_time', 0):.2f}s")
    print(f"Process time: {result.get('timing', {}).get('process_time', 0):.2f}s")
    print(f"Tasks executed: {result.get('timing', {}).get('tasks_executed', 0)}")
    print(f"Successful tasks: {result.get('timing', {}).get('successful_tasks', 0)}")
    
    # Show what was updated
    data_sources = result.get('data_sources', {})
    for source, data in data_sources.items():
        updated = data.get('updated', [])
        errors = data.get('errors', [])
        if updated:
            print(f"  {source}: Updated {len(updated)} items - {updated}")
        if errors:
            print(f"  {source}: {len(errors)} errors - {errors}")
    
    # Test force refresh
    print("\n--- Force Refresh Test ---")
    start_time = time.time()
    result = await enhanced_portfolio.refresh_all_data_async(force_refresh=True)
    end_time = time.time()
    
    print(f"Force refresh completed in {end_time - start_time:.2f} seconds")
    print(f"Success: {result.get('success', False)}")
    print(f"Total time: {result.get('timing', {}).get('total_time', 0):.2f}s")
    
    return result

def test_storage_features():
    """Test enhanced storage features."""
    print("\n" + "="*60)
    print("TESTING ENHANCED STORAGE FEATURES")
    print("="*60)
    
    storage = CryptoStorage(use_enhanced=True)
    
    # Test incremental data saving
    print("\n--- Incremental Data Test ---")
    
    # Save initial data
    initial_data = [
        {'id': 1, 'symbol': 'BTCUSDT', 'price': 60000},
        {'id': 2, 'symbol': 'ETHUSDT', 'price': 3500}
    ]
    
    success = storage.save_data('test_prices', initial_data, {})
    print(f"Initial data saved: {success}")
    
    # Add new data incrementally
    new_data = [
        {'id': 3, 'symbol': 'BNBUSDT', 'price': 500},
        {'id': 1, 'symbol': 'BTCUSDT', 'price': 61000}  # Updated price
    ]
    
    success = storage.save_incremental_data('test_prices', new_data, {}, merge_strategy="append")
    print(f"Incremental data saved: {success}")
    
    # Load and verify merged data
    merged_data = storage.load_data('test_prices', {})
    print(f"Merged data contains {len(merged_data) if merged_data else 0} items")
    
    if merged_data:
        for item in merged_data:
            print(f"  {item}")
    
    # Test data freshness
    print("\n--- Data Freshness Test ---")
    is_fresh = storage.is_data_fresh('test_prices', {}, max_age_seconds=60)
    print(f"Data is fresh (within 60s): {is_fresh}")
    
    age = storage.get_data_age('test_prices', {})
    print(f"Data age: {age} seconds" if age is not None else "Data age: Unknown")
    
    # Test storage statistics
    print("\n--- Storage Statistics ---")
    stats = storage.get_storage_stats()
    if stats:
        print(f"Total files: {stats.get('total_files', 0)}")
        print(f"Total size: {stats.get('total_size', 0) / 1024:.1f} KB")
        print(f"Backup count: {stats.get('backup_count', 0)}")
        print(f"Corrupted files: {stats.get('corrupted_files', 0)}")
        print(f"Health: {stats.get('health_percentage', 0):.1f}%")
    
    # Clean up test data
    storage.delete_data('test_prices', {})
    print("\nTest data cleaned up")

def test_refresh_status():
    """Test refresh status and cache health monitoring."""
    print("\n" + "="*60)
    print("TESTING REFRESH STATUS & CACHE HEALTH")
    print("="*60)
    
    enhanced_portfolio = EnhancedPortfolioService()
    
    # Get refresh status
    status = enhanced_portfolio.get_refresh_status()
    
    print("Cache Health:")
    cache_health = status.get('cache_health', {})
    print(f"  Total files: {cache_health.get('total_files', 0)}")
    print(f"  Corrupted files: {cache_health.get('corrupted_files', 0)}")
    print(f"  Health percentage: {cache_health.get('health_percentage', 0):.1f}%")
    print(f"  Backup count: {cache_health.get('backup_count', 0)}")
    
    print("\nData Freshness:")
    freshness = status.get('data_freshness', {})
    for data_type, info in freshness.items():
        age = info.get('age_seconds')
        is_fresh = info.get('is_fresh', False)
        max_age = info.get('max_age_seconds', 0)
        
        if age is not None:
            print(f"  {data_type}: {age}s old, {'fresh' if is_fresh else 'stale'} (max: {max_age}s)")
        else:
            print(f"  {data_type}: No data available")
    
    print("\nRecommendations:")
    recommendations = status.get('recommendations', [])
    if recommendations:
        for rec in recommendations:
            print(f"  - {rec}")
    else:
        print("  No recommendations - all data is healthy!")

def test_cache_cleanup():
    """Test cache cleanup functionality."""
    print("\n" + "="*60)
    print("TESTING CACHE CLEANUP")
    print("="*60)
    
    enhanced_portfolio = EnhancedPortfolioService()
    
    # Perform cleanup
    cleanup_stats = enhanced_portfolio.cleanup_cache()
    
    print(f"Expired entries cleaned: {cleanup_stats.get('expired_cleaned', 0)}")
    print(f"Corrupted entries cleaned: {cleanup_stats.get('corrupted_cleaned', 0)}")
    print(f"Backups cleaned: {cleanup_stats.get('backups_cleaned', 0)}")
    print(f"Cleanup errors: {cleanup_stats.get('errors', 0)}")

async def main():
    """Main test function."""
    print("🚀 TESTING ENHANCED DATA REFRESH FUNCTIONALITY")
    print("=" * 80)
    
    try:
        # Test 1: Traditional refresh
        traditional_result = test_traditional_refresh()
        
        # Test 2: Enhanced async refresh
        enhanced_result = await test_enhanced_refresh()
        
        # Test 3: Storage features
        test_storage_features()
        
        # Test 4: Refresh status
        test_refresh_status()
        
        # Test 5: Cache cleanup
        test_cache_cleanup()
        
        # Performance comparison
        print("\n" + "="*60)
        print("PERFORMANCE COMPARISON")
        print("="*60)
        
        traditional_time = traditional_result.get('timing', {}).get('total_time', 0)
        enhanced_time = enhanced_result.get('timing', {}).get('total_time', 0)
        
        print(f"Traditional refresh: {traditional_time:.2f}s")
        print(f"Enhanced async refresh: {enhanced_time:.2f}s")
        
        if traditional_time > 0 and enhanced_time > 0:
            improvement = ((traditional_time - enhanced_time) / traditional_time) * 100
            print(f"Performance improvement: {improvement:.1f}%")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
