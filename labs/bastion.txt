The article from the AWS Startups Blog, titled "How to Secure Your Instances with Multi-factor Authentication", provides a detailed guide on enhancing the security of Amazon EC2 Linux hosts using multi-factor authentication (MFA) for SSH access. It outlines the importance of securing key pairs and recommends using a bastion host for controlled access to instances. The article also provides a step-by-step tutorial on implementing MFA using Google Authenticator, including configuring SSH and PAM, managing user authentication, and automating the setup for new users. This approach ensures a higher level of security by requiring both a key pair and a one-time password for SSH access.

https://aws.amazon.com/blogs/startups/securing-ssh-to-amazon-ec2-linux-hosts/



The DigitalOcean tutorial outlines configuring two-factor authentication (2FA) for a non-root sudoer user on Ubuntu 18.04 using Google's PAM module. Key steps include installing the PAM module, configuring 2FA for the user with time-based OTPs, updating Ubuntu's authentication settings, and testing the setup. Options are provided for requiring 2FA during both login and sudo requests, or for login only. The tutorial is applicable for both server and desktop environments and includes detailed instructions for each configuration step.

https://www.digitalocean.com/community/tutorials/how-to-configure-multi-factor-authentication-on-ubuntu-18-04

