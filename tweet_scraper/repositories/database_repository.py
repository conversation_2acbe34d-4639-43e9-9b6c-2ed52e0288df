"""
Database-based implementation of the TweetRepository interface.
"""

import logging
from datetime import datetime
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from tweet_scraper.domain.models import Tweet
from tweet_scraper.repositories.base import TweetRepository
from tweet_scraper.database import get_db_session_sync
from tweet_scraper.database.models import TweetRecord

logger = logging.getLogger(__name__)


class DatabaseTweetRepository(TweetRepository):
    """
    Database-based implementation of the TweetRepository interface.
    
    Stores tweets in a SQL database using SQLAlchemy.
    """
    
    def __init__(self):
        """Initialize the database repository."""
        self.logger = logging.getLogger('DatabaseTweetRepository')
    
    def save_tweet(self, tweet: Tweet) -> None:
        """
        Save a tweet to the database.
        
        Args:
            tweet: The tweet to save
        """
        try:
            db = get_db_session_sync()
            
            # Check if tweet already exists
            existing_tweet = db.query(TweetRecord).filter(TweetRecord.id == tweet.id).first()
            
            if existing_tweet:
                # Update existing tweet
                existing_tweet.text = tweet.text
                existing_tweet.time_ago = tweet.time_ago
                existing_tweet.publish_date = tweet.publish_date
                existing_tweet.url = tweet.url
                existing_tweet.scraped_at = tweet.scraped_at
                existing_tweet.approximate_datetime = tweet.approximate_datetime
                existing_tweet.analysis_status = tweet.analysis_status
                existing_tweet.analyzed_at = tweet.analyzed_at
                
                self.logger.info(f"Updated existing tweet {tweet.id}")
            else:
                # Create new tweet record
                tweet_record = TweetRecord(
                    id=tweet.id,
                    text=tweet.text,
                    time_ago=tweet.time_ago,
                    publish_date=tweet.publish_date,
                    url=tweet.url,
                    scraped_at=tweet.scraped_at,
                    approximate_datetime=tweet.approximate_datetime,
                    analysis_status=tweet.analysis_status,
                    analyzed_at=tweet.analyzed_at
                )
                
                db.add(tweet_record)
                self.logger.info(f"Saved new tweet {tweet.id}")
            
            db.commit()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error saving tweet {tweet.id}: {e}")
            db.rollback()
            raise
        except Exception as e:
            self.logger.error(f"Error saving tweet {tweet.id}: {e}")
            db.rollback()
            raise
        finally:
            db.close()
    
    def is_tweet_scraped(self, tweet_id: str) -> bool:
        """
        Check if a tweet has already been scraped.
        
        Args:
            tweet_id: The ID of the tweet to check
            
        Returns:
            True if the tweet has been scraped, False otherwise
        """
        try:
            db = get_db_session_sync()
            
            tweet_exists = db.query(TweetRecord).filter(TweetRecord.id == tweet_id).first() is not None
            
            return tweet_exists
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error checking tweet {tweet_id}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error checking tweet {tweet_id}: {e}")
            return False
        finally:
            db.close()
    
    def add_tweet_to_db(self, tweet: Tweet) -> None:
        """
        Add a tweet to the database (same as save_tweet for database implementation).
        
        Args:
            tweet: The tweet to add
        """
        self.save_tweet(tweet)
    
    def get_tweets_for_week(self, week_start: datetime) -> List[Tweet]:
        """
        Get all tweets for a specific week.
        
        Args:
            week_start: The start date of the week
            
        Returns:
            A list of tweets for the specified week
        """
        try:
            db = get_db_session_sync()
            
            # Calculate week end
            week_end = week_start.replace(hour=23, minute=59, second=59, microsecond=999999)
            week_end = week_end.replace(day=week_start.day + 6)
            
            # Query tweets for the week
            tweet_records = db.query(TweetRecord).filter(
                TweetRecord.approximate_datetime >= week_start,
                TweetRecord.approximate_datetime <= week_end
            ).order_by(TweetRecord.approximate_datetime.desc()).all()
            
            # Convert to domain models
            tweets = []
            for record in tweet_records:
                tweet = Tweet(
                    id=record.id,
                    text=record.text,
                    time_ago=record.time_ago,
                    publish_date=record.publish_date,
                    scraped_at=record.scraped_at,
                    approximate_datetime=record.approximate_datetime,
                    url=record.url,
                    analysis_status=record.analysis_status,
                    analyzed_at=record.analyzed_at
                )
                tweets.append(tweet)
            
            self.logger.info(f"Retrieved {len(tweets)} tweets for week starting {week_start}")
            return tweets
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error getting tweets for week {week_start}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Error getting tweets for week {week_start}: {e}")
            return []
        finally:
            db.close()
    
    def get_tweets_by_status(self, status: str, limit: Optional[int] = None) -> List[Tweet]:
        """
        Get tweets by analysis status.
        
        Args:
            status: Analysis status to filter by
            limit: Maximum number of tweets to return
            
        Returns:
            List of tweets with the specified status
        """
        try:
            db = get_db_session_sync()
            
            query = db.query(TweetRecord).filter(TweetRecord.analysis_status == status)
            
            if limit:
                query = query.limit(limit)
            
            tweet_records = query.order_by(TweetRecord.scraped_at.desc()).all()
            
            # Convert to domain models
            tweets = []
            for record in tweet_records:
                tweet = Tweet(
                    id=record.id,
                    text=record.text,
                    time_ago=record.time_ago,
                    publish_date=record.publish_date,
                    scraped_at=record.scraped_at,
                    approximate_datetime=record.approximate_datetime,
                    url=record.url,
                    analysis_status=record.analysis_status,
                    analyzed_at=record.analyzed_at
                )
                tweets.append(tweet)
            
            self.logger.info(f"Retrieved {len(tweets)} tweets with status '{status}'")
            return tweets
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error getting tweets by status {status}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Error getting tweets by status {status}: {e}")
            return []
        finally:
            db.close()
    
    def get_pending_analysis_tweets(self, limit: Optional[int] = None) -> List[Tweet]:
        """
        Get tweets that are pending analysis.
        
        Args:
            limit: Maximum number of tweets to return
            
        Returns:
            List of tweets pending analysis
        """
        return self.get_tweets_by_status("pending", limit)
    
    def update_tweet_analysis_status(self, tweet_id: str, status: str, analyzed_at: Optional[datetime] = None) -> bool:
        """
        Update the analysis status of a tweet.
        
        Args:
            tweet_id: ID of the tweet to update
            status: New analysis status
            analyzed_at: When the analysis was completed (optional)
            
        Returns:
            True if updated successfully
        """
        try:
            db = get_db_session_sync()
            
            tweet_record = db.query(TweetRecord).filter(TweetRecord.id == tweet_id).first()
            
            if not tweet_record:
                self.logger.warning(f"Tweet {tweet_id} not found for status update")
                return False
            
            tweet_record.analysis_status = status
            if analyzed_at:
                tweet_record.analyzed_at = analyzed_at
            
            db.commit()
            
            self.logger.info(f"Updated tweet {tweet_id} analysis status to '{status}'")
            return True
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error updating tweet {tweet_id} status: {e}")
            db.rollback()
            return False
        except Exception as e:
            self.logger.error(f"Error updating tweet {tweet_id} status: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def get_tweet_count(self) -> int:
        """
        Get the total number of tweets in the database.
        
        Returns:
            Total tweet count
        """
        try:
            db = get_db_session_sync()
            
            count = db.query(TweetRecord).count()
            
            return count
            
        except SQLAlchemyError as e:
            self.logger.error(f"Database error getting tweet count: {e}")
            return 0
        except Exception as e:
            self.logger.error(f"Error getting tweet count: {e}")
            return 0
        finally:
            db.close()
