"""
Base repository interfaces for the Tweet Scraper service.
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from datetime import datetime

from tweet_scraper.domain.models import Tweet, TweetDatabase


class TweetRepository(ABC):
    """
    Abstract base class for tweet repositories.
    """
    
    @abstractmethod
    def save_tweet(self, tweet: Tweet) -> None:
        """
        Save a tweet to the repository.
        
        Args:
            tweet: The tweet to save
        """
        pass
    
    @abstractmethod
    def is_tweet_scraped(self, tweet_id: str) -> bool:
        """
        Check if a tweet has already been scraped.
        
        Args:
            tweet_id: The ID of the tweet to check
            
        Returns:
            True if the tweet has been scraped, False otherwise
        """
        pass
    
    @abstractmethod
    def add_tweet_to_db(self, tweet: Tweet) -> None:
        """
        Add a tweet to the database of scraped tweets.
        
        Args:
            tweet: The tweet to add
        """
        pass
    
    @abstractmethod
    def get_tweets_for_week(self, week_start: datetime) -> List[Tweet]:
        """
        Get all tweets for a specific week.

        Args:
            week_start: The start date of the week

        Returns:
            A list of tweets for the specified week
        """
        pass

    def get_tweets_by_status(self, status: str, limit: Optional[int] = None) -> List[Tweet]:
        """
        Get tweets by analysis status.

        Args:
            status: Analysis status to filter by
            limit: Maximum number of tweets to return

        Returns:
            List of tweets with the specified status
        """
        # Default implementation for backward compatibility
        return []

    def get_pending_analysis_tweets(self, limit: Optional[int] = None) -> List[Tweet]:
        """
        Get tweets that are pending analysis.

        Args:
            limit: Maximum number of tweets to return

        Returns:
            List of tweets pending analysis
        """
        return self.get_tweets_by_status("pending", limit)

    def update_tweet_analysis_status(self, tweet_id: str, status: str, analyzed_at: Optional[datetime] = None) -> bool:
        """
        Update the analysis status of a tweet.

        Args:
            tweet_id: ID of the tweet to update
            status: New analysis status
            analyzed_at: When the analysis was completed (optional)

        Returns:
            True if updated successfully
        """
        # Default implementation for backward compatibility
        return False
