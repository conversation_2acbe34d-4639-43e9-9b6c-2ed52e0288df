"""
File-based repository implementation for the Tweet Scraper service.
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional

from tweet_scraper.domain.models import Tweet, TweetDatabase
from tweet_scraper.repositories.base import TweetRepository


class FileTweetRepository(TweetRepository):
    """
    File-based implementation of the TweetRepository interface.
    
    Stores tweets in JSON files organized by week.
    """
    
    def __init__(self, data_dir: Path = Path('data/tweets')):
        """
        Initialize the repository with the data directory.
        
        Args:
            data_dir: The directory where tweet data will be stored
        """
        self.data_dir = data_dir
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize database file
        self.db_file = self.data_dir / 'tweet_database.json'
        if not self.db_file.exists():
            with open(self.db_file, 'w') as f:
                json.dump({"tweets": []}, f)
        
        self.logger = logging.getLogger('FileTweetRepository')
    
    def get_week_folder(self, date: Optional[datetime] = None) -> Path:
        """
        Get the folder path for a specific week.
        
        Args:
            date: The date for which to get the week folder. Defaults to the current date.
            
        Returns:
            The path to the week folder
        """
        if date is None:
            date = datetime.now()
        
        # Get the Monday of the week
        monday = date - timedelta(days=date.weekday())
        week_folder = f"week_{monday.strftime('%Y-%m-%d')}"
        
        # Create the week folder if it doesn't exist
        week_path = self.data_dir / week_folder
        week_path.mkdir(exist_ok=True)
        
        return week_path
    
    def save_tweet(self, tweet: Tweet) -> None:
        """
        Save a tweet to a JSON file in the appropriate week folder.
        
        Args:
            tweet: The tweet to save
        """
        week_folder = self.get_week_folder(datetime.fromisoformat(str(tweet.approximate_datetime)))
        tweet_file = week_folder / f"tweet_{tweet.id}.json"
        
        with open(tweet_file, 'w') as f:
            f.write(tweet.json(indent=2))
        
        self.logger.info(f"Saved tweet {tweet.id} to {tweet_file}")
    
    def is_tweet_scraped(self, tweet_id: str) -> bool:
        """
        Check if a tweet has already been scraped by looking for its ID in the database.
        
        Args:
            tweet_id: The ID of the tweet to check
            
        Returns:
            True if the tweet has been scraped, False otherwise
        """
        with open(self.db_file, "r") as f:
            db = json.load(f)
        
        return tweet_id in db["tweets"]
    
    def add_tweet_to_db(self, tweet: Tweet) -> None:
        """
        Add a tweet's ID to the database of scraped tweets.
        
        Args:
            tweet: The tweet to add
        """
        with open(self.db_file, 'r') as f:
            db = json.load(f)
        
        if tweet.id not in db["tweets"]:
            db["tweets"].append(tweet.id)
            
            with open(self.db_file, 'w') as f:
                json.dump(db, f, indent=2)
            
            self.logger.info(f"Added tweet {tweet.id} to database")
    
    def get_tweets_for_week(self, week_start: datetime) -> List[Tweet]:
        """
        Get all tweets for a specific week.
        
        Args:
            week_start: The start date of the week
            
        Returns:
            A list of tweets for the specified week
        """
        week_folder = self.get_week_folder(week_start)
        tweets = []
        
        if not week_folder.exists():
            self.logger.warning(f"Week folder {week_folder} does not exist")
            return tweets
        
        for tweet_file in week_folder.glob('tweet_*.json'):
            try:
                with open(tweet_file, 'r') as f:
                    tweet_data = json.load(f)
                    # Convert the JSON data to a Tweet object
                    tweet = Tweet(
                        id=tweet_data['id'],
                        text=tweet_data['text'],
                        time_ago=tweet_data['time_ago'],
                        publish_date=tweet_data['publish_date'],
                        scraped_at=datetime.fromisoformat(tweet_data['scraped_at']),
                        approximate_datetime=datetime.fromisoformat(tweet_data['approximate_datetime'])
                    )
                    tweets.append(tweet)
            except Exception as e:
                self.logger.error(f"Error reading tweet file {tweet_file}: {str(e)}")
        
        # Sort tweets by approximate_datetime in descending order (newest first)
        tweets.sort(key=lambda x: x.approximate_datetime, reverse=True)
        
        return tweets
