"""
Tweet analysis service using Google Gemini AI.
"""

import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from prometheus_client import Counter, Histogram, Gauge

from tweet_scraper.domain.config import TweetScraperConfig
from tweet_scraper.domain.analysis_models import TweetAnalysisResult, AnalysisStatus, AnalysisRequest
from tweet_scraper.domain.exceptions import ConfigurationError, NetworkError
from tweet_scraper.services.gemini_client import GeminiClient
from tweet_scraper.database import get_db_session_sync
from tweet_scraper.database.models import (
    TweetRecord, AnalysisResultRecord, AffectedAssetRecord, 
    AnalysisRequestRecord, AnalysisMetricsRecord
)

logger = logging.getLogger(__name__)

# Prometheus metrics
analysis_requests_total = Counter('tweet_analysis_requests_total', 'Total number of analysis requests', ['status'])
analysis_duration_seconds = Histogram('tweet_analysis_duration_seconds', 'Time spent analyzing tweets')
analysis_cost_usd = Counter('tweet_analysis_cost_usd_total', 'Total cost of analyses in USD')
pending_analyses_gauge = Gauge('tweet_analysis_pending_total', 'Number of pending analyses')
manual_review_gauge = Gauge('tweet_analysis_manual_review_total', 'Number of analyses requiring manual review')


class TweetAnalysisService:
    """
    Service for analyzing tweets using Google Gemini AI.
    """
    
    def __init__(self, config: TweetScraperConfig):
        """
        Initialize the analysis service.
        
        Args:
            config: Configuration object with API keys and settings
        """
        self.config = config
        self.logger = logging.getLogger('TweetAnalysisService')
        
        # Validate configuration
        if not config.enable_analysis:
            self.logger.info("Tweet analysis is disabled")
            self.gemini_client = None
            self.gemini_free_client = None
            return
        
        # Initialize Gemini clients
        self._init_gemini_clients()
        
        self.logger.info("Tweet analysis service initialized")
    
    def _init_gemini_clients(self):
        """Initialize Gemini API clients."""
        # Production client
        if self.config.gemini_api_key:
            try:
                self.gemini_client = GeminiClient(
                    api_key=self.config.gemini_api_key,
                    model_name=self.config.gemini_model,
                    max_retries=self.config.analysis_max_retries
                )
                self.logger.info(f"Production Gemini client initialized with model: {self.config.gemini_model}")
            except Exception as e:
                self.logger.error(f"Failed to initialize production Gemini client: {e}")
                self.gemini_client = None
        else:
            self.gemini_client = None
            self.logger.warning("No production Gemini API key provided")
        
        # Free tier client for testing
        if self.config.gemini_free_api_key:
            try:
                self.gemini_free_client = GeminiClient(
                    api_key=self.config.gemini_free_api_key,
                    model_name=self.config.gemini_free_model,
                    max_retries=self.config.analysis_max_retries
                )
                self.logger.info(f"Free Gemini client initialized with model: {self.config.gemini_free_model}")
            except Exception as e:
                self.logger.error(f"Failed to initialize free Gemini client: {e}")
                self.gemini_free_client = None
        else:
            self.gemini_free_client = None
            self.logger.warning("No free Gemini API key provided")
        
        # Ensure at least one client is available
        if not self.gemini_client and not self.gemini_free_client:
            raise ConfigurationError("No valid Gemini API keys provided")
    
    def queue_tweet_for_analysis(self, tweet_id: str, tweet_text: str, tweet_url: Optional[str] = None, priority: int = 0) -> bool:
        """
        Queue a tweet for analysis.
        
        Args:
            tweet_id: ID of the tweet
            tweet_text: Content of the tweet
            tweet_url: URL of the tweet (optional)
            priority: Priority level (higher = more urgent)
            
        Returns:
            True if successfully queued
        """
        if not self.config.enable_analysis:
            self.logger.debug("Analysis disabled, skipping queue")
            return False
        
        try:
            db = get_db_session_sync()
            
            # Check if already queued or analyzed
            existing_request = db.query(AnalysisRequestRecord).filter(
                AnalysisRequestRecord.tweet_id == tweet_id
            ).first()
            
            if existing_request:
                self.logger.debug(f"Tweet {tweet_id} already queued for analysis")
                return False
            
            # Check if already analyzed
            existing_result = db.query(AnalysisResultRecord).filter(
                AnalysisResultRecord.tweet_id == tweet_id
            ).first()
            
            if existing_result:
                self.logger.debug(f"Tweet {tweet_id} already analyzed")
                return False
            
            # Create analysis request
            request = AnalysisRequestRecord(
                tweet_id=tweet_id,
                tweet_text=tweet_text,
                tweet_url=tweet_url,
                priority=priority,
                status="pending"
            )
            
            db.add(request)
            db.commit()
            
            self.logger.info(f"Queued tweet {tweet_id} for analysis")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to queue tweet {tweet_id} for analysis: {e}")
            return False
        finally:
            db.close()
    
    def process_pending_analyses(self, batch_size: Optional[int] = None) -> int:
        """
        Process pending tweet analyses in batches.
        
        Args:
            batch_size: Number of tweets to process (uses config default if None)
            
        Returns:
            Number of tweets processed
        """
        if not self.config.enable_analysis:
            self.logger.debug("Analysis disabled, skipping processing")
            return 0
        
        if not self.gemini_client and not self.gemini_free_client:
            self.logger.error("No Gemini clients available")
            return 0
        
        batch_size = batch_size or self.config.analysis_batch_size
        processed_count = 0
        
        try:
            db = get_db_session_sync()
            
            # Get pending requests ordered by priority and creation time
            pending_requests = db.query(AnalysisRequestRecord).filter(
                AnalysisRequestRecord.status == "pending"
            ).order_by(
                AnalysisRequestRecord.priority.desc(),
                AnalysisRequestRecord.requested_at.asc()
            ).limit(batch_size).all()
            
            if not pending_requests:
                self.logger.debug("No pending analyses found")
                return 0
            
            self.logger.info(f"Processing {len(pending_requests)} pending analyses")
            
            for request in pending_requests:
                try:
                    # Mark as processing
                    request.status = "processing"
                    request.started_at = datetime.now()
                    db.commit()
                    
                    # Perform analysis
                    result = self._analyze_tweet(request)
                    
                    if result:
                        # Save result to database
                        self._save_analysis_result(db, result)
                        
                        # Mark request as completed
                        request.status = "completed"
                        request.completed_at = datetime.now()
                        
                        processed_count += 1
                        analysis_requests_total.labels(status='success').inc()
                        
                        self.logger.info(f"Successfully analyzed tweet {request.tweet_id}")
                    else:
                        # Mark as failed
                        request.status = "failed"
                        request.retry_count += 1
                        request.last_error = "Analysis returned no result"
                        analysis_requests_total.labels(status='failed').inc()
                        
                        self.logger.error(f"Analysis failed for tweet {request.tweet_id}")
                    
                    db.commit()
                    
                except Exception as e:
                    # Handle individual analysis failure
                    self.logger.error(f"Error analyzing tweet {request.tweet_id}: {e}")
                    
                    request.status = "failed"
                    request.retry_count += 1
                    request.last_error = str(e)
                    analysis_requests_total.labels(status='error').inc()
                    
                    db.commit()
                    continue
            
            # Update metrics
            self._update_metrics(db)
            
            return processed_count
            
        except Exception as e:
            self.logger.error(f"Error processing pending analyses: {e}")
            return processed_count
        finally:
            db.close()
    
    def _analyze_tweet(self, request: AnalysisRequestRecord) -> Optional[TweetAnalysisResult]:
        """
        Analyze a single tweet using the appropriate Gemini client.
        
        Args:
            request: Analysis request record
            
        Returns:
            Analysis result or None if failed
        """
        # Choose client (prefer production, fallback to free)
        client = self.gemini_client or self.gemini_free_client
        
        if not client:
            self.logger.error("No Gemini client available")
            return None
        
        try:
            with analysis_duration_seconds.time():
                result = client.analyze_tweet(
                    tweet_id=request.tweet_id,
                    tweet_text=request.tweet_text,
                    tweet_url=request.tweet_url
                )
            
            # Update cost metrics
            analysis_cost_usd.inc(result.estimated_cost_usd)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Gemini analysis failed for tweet {request.tweet_id}: {e}")
            return None
    
    def _save_analysis_result(self, db: Session, result: TweetAnalysisResult) -> None:
        """
        Save analysis result to database.
        
        Args:
            db: Database session
            result: Analysis result to save
        """
        # Create analysis result record
        result_record = AnalysisResultRecord(
            tweet_id=result.tweet_id,
            tweet_url=result.tweet_url,
            financial_impact_percentage=result.financial_impact_percentage,
            political_impact_percentage=result.political_impact_percentage,
            analysis_timestamp=result.analysis_timestamp,
            model_version=result.model_version,
            processing_time_ms=result.processing_time_ms,
            token_usage=result.token_usage,
            estimated_cost_usd=result.estimated_cost_usd,
            raw_model_output=result.raw_model_output,
            status=result.status.value,
            confidence_score=result.confidence_score,
            needs_manual_review=result.needs_manual_review,
            manual_review_reason=result.manual_review_reason
        )
        
        db.add(result_record)
        db.flush()  # Get the ID
        
        # Create affected asset records
        for asset in result.affected_assets:
            asset_record = AffectedAssetRecord(
                analysis_result_id=result_record.id,
                symbol=asset.symbol,
                name=asset.name,
                asset_type=asset.asset_type.value,
                impact_direction=asset.impact_direction.value,
                confidence_score=asset.confidence_score,
                reasoning=asset.reasoning
            )
            db.add(asset_record)
        
        # Update tweet record status
        tweet_record = db.query(TweetRecord).filter(TweetRecord.id == result.tweet_id).first()
        if tweet_record:
            tweet_record.analysis_status = "completed"
            tweet_record.analyzed_at = result.analysis_timestamp
    
    def _update_metrics(self, db: Session) -> None:
        """
        Update Prometheus metrics.
        
        Args:
            db: Database session
        """
        try:
            # Count pending analyses
            pending_count = db.query(AnalysisRequestRecord).filter(
                AnalysisRequestRecord.status == "pending"
            ).count()
            pending_analyses_gauge.set(pending_count)
            
            # Count manual reviews needed
            manual_review_count = db.query(AnalysisResultRecord).filter(
                AnalysisResultRecord.needs_manual_review == True
            ).count()
            manual_review_gauge.set(manual_review_count)
            
        except Exception as e:
            self.logger.error(f"Error updating metrics: {e}")
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """
        Get analysis statistics.
        
        Returns:
            Dictionary with analysis statistics
        """
        try:
            db = get_db_session_sync()
            
            stats = {
                "pending_analyses": db.query(AnalysisRequestRecord).filter(
                    AnalysisRequestRecord.status == "pending"
                ).count(),
                "completed_analyses": db.query(AnalysisResultRecord).count(),
                "manual_reviews_needed": db.query(AnalysisResultRecord).filter(
                    AnalysisResultRecord.needs_manual_review == True
                ).count(),
                "total_cost_usd": db.query(AnalysisResultRecord).with_entities(
                    db.func.sum(AnalysisResultRecord.estimated_cost_usd)
                ).scalar() or 0.0,
                "average_confidence": db.query(AnalysisResultRecord).with_entities(
                    db.func.avg(AnalysisResultRecord.confidence_score)
                ).scalar() or 0.0
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting analysis stats: {e}")
            return {}
        finally:
            db.close()
