"""
Google Gemini API client for tweet analysis.
"""

import json
import time
import logging
from typing import Dict, <PERSON>, Optional, Tu<PERSON>
from datetime import datetime

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from tweet_scraper.domain.exceptions import ConfigurationError, NetworkError
from tweet_scraper.domain.analysis_models import TweetAnalysisResult, AffectedAsset, AssetType, ImpactDirection

logger = logging.getLogger(__name__)


class GeminiClient:
    """
    Client for interacting with Google Gemini API for tweet analysis.
    """
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp", max_retries: int = 3):
        """
        Initialize the Gemini client.
        
        Args:
            api_key: Google Gemini API key
            model_name: Name of the Gemini model to use
            max_retries: Maximum number of retries for API calls
        """
        if not api_key:
            raise ConfigurationError("Gemini API key is required")
        
        self.api_key = api_key
        self.model_name = model_name
        self.max_retries = max_retries
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Initialize the model
        self.model = genai.GenerativeModel(
            model_name=model_name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        logger.info(f"Gemini client initialized with model: {model_name}")
    
    def _create_analysis_prompt(self, tweet_text: str) -> str:
        """
        Create a structured prompt for tweet analysis.
        
        Args:
            tweet_text: The tweet content to analyze
            
        Returns:
            Formatted prompt for the AI model
        """
        prompt = f"""
Analyze the following tweet for its potential impact on financial markets and politics. Provide a structured JSON response.

Tweet: "{tweet_text}"

Please analyze and respond with a JSON object containing:

1. "financial_impact_percentage": A number between 0-100 representing the percentage impact on financial markets
2. "political_impact_percentage": A number between 0-100 representing the percentage impact on politics
3. "affected_assets": An array of up to 5 potentially affected assets, each with:
   - "symbol": Asset symbol (e.g., "BTC", "AAPL")
   - "name": Full name of the asset
   - "asset_type": Either "crypto" or "stock"
   - "impact_direction": Either "positive", "negative", or "neutral"
   - "confidence_score": A number between 0-1 representing confidence in this assessment
   - "reasoning": Brief explanation for the impact assessment
4. "confidence_score": Overall confidence in the analysis (0-1)
5. "reasoning": Brief explanation of the overall analysis

Respond ONLY with valid JSON. Do not include any other text or formatting.

Example format:
{{
  "financial_impact_percentage": 25.5,
  "political_impact_percentage": 10.0,
  "affected_assets": [
    {{
      "symbol": "BTC",
      "name": "Bitcoin",
      "asset_type": "crypto",
      "impact_direction": "positive",
      "confidence_score": 0.8,
      "reasoning": "Positive sentiment towards cryptocurrency adoption"
    }}
  ],
  "confidence_score": 0.75,
  "reasoning": "Tweet shows moderate financial impact due to market sentiment"
}}
"""
        return prompt.strip()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    def _make_api_call(self, prompt: str) -> Tuple[str, Dict[str, Any]]:
        """
        Make an API call to Gemini with retry logic.
        
        Args:
            prompt: The prompt to send to the model
            
        Returns:
            Tuple of (response_text, usage_metadata)
        """
        start_time = time.time()
        
        try:
            response = self.model.generate_content(prompt)
            
            # Extract usage metadata
            usage_metadata = {
                "input_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0),
                "output_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0),
                "total_tokens": getattr(response.usage_metadata, 'total_token_count', 0),
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }
            
            if not response.text:
                raise NetworkError("Empty response from Gemini API")
            
            logger.debug(f"Gemini API call successful. Tokens: {usage_metadata}")
            return response.text, usage_metadata
            
        except Exception as e:
            logger.error(f"Gemini API call failed: {str(e)}")
            raise NetworkError(f"Gemini API error: {str(e)}")
    
    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the JSON response from Gemini.
        
        Args:
            response_text: Raw response text from the model
            
        Returns:
            Parsed JSON data
        """
        try:
            # Clean the response text
            cleaned_text = response_text.strip()
            
            # Remove any markdown formatting if present
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]
            
            cleaned_text = cleaned_text.strip()
            
            # Parse JSON
            parsed_data = json.loads(cleaned_text)
            
            # Validate required fields
            required_fields = [
                "financial_impact_percentage",
                "political_impact_percentage", 
                "affected_assets",
                "confidence_score"
            ]
            
            for field in required_fields:
                if field not in parsed_data:
                    raise ValueError(f"Missing required field: {field}")
            
            return parsed_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response_text}")
            raise ValueError(f"Invalid JSON response from model: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            raise ValueError(f"Error parsing model response: {str(e)}")
    
    def analyze_tweet(self, tweet_id: str, tweet_text: str, tweet_url: Optional[str] = None) -> TweetAnalysisResult:
        """
        Analyze a tweet for financial and political impact.
        
        Args:
            tweet_id: ID of the tweet
            tweet_text: Content of the tweet
            tweet_url: URL of the tweet (optional)
            
        Returns:
            TweetAnalysisResult with analysis data
        """
        start_time = time.time()
        
        try:
            # Create prompt
            prompt = self._create_analysis_prompt(tweet_text)
            
            # Make API call
            response_text, usage_metadata = self._make_api_call(prompt)
            
            # Parse response
            parsed_data = self._parse_response(response_text)
            
            # Create affected assets
            affected_assets = []
            for asset_data in parsed_data.get("affected_assets", [])[:5]:  # Limit to 5
                try:
                    asset = AffectedAsset(
                        symbol=asset_data["symbol"],
                        name=asset_data["name"],
                        asset_type=AssetType(asset_data["asset_type"]),
                        impact_direction=ImpactDirection(asset_data["impact_direction"]),
                        confidence_score=float(asset_data["confidence_score"]),
                        reasoning=asset_data["reasoning"]
                    )
                    affected_assets.append(asset)
                except Exception as e:
                    logger.warning(f"Failed to parse affected asset: {e}")
                    continue
            
            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            
            # Estimate cost (rough estimate for Gemini pricing)
            estimated_cost = self._estimate_cost(usage_metadata)
            
            # Create analysis result
            result = TweetAnalysisResult(
                tweet_id=tweet_id,
                tweet_url=tweet_url,
                financial_impact_percentage=float(parsed_data["financial_impact_percentage"]),
                political_impact_percentage=float(parsed_data["political_impact_percentage"]),
                affected_assets=affected_assets,
                analysis_timestamp=datetime.now(),
                model_version=self.model_name,
                processing_time_ms=processing_time_ms,
                token_usage=usage_metadata,
                estimated_cost_usd=estimated_cost,
                raw_model_output=response_text,
                confidence_score=float(parsed_data["confidence_score"]),
                needs_manual_review=self._should_flag_for_review(parsed_data, response_text)
            )
            
            logger.info(f"Successfully analyzed tweet {tweet_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze tweet {tweet_id}: {str(e)}")
            raise
    
    def _estimate_cost(self, usage_metadata: Dict[str, Any]) -> float:
        """
        Estimate the cost of the API call based on token usage.
        
        Args:
            usage_metadata: Token usage information
            
        Returns:
            Estimated cost in USD
        """
        # Rough pricing estimates for Gemini (these may need updating)
        input_cost_per_1k = 0.00025  # $0.00025 per 1K input tokens
        output_cost_per_1k = 0.0005   # $0.0005 per 1K output tokens
        
        input_tokens = usage_metadata.get("input_tokens", 0)
        output_tokens = usage_metadata.get("output_tokens", 0)
        
        input_cost = (input_tokens / 1000) * input_cost_per_1k
        output_cost = (output_tokens / 1000) * output_cost_per_1k
        
        return input_cost + output_cost
    
    def _should_flag_for_review(self, parsed_data: Dict[str, Any], raw_response: str) -> bool:
        """
        Determine if the analysis should be flagged for manual review.
        
        Args:
            parsed_data: Parsed analysis data
            raw_response: Raw model response
            
        Returns:
            True if manual review is needed
        """
        # Flag for review if confidence is very low
        if parsed_data.get("confidence_score", 1.0) < 0.3:
            return True
        
        # Flag if impact percentages are very high
        financial_impact = parsed_data.get("financial_impact_percentage", 0)
        political_impact = parsed_data.get("political_impact_percentage", 0)
        
        if financial_impact > 80 or political_impact > 80:
            return True
        
        # Flag if no affected assets were identified for high impact tweets
        if (financial_impact > 50 or political_impact > 50) and not parsed_data.get("affected_assets"):
            return True
        
        return False
