"""
Service for scraping tweets from a website.

This module provides the core functionality for scraping tweets from a configured URL
and storing them using a repository. It includes retry mechanisms for resilience
and proper error handling.
"""

import os
import time
import random
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_log, after_log

from tweet_scraper.domain.models import Tweet
from tweet_scraper.domain.config import TweetScraperConfig, load_config_from_env
from tweet_scraper.domain.exceptions import (
    TweetScraperError, ConfigurationError, ScrapingError,
    NetworkError, ParsingError, StorageError, RateLimitError
)
from tweet_scraper.repositories.base import TweetRepository


class TweetScraperService:
    """
    Service for scraping tweets from a website.

    This service is responsible for:
    1. Scraping tweets from a configured URL
    2. Parsing the tweet data
    3. Storing the tweets using a repository
    4. Scheduling periodic scraping

    It includes retry mechanisms for resilience and proper error handling.
    """

    def __init__(self, repository: TweetRepository, config: Optional[TweetScraperConfig] = None):
        """
        Initialize the TweetScraperService.

        Args:
            repository: The repository to use for storing tweets
            config: Configuration for the service. If None, loads from environment variables.

        Raises:
            ConfigurationError: If the configuration is invalid or missing required values
        """
        self.repository = repository
        self.analysis_service = None  # Will be set later if analysis is enabled

        # Load configuration
        self.config = config or load_config_from_env()

        # Configure logging
        self.logger = logging.getLogger('TweetScraperService')

        self.logger.info(f"TweetScraperService initialized with URL: {self.config.url}")

    def set_analysis_service(self, analysis_service):
        """
        Set the analysis service for queuing tweets for analysis.

        Args:
            analysis_service: TweetAnalysisService instance
        """
        self.analysis_service = analysis_service
        self.logger.info("Analysis service integration enabled")

    def setup_driver(self) -> webdriver.Chrome:
        """
        Set up and return a configured Chrome WebDriver.

        Returns:
            A configured Chrome WebDriver instance

        Raises:
            ScrapingError: If there is an error setting up the WebDriver
        """
        try:
            chrome_options = Options()

            if not self.debug_mode:
                chrome_options.add_argument("--headless=new")  # Usa nuevo headless mode
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                chrome_options.add_argument(
                    "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36")
                self.logger.info("Running in headless mode with stealth options")
            else:
                self.logger.info("Running in debug mode with visible browser")

            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")

            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Avoid being detected as a bot
            driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                "source": """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    })
                """
            })

            return driver
        except Exception as e:
            self.logger.error(f"Error setting up WebDriver: {str(e)}")
            raise ScrapingError(f"Failed to set up WebDriver: {str(e)}") from e

    def parse_time_ago(self, time_ago_text: str) -> datetime:
        """
        Parse the 'time ago' text to get an approximate datetime.

        Args:
            time_ago_text: Text describing how long ago the tweet was posted (e.g., "2h", "5m")

        Returns:
            An approximate datetime for when the tweet was posted
        """
        now = datetime.now()

        if 'm' in time_ago_text and 'min' in time_ago_text:
            minutes = int(time_ago_text.split('m')[0])
            return now - timedelta(minutes=minutes)
        elif 'h' in time_ago_text:
            hours = int(time_ago_text.split('h')[0])
            return now - timedelta(hours=hours)
        elif 'd' in time_ago_text:
            days = int(time_ago_text.split('d')[0])
            return now - timedelta(days=days)
        else:
            # Default to current time if format is unknown
            return now

    def _get_retry_decorator(self):
        """
        Get a configured retry decorator based on the current configuration.

        Returns:
            A configured retry decorator
        """
        # Define a before_sleep callback that logs the retry attempt
        def before_sleep_log(retry_state):
            self.logger.info(f"Attempting to load URL... (Attempt {retry_state.attempt_number})")

        return retry(
            stop=stop_after_attempt(self.config.max_retries),
            wait=wait_exponential(
                multiplier=self.config.retry_multiplier,
                min=self.config.retry_min_wait,
                max=self.config.retry_max_wait
            ),
            retry=retry_if_exception_type((TimeoutException, WebDriverException)),
            before_sleep=before_sleep_log,
            reraise=True
        )
    def query_with_retries(self, driver: webdriver.Chrome, url: str) -> bool:
        """
        Load a URL with retry logic for handling HTTP errors.

        Args:
            driver: Selenium WebDriver instance
            url: URL to load

        Returns:
            True if successful, False otherwise

        Raises:
            NetworkError: If there is a network-related error after all retries
            RateLimitError: If the scraper hits a rate limit
            ScrapingError: If there is any other error during scraping
        """
        # Apply the retry decorator dynamically
        retry_decorator = self._get_retry_decorator()

        # Define the function to retry
        @retry_decorator
        def _query_with_retries(driver, url):
            try:
                self.logger.info(f"Loading URL: {url}")
                driver.get(url)

                # Wait for the page to load
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='status']"))
                )

                # Check for rate limiting or server errors in the page source
                page_source = driver.page_source.lower()
                if "429" in page_source or "too many requests" in page_source:
                    self.logger.warning("Rate limited (429)")
                    raise RateLimitError("Rate limited by the server (429)")
                elif "500" in page_source or "server error" in page_source:
                    self.logger.warning("Server error (500)")
                    raise NetworkError("Server error (500)")

                # If we get here, the page loaded successfully
                return True

            except TimeoutException as e:
                self.logger.warning(f"Timeout error: {str(e)}")
                raise NetworkError(f"Timeout while loading URL: {str(e)}") from e

            except WebDriverException as e:
                self.logger.warning(f"WebDriver error: {str(e)}")
                raise NetworkError(f"WebDriver error: {str(e)}") from e

            except (RateLimitError, NetworkError):
                # Re-raise these specific exceptions for proper handling
                raise

            except Exception as e:
                self.logger.error(f"Unexpected error: {str(e)}")
                raise ScrapingError(f"Unexpected error while loading URL: {str(e)}") from e

        # Call the inner function with retry logic
        return _query_with_retries(driver, url)

    def scrape_tweets(self) -> int:
        """
        Scrape tweets from the configured URL.

        Returns:
            The number of new tweets scraped

        Raises:
            ScrapingError: If there is an error during the scraping process
            NetworkError: If there is a network-related error
            RateLimitError: If the scraper hits a rate limit
            StorageError: If there is an error storing the tweets
        """
        self.logger.info("Starting tweet scraping")

        driver = None
        tweets_scraped = 0

        try:
            # Set up the WebDriver
            driver = self.setup_driver()

            # Load the page with retry logic
            self.query_with_retries(driver, self.config.url)

            # No scrolling - just get the tweets that are initially visible
            # This ensures we only get the most recent tweets
            self.logger.info("Skipping scrolling to focus on most recent tweets only")

            # Get the page source
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # Find all tweet elements
            tweet_elements = soup.select("[data-testid='status']")
            self.logger.info(f"Found {len(tweet_elements)} tweets")

            # Limit to the configured maximum number of tweets
            tweet_elements = tweet_elements[:self.config.max_tweets_per_scrape]
            self.logger.info(f"Processing only the latest {self.config.max_tweets_per_scrape} tweets")

            # Reverse the order to process from newest to oldest
            tweet_elements.reverse()
            self.logger.info(f"Reversed order to process from newest to oldest")

            for tweet_element in tweet_elements:
                try:
                    # Extract tweet ID
                    tweet_id = tweet_element.select_one("[data-id]")
                    if tweet_id:
                        tweet_id = tweet_id.get('data-id')
                    else:
                        # Generate a unique ID if none is found
                        tweet_id = f"tweet_{int(time.time())}_{random.randint(1000, 9999)}"

                    # Skip if already scraped
                    if self.repository.is_tweet_scraped(tweet_id):
                        self.logger.info(f"Tweet {tweet_id} already scraped, skipping")
                        continue

                    # Extract tweet text
                    tweet_text_element = tweet_element.select_one("[data-markup='true']")
                    tweet_text = tweet_text_element.get_text(strip=True) if tweet_text_element else "No text found"

                    # Extract time ago
                    time_element = tweet_element.select_one("time")
                    time_ago = time_element.get_text(strip=True) if time_element else "Unknown"

                    # Extract publish date from title attribute
                    publish_date_str = time_element.get('title') if time_element else None
                    publish_date = publish_date_str if publish_date_str else "Unknown"

                    # Parse the time ago to get an approximate datetime
                    approximate_datetime = self.parse_time_ago(time_ago)

                    # Generate tweet URL if possible (basic format)
                    tweet_url = f"https://twitter.com/i/status/{tweet_id}" if tweet_id.startswith('tweet_') == False else None

                    # Create tweet object
                    tweet = Tweet(
                        id=tweet_id,
                        text=tweet_text,
                        time_ago=time_ago,
                        publish_date=publish_date,
                        scraped_at=datetime.now(),
                        approximate_datetime=approximate_datetime,
                        url=tweet_url,
                        analysis_status="pending" if self.analysis_service else "disabled"
                    )

                    # Save tweet to repository
                    self.repository.save_tweet(tweet)

                    # Add to database
                    self.repository.add_tweet_to_db(tweet)

                    # Queue for analysis if analysis service is available
                    if self.analysis_service:
                        try:
                            self.analysis_service.queue_tweet_for_analysis(
                                tweet_id=tweet_id,
                                tweet_text=tweet_text,
                                tweet_url=tweet_url
                            )
                            self.logger.debug(f"Queued tweet {tweet_id} for analysis")
                        except Exception as e:
                            self.logger.warning(f"Failed to queue tweet {tweet_id} for analysis: {e}")

                    tweets_scraped += 1
                    self.logger.info(f"Scraped tweet {tweet_id}")

                except NoSuchElementException as e:
                    self.logger.warning(f"Element not found while parsing tweet: {str(e)}")
                    continue
                except ParsingError as e:
                    self.logger.warning(f"Error parsing tweet: {str(e)}")
                    continue
                except StorageError as e:
                    self.logger.error(f"Error storing tweet: {str(e)}")
                    raise
                except Exception as e:
                    self.logger.error(f"Unexpected error scraping tweet: {str(e)}")
                    raise ParsingError(f"Failed to parse tweet: {str(e)}") from e

            self.logger.info(f"Scraped {tweets_scraped} new tweets")

        except (NetworkError, RateLimitError) as e:
            # These are already logged in query_with_retries
            raise
        except StorageError as e:
            # These are already logged in the repository
            raise
        except ScrapingError as e:
            # These are already logged where they occurred
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during scraping: {str(e)}")
            raise ScrapingError(f"Unexpected error during scraping: {str(e)}") from e
        finally:
            if driver:
                try:
                    driver.quit()
                except Exception as e:
                    self.logger.warning(f"Error closing WebDriver: {str(e)}")

        return tweets_scraped

    def run_scheduled(self):
        """
        Run the scraper on a schedule with random intervals.

        Uses the configured min_interval and max_interval for scheduling.

        Raises:
            ConfigurationError: If the configuration is invalid
            ScrapingError: If there is an error during scraping
        """
        import schedule

        min_interval = self.config.min_interval
        max_interval = self.config.max_interval

        self.logger.info(f"Starting scheduled scraping with intervals between {min_interval} and {max_interval} minutes")

        def job():
            try:
                # Scrape new tweets
                scraped_count = self.scrape_tweets()

                # Process pending analyses if analysis service is available
                if self.analysis_service:
                    try:
                        analyzed_count = self.analysis_service.process_pending_analyses()
                        if analyzed_count > 0:
                            self.logger.info(f"Analyzed {analyzed_count} tweets")
                    except Exception as e:
                        self.logger.error(f"Error processing analyses: {e}")

                # Schedule next run with random interval
                minutes = random.randint(min_interval, max_interval)
                self.logger.info(f"Next scrape scheduled in {minutes} minutes")

                # Clear existing jobs and schedule a new one
                schedule.clear()
                schedule.every(minutes).minutes.do(job)

            except RateLimitError as e:
                # If we hit a rate limit, wait longer before the next attempt
                self.logger.warning(f"Rate limited. Scheduling next attempt with longer delay: {str(e)}")
                schedule.clear()
                # Double the max interval for the next attempt
                extended_wait = max_interval * 2
                self.logger.info(f"Next scrape scheduled in {extended_wait} minutes due to rate limiting")
                schedule.every(extended_wait).minutes.do(job)

            except (NetworkError, ScrapingError, StorageError) as e:
                # Log the error but continue with normal scheduling
                self.logger.error(f"Error in scheduled job: {str(e)}")
                schedule.clear()
                minutes = random.randint(min_interval, max_interval)
                self.logger.info(f"Next scrape scheduled in {minutes} minutes despite error")
                schedule.every(minutes).minutes.do(job)

            except Exception as e:
                self.logger.error(f"Unexpected error in scheduled job: {str(e)}")
                # Still try to schedule the next run
                schedule.clear()
                minutes = random.randint(min_interval, max_interval)
                self.logger.info(f"Next scrape scheduled in {minutes} minutes despite unexpected error")
                schedule.every(minutes).minutes.do(job)

        # Initial run
        job()

        # Keep the script running
        while True:
            schedule.run_pending()
            time.sleep(1)
