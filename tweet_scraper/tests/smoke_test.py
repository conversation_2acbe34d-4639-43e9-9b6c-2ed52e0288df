#!/usr/bin/env python3
"""
Smoke test for tweet analysis with Google Gemini.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tweet_scraper.services.gemini_client import GeminiC<PERSON>
from tweet_scraper.domain.exceptions import ConfigurationError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_gemini_connectivity():
    """Test basic connectivity to Gemini API."""
    
    # Check for API keys
    api_key = os.environ.get('GEMINI_FREE_API_KEY') or os.environ.get('GEMINI_API_KEY')
    
    if not api_key:
        logger.error("No Gemini API key found. Set GEMINI_FREE_API_KEY or GEMINI_API_KEY environment variable.")
        return False
    
    try:
        # Use free model for smoke test
        model_name = "gemini-1.5-flash" if 'GEMINI_FREE_API_KEY' in os.environ else "gemini-2.0-flash-exp"
        
        logger.info(f"Testing Gemini connectivity with model: {model_name}")
        
        # Initialize client
        client = GeminiClient(api_key=api_key, model_name=model_name)
        
        # Test with a simple tweet
        test_tweet = "Bitcoin price surges to new all-time high as institutional adoption increases"
        
        logger.info(f"Analyzing test tweet: {test_tweet}")
        
        # Perform analysis
        result = client.analyze_tweet(
            tweet_id="smoke_test_001",
            tweet_text=test_tweet,
            tweet_url="https://twitter.com/test/smoke_test_001"
        )
        
        # Validate result
        assert result.tweet_id == "smoke_test_001"
        assert 0 <= result.financial_impact_percentage <= 100
        assert 0 <= result.political_impact_percentage <= 100
        assert 0 <= result.confidence_score <= 1
        assert len(result.affected_assets) <= 5
        
        logger.info("✅ Smoke test PASSED")
        logger.info(f"Financial Impact: {result.financial_impact_percentage}%")
        logger.info(f"Political Impact: {result.political_impact_percentage}%")
        logger.info(f"Confidence Score: {result.confidence_score}")
        logger.info(f"Affected Assets: {len(result.affected_assets)}")
        logger.info(f"Processing Time: {result.processing_time_ms}ms")
        logger.info(f"Estimated Cost: ${result.estimated_cost_usd:.6f}")
        
        if result.affected_assets:
            logger.info("Affected Assets:")
            for asset in result.affected_assets:
                logger.info(f"  - {asset.symbol} ({asset.name}): {asset.impact_direction} impact")
        
        return True
        
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        return False
    except Exception as e:
        logger.error(f"Smoke test failed: {e}")
        return False


def main():
    """Run smoke test."""
    logger.info("Starting Gemini API smoke test...")
    
    success = test_gemini_connectivity()
    
    if success:
        logger.info("🎉 All smoke tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Smoke tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
