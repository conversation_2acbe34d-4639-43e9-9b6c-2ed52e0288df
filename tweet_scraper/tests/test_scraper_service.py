"""
Tests for the TweetScraperService class.
"""

import os
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch
import pytest

from tweet_scraper.domain.models import Tweet
from tweet_scraper.services.scraper_service import TweetScraperService


class TestTweetScraperService:
    """Test suite for the TweetScraperService class."""
    
    @pytest.fixture
    def mock_repository(self):
        """Create a mock repository for testing."""
        repository = MagicMock()
        repository.is_tweet_scraped.return_value = False
        return repository
    
    @pytest.fixture
    def service(self, mock_repository):
        """Create a service instance with a mock repository."""
        with patch.dict('os.environ', {'TWEET_SCRAPER_URL': 'https://example.com/tweets'}):
            return TweetScraperService(repository=mock_repository)
    
    def test_init_with_url_parameter(self, mock_repository):
        """Test that the service can be initialized with a URL parameter."""
        url = "https://example.com/tweets"
        service = TweetScraperService(repository=mock_repository, url=url)
        assert service.url == url
    
    def test_init_with_environment_variable(self, mock_repository):
        """Test that the service can be initialized with a URL from an environment variable."""
        url = "https://example.com/tweets"
        with patch.dict('os.environ', {'TWEET_SCRAPER_URL': url}):
            service = TweetScraperService(repository=mock_repository)
            assert service.url == url
    
    def test_init_raises_error_without_url(self, mock_repository):
        """Test that the service raises an error if no URL is provided."""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(ValueError):
                TweetScraperService(repository=mock_repository)
    
    def test_parse_time_ago_minutes(self, service):
        """Test that parse_time_ago correctly parses minute-based time ago strings."""
        now = datetime.now()
        time_ago = "5m"
        result = service.parse_time_ago(time_ago)
        
        # Should be approximately 5 minutes ago
        assert (now - result).total_seconds() // 60 == 5
    
    def test_parse_time_ago_hours(self, service):
        """Test that parse_time_ago correctly parses hour-based time ago strings."""
        now = datetime.now()
        time_ago = "3h"
        result = service.parse_time_ago(time_ago)
        
        # Should be approximately 3 hours ago
        assert (now - result).total_seconds() // 3600 == 3
    
    def test_parse_time_ago_days(self, service):
        """Test that parse_time_ago correctly parses day-based time ago strings."""
        now = datetime.now()
        time_ago = "2d"
        result = service.parse_time_ago(time_ago)
        
        # Should be approximately 2 days ago
        assert (now - result).total_seconds() // 86400 == 2
    
    def test_parse_time_ago_unknown_format(self, service):
        """Test that parse_time_ago returns the current time for unknown formats."""
        now = datetime.now()
        time_ago = "unknown"
        result = service.parse_time_ago(time_ago)
        
        # Should be very close to now
        assert (now - result).total_seconds() < 1
    
    @patch('tweet_scraper.services.scraper_service.webdriver.Chrome')
    def test_setup_driver(self, mock_chrome, service):
        """Test that setup_driver correctly configures a Chrome WebDriver."""
        mock_driver = MagicMock()
        mock_chrome.return_value = mock_driver
        
        driver = service.setup_driver()
        
        assert driver == mock_driver
        mock_driver.execute_cdp_cmd.assert_called_once()
    
    @patch('tweet_scraper.services.scraper_service.webdriver.Chrome')
    @patch('tweet_scraper.services.scraper_service.BeautifulSoup')
    def test_scrape_tweets(self, mock_bs, mock_chrome, service, mock_repository):
        """Test that scrape_tweets correctly scrapes and processes tweets."""
        # Mock the driver and query_with_retries
        mock_driver = MagicMock()
        mock_chrome.return_value = mock_driver
        service.query_with_retries = MagicMock(return_value=True)
        
        # Mock BeautifulSoup and tweet elements
        mock_soup = MagicMock()
        mock_bs.return_value = mock_soup
        
        # Create mock tweet elements
        mock_tweet_element = MagicMock()
        mock_tweet_id = MagicMock()
        mock_tweet_id.get.return_value = "test_tweet_123"
        mock_tweet_element.select_one.side_effect = lambda selector: {
            "[data-id]": mock_tweet_id,
            "[data-markup='true']": MagicMock(get_text=lambda strip: "This is a test tweet"),
            "time": MagicMock(get_text=lambda strip: "5m", get=lambda attr: "2023-05-01 12:00:00")
        }.get(selector)
        
        mock_soup.select.return_value = [mock_tweet_element]
        
        # Run the scrape
        result = service.scrape_tweets()
        
        # Check that the repository methods were called
        assert mock_repository.save_tweet.call_count == 1
        assert mock_repository.add_tweet_to_db.call_count == 1
        
        # Check that the correct number of tweets was scraped
        assert result == 1
    
    @patch('tweet_scraper.services.scraper_service.webdriver.Chrome')
    @patch('tweet_scraper.services.scraper_service.BeautifulSoup')
    def test_scrape_tweets_already_scraped(self, mock_bs, mock_chrome, service, mock_repository):
        """Test that scrape_tweets skips tweets that have already been scraped."""
        # Mock the driver and query_with_retries
        mock_driver = MagicMock()
        mock_chrome.return_value = mock_driver
        service.query_with_retries = MagicMock(return_value=True)
        
        # Mock BeautifulSoup and tweet elements
        mock_soup = MagicMock()
        mock_bs.return_value = mock_soup
        
        # Create mock tweet elements
        mock_tweet_element = MagicMock()
        mock_tweet_id = MagicMock()
        mock_tweet_id.get.return_value = "test_tweet_123"
        mock_tweet_element.select_one.side_effect = lambda selector: {
            "[data-id]": mock_tweet_id,
            "[data-markup='true']": MagicMock(get_text=lambda strip: "This is a test tweet"),
            "time": MagicMock(get_text=lambda strip: "5m", get=lambda attr: "2023-05-01 12:00:00")
        }.get(selector)
        
        mock_soup.select.return_value = [mock_tweet_element]
        
        # Set the repository to indicate the tweet has already been scraped
        mock_repository.is_tweet_scraped.return_value = True
        
        # Run the scrape
        result = service.scrape_tweets()
        
        # Check that the repository methods were not called
        assert mock_repository.save_tweet.call_count == 0
        assert mock_repository.add_tweet_to_db.call_count == 0
        
        # Check that no tweets were scraped
        assert result == 0
