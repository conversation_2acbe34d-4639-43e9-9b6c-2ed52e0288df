"""
Tests for the GeminiClient class.
"""

import json
import pytest
from unittest.mock import Magic<PERSON><PERSON>, patch, Mock
from datetime import datetime

from tweet_scraper.services.gemini_client import Gemini<PERSON>lient
from tweet_scraper.domain.exceptions import ConfigurationError, NetworkError
from tweet_scraper.domain.analysis_models import TweetAnalysisResult, AssetType, ImpactDirection


class TestGeminiClient:
    """Test suite for the GeminiClient class."""
    
    def test_init_without_api_key_raises_error(self):
        """Test that initializing without API key raises ConfigurationError."""
        with pytest.raises(ConfigurationError, match="Gemini API key is required"):
            GeminiClient(api_key="")
    
    @patch('tweet_scraper.services.gemini_client.genai.configure')
    @patch('tweet_scraper.services.gemini_client.genai.GenerativeModel')
    def test_init_with_valid_api_key(self, mock_model_class, mock_configure):
        """Test successful initialization with valid API key."""
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(api_key="test-key", model_name="test-model")
        
        assert client.api_key == "test-key"
        assert client.model_name == "test-model"
        assert client.max_retries == 3
        mock_configure.assert_called_once_with(api_key="test-key")
        mock_model_class.assert_called_once()
    
    def test_create_analysis_prompt(self):
        """Test that analysis prompt is created correctly."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            prompt = client._create_analysis_prompt("Test tweet content")
            
            assert "Test tweet content" in prompt
            assert "financial_impact_percentage" in prompt
            assert "political_impact_percentage" in prompt
            assert "affected_assets" in prompt
            assert "JSON" in prompt
    
    @patch('tweet_scraper.services.gemini_client.genai.configure')
    @patch('tweet_scraper.services.gemini_client.genai.GenerativeModel')
    def test_make_api_call_success(self, mock_model_class, mock_configure):
        """Test successful API call."""
        # Mock response
        mock_response = MagicMock()
        mock_response.text = '{"test": "response"}'
        mock_response.usage_metadata.prompt_token_count = 100
        mock_response.usage_metadata.candidates_token_count = 50
        mock_response.usage_metadata.total_token_count = 150
        
        # Mock model
        mock_model = MagicMock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(api_key="test-key")
        
        response_text, usage_metadata = client._make_api_call("test prompt")
        
        assert response_text == '{"test": "response"}'
        assert usage_metadata["input_tokens"] == 100
        assert usage_metadata["output_tokens"] == 50
        assert usage_metadata["total_tokens"] == 150
        assert "processing_time_ms" in usage_metadata
    
    @patch('tweet_scraper.services.gemini_client.genai.configure')
    @patch('tweet_scraper.services.gemini_client.genai.GenerativeModel')
    def test_make_api_call_empty_response(self, mock_model_class, mock_configure):
        """Test API call with empty response raises NetworkError."""
        # Mock response with empty text
        mock_response = MagicMock()
        mock_response.text = ""
        
        # Mock model
        mock_model = MagicMock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(api_key="test-key")
        
        with pytest.raises(NetworkError, match="Empty response from Gemini API"):
            client._make_api_call("test prompt")
    
    def test_parse_response_valid_json(self):
        """Test parsing valid JSON response."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            response_text = json.dumps({
                "financial_impact_percentage": 25.5,
                "political_impact_percentage": 10.0,
                "affected_assets": [
                    {
                        "symbol": "BTC",
                        "name": "Bitcoin",
                        "asset_type": "crypto",
                        "impact_direction": "positive",
                        "confidence_score": 0.8,
                        "reasoning": "Test reasoning"
                    }
                ],
                "confidence_score": 0.75
            })
            
            parsed_data = client._parse_response(response_text)
            
            assert parsed_data["financial_impact_percentage"] == 25.5
            assert parsed_data["political_impact_percentage"] == 10.0
            assert len(parsed_data["affected_assets"]) == 1
            assert parsed_data["confidence_score"] == 0.75
    
    def test_parse_response_with_markdown(self):
        """Test parsing JSON response wrapped in markdown."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            response_text = """```json
{
    "financial_impact_percentage": 25.5,
    "political_impact_percentage": 10.0,
    "affected_assets": [],
    "confidence_score": 0.75
}
```"""
            
            parsed_data = client._parse_response(response_text)
            
            assert parsed_data["financial_impact_percentage"] == 25.5
            assert parsed_data["political_impact_percentage"] == 10.0
    
    def test_parse_response_invalid_json(self):
        """Test parsing invalid JSON raises ValueError."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            with pytest.raises(ValueError, match="Invalid JSON response"):
                client._parse_response("invalid json")
    
    def test_parse_response_missing_required_fields(self):
        """Test parsing JSON with missing required fields raises ValueError."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            response_text = json.dumps({
                "financial_impact_percentage": 25.5
                # Missing other required fields
            })
            
            with pytest.raises(ValueError, match="Missing required field"):
                client._parse_response(response_text)
    
    def test_estimate_cost(self):
        """Test cost estimation calculation."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            usage_metadata = {
                "input_tokens": 1000,
                "output_tokens": 500
            }
            
            cost = client._estimate_cost(usage_metadata)
            
            # Expected: (1000/1000 * 0.00025) + (500/1000 * 0.0005) = 0.00025 + 0.00025 = 0.0005
            assert cost == 0.0005
    
    def test_should_flag_for_review_low_confidence(self):
        """Test flagging for review based on low confidence."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            parsed_data = {
                "confidence_score": 0.2,
                "financial_impact_percentage": 10,
                "political_impact_percentage": 5,
                "affected_assets": []
            }
            
            should_flag = client._should_flag_for_review(parsed_data, "raw response")
            assert should_flag is True
    
    def test_should_flag_for_review_high_impact(self):
        """Test flagging for review based on high impact."""
        with patch('tweet_scraper.services.gemini_client.genai.configure'), \
             patch('tweet_scraper.services.gemini_client.genai.GenerativeModel'):
            
            client = GeminiClient(api_key="test-key")
            
            parsed_data = {
                "confidence_score": 0.8,
                "financial_impact_percentage": 85,
                "political_impact_percentage": 5,
                "affected_assets": []
            }
            
            should_flag = client._should_flag_for_review(parsed_data, "raw response")
            assert should_flag is True
    
    @patch('tweet_scraper.services.gemini_client.genai.configure')
    @patch('tweet_scraper.services.gemini_client.genai.GenerativeModel')
    def test_analyze_tweet_success(self, mock_model_class, mock_configure):
        """Test successful tweet analysis."""
        # Mock response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "financial_impact_percentage": 25.5,
            "political_impact_percentage": 10.0,
            "affected_assets": [
                {
                    "symbol": "BTC",
                    "name": "Bitcoin",
                    "asset_type": "crypto",
                    "impact_direction": "positive",
                    "confidence_score": 0.8,
                    "reasoning": "Test reasoning"
                }
            ],
            "confidence_score": 0.75
        })
        mock_response.usage_metadata.prompt_token_count = 100
        mock_response.usage_metadata.candidates_token_count = 50
        mock_response.usage_metadata.total_token_count = 150
        
        # Mock model
        mock_model = MagicMock()
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        client = GeminiClient(api_key="test-key")
        
        result = client.analyze_tweet(
            tweet_id="test_123",
            tweet_text="Test tweet content",
            tweet_url="https://twitter.com/test/123"
        )
        
        assert isinstance(result, TweetAnalysisResult)
        assert result.tweet_id == "test_123"
        assert result.tweet_url == "https://twitter.com/test/123"
        assert result.financial_impact_percentage == 25.5
        assert result.political_impact_percentage == 10.0
        assert len(result.affected_assets) == 1
        assert result.affected_assets[0].symbol == "BTC"
        assert result.affected_assets[0].asset_type == AssetType.CRYPTO
        assert result.affected_assets[0].impact_direction == ImpactDirection.POSITIVE
        assert result.confidence_score == 0.75
        assert result.model_version == "gemini-2.0-flash-exp"
