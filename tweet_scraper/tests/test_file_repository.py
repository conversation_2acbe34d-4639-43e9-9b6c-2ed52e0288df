"""
Tests for the FileTweetRepository class.
"""

import json
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import pytest

from tweet_scraper.domain.models import Tweet
from tweet_scraper.repositories.file_repository import FileTweetRepository


class TestFileTweetRepository:
    """Test suite for the FileTweetRepository class."""
    
    @pytest.fixture
    def temp_data_dir(self):
        """Create a temporary directory for test data."""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def repository(self, temp_data_dir):
        """Create a repository instance with a temporary data directory."""
        return FileTweetRepository(data_dir=temp_data_dir)
    
    @pytest.fixture
    def sample_tweet(self):
        """Create a sample tweet for testing."""
        return Tweet(
            id="test_tweet_123",
            text="This is a test tweet",
            time_ago="5m",
            publish_date="2023-05-01 12:00:00",
            scraped_at=datetime.now(),
            approximate_datetime=datetime.now() - timedelta(minutes=5)
        )
    
    def test_init_creates_data_dir_and_db_file(self, temp_data_dir):
        """Test that the repository creates the data directory and database file on initialization."""
        repository = FileTweetRepository(data_dir=temp_data_dir)
        
        assert temp_data_dir.exists()
        assert (temp_data_dir / 'tweet_database.json').exists()
        
        # Check that the database file has the correct structure
        with open(temp_data_dir / 'tweet_database.json', 'r') as f:
            db = json.load(f)
            assert "tweets" in db
            assert isinstance(db["tweets"], list)
    
    def test_get_week_folder(self, repository):
        """Test that get_week_folder returns the correct path and creates the directory."""
        test_date = datetime(2023, 5, 3)  # A Wednesday
        week_folder = repository.get_week_folder(test_date)
        
        # The Monday of this week is 2023-05-01
        expected_folder_name = "week_2023-05-01"
        assert week_folder.name == expected_folder_name
        assert week_folder.exists()
    
    def test_save_tweet(self, repository, sample_tweet):
        """Test that save_tweet correctly saves a tweet to a file."""
        repository.save_tweet(sample_tweet)
        
        # Get the week folder for the tweet
        week_folder = repository.get_week_folder(sample_tweet.approximate_datetime)
        tweet_file = week_folder / f"tweet_{sample_tweet.id}.json"
        
        # Check that the tweet file exists
        assert tweet_file.exists()
        
        # Check that the tweet file contains the correct data
        with open(tweet_file, 'r') as f:
            tweet_data = json.load(f)
            assert tweet_data["id"] == sample_tweet.id
            assert tweet_data["text"] == sample_tweet.text
    
    def test_is_tweet_scraped(self, repository, sample_tweet):
        """Test that is_tweet_scraped correctly identifies scraped tweets."""
        # Initially, the tweet should not be marked as scraped
        assert not repository.is_tweet_scraped(sample_tweet.id)
        
        # Add the tweet to the database
        with open(repository.db_file, 'r') as f:
            db = json.load(f)
        
        db["tweets"].append(sample_tweet.id)
        
        with open(repository.db_file, 'w') as f:
            json.dump(db, f)
        
        # Now the tweet should be marked as scraped
        assert repository.is_tweet_scraped(sample_tweet.id)
    
    def test_add_tweet_to_db(self, repository, sample_tweet):
        """Test that add_tweet_to_db correctly adds a tweet to the database."""
        # Initially, the tweet should not be in the database
        assert not repository.is_tweet_scraped(sample_tweet.id)
        
        # Add the tweet to the database
        repository.add_tweet_to_db(sample_tweet)
        
        # Now the tweet should be in the database
        assert repository.is_tweet_scraped(sample_tweet.id)
    
    def test_get_tweets_for_week(self, repository, sample_tweet):
        """Test that get_tweets_for_week returns the correct tweets for a week."""
        # Save a tweet
        repository.save_tweet(sample_tweet)
        
        # Get tweets for the week
        tweets = repository.get_tweets_for_week(sample_tweet.approximate_datetime)
        
        # Check that the tweet is in the list
        assert len(tweets) == 1
        assert tweets[0].id == sample_tweet.id
        
        # Check that tweets are sorted by approximate_datetime in descending order
        newer_tweet = Tweet(
            id="newer_tweet_456",
            text="This is a newer test tweet",
            time_ago="1m",
            publish_date="2023-05-01 12:04:00",
            scraped_at=datetime.now(),
            approximate_datetime=datetime.now() - timedelta(minutes=1)
        )
        
        repository.save_tweet(newer_tweet)
        
        tweets = repository.get_tweets_for_week(sample_tweet.approximate_datetime)
        
        assert len(tweets) == 2
        assert tweets[0].id == newer_tweet.id  # Newer tweet should be first
