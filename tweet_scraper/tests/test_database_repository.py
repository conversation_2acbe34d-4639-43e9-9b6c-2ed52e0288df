"""
Tests for the DatabaseTweetRepository class.
"""

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

from tweet_scraper.repositories.database_repository import DatabaseTweetRepository
from tweet_scraper.domain.models import Tweet


class TestDatabaseTweetRepository:
    """Test suite for the DatabaseTweetRepository class."""
    
    @pytest.fixture
    def repository(self):
        """Create a repository instance for testing."""
        return DatabaseTweetRepository()
    
    @pytest.fixture
    def sample_tweet(self):
        """Create a sample tweet for testing."""
        return Tweet(
            id="test_123",
            text="This is a test tweet",
            time_ago="5m",
            publish_date="2023-05-01 12:00:00",
            scraped_at=datetime.now(),
            approximate_datetime=datetime.now() - timedelta(minutes=5),
            url="https://twitter.com/test/123",
            analysis_status="pending"
        )
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_save_tweet_new(self, mock_get_db, repository, sample_tweet):
        """Test saving a new tweet."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None  # No existing tweet
        
        repository.save_tweet(sample_tweet)
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_save_tweet_existing(self, mock_get_db, repository, sample_tweet):
        """Test updating an existing tweet."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock existing tweet record
        mock_existing_tweet = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_existing_tweet
        
        repository.save_tweet(sample_tweet)
        
        # Should update existing tweet, not add new one
        mock_db.add.assert_not_called()
        mock_db.commit.assert_called_once()
        
        # Verify fields were updated
        assert mock_existing_tweet.text == sample_tweet.text
        assert mock_existing_tweet.analysis_status == sample_tweet.analysis_status
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_is_tweet_scraped_exists(self, mock_get_db, repository):
        """Test checking if a tweet exists."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock existing tweet
        mock_tweet = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_tweet
        
        result = repository.is_tweet_scraped("test_123")
        
        assert result is True
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_is_tweet_scraped_not_exists(self, mock_get_db, repository):
        """Test checking if a tweet doesn't exist."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = repository.is_tweet_scraped("test_123")
        
        assert result is False
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_get_tweets_for_week(self, mock_get_db, repository):
        """Test getting tweets for a specific week."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock tweet records
        mock_record1 = MagicMock()
        mock_record1.id = "tweet_1"
        mock_record1.text = "Tweet 1"
        mock_record1.time_ago = "1h"
        mock_record1.publish_date = "2023-05-01 11:00:00"
        mock_record1.scraped_at = datetime.now()
        mock_record1.approximate_datetime = datetime.now() - timedelta(hours=1)
        mock_record1.url = "https://twitter.com/test/1"
        mock_record1.analysis_status = "completed"
        mock_record1.analyzed_at = datetime.now()
        
        mock_record2 = MagicMock()
        mock_record2.id = "tweet_2"
        mock_record2.text = "Tweet 2"
        mock_record2.time_ago = "2h"
        mock_record2.publish_date = "2023-05-01 10:00:00"
        mock_record2.scraped_at = datetime.now()
        mock_record2.approximate_datetime = datetime.now() - timedelta(hours=2)
        mock_record2.url = "https://twitter.com/test/2"
        mock_record2.analysis_status = "pending"
        mock_record2.analyzed_at = None
        
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            mock_record1, mock_record2
        ]
        
        week_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tweets = repository.get_tweets_for_week(week_start)
        
        assert len(tweets) == 2
        assert tweets[0].id == "tweet_1"
        assert tweets[1].id == "tweet_2"
        assert all(isinstance(tweet, Tweet) for tweet in tweets)
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_get_tweets_by_status(self, mock_get_db, repository):
        """Test getting tweets by analysis status."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock tweet record
        mock_record = MagicMock()
        mock_record.id = "tweet_1"
        mock_record.text = "Tweet 1"
        mock_record.time_ago = "1h"
        mock_record.publish_date = "2023-05-01 11:00:00"
        mock_record.scraped_at = datetime.now()
        mock_record.approximate_datetime = datetime.now() - timedelta(hours=1)
        mock_record.url = "https://twitter.com/test/1"
        mock_record.analysis_status = "pending"
        mock_record.analyzed_at = None
        
        mock_db.query.return_value.filter.return_value.limit.return_value.order_by.return_value.all.return_value = [mock_record]
        
        tweets = repository.get_tweets_by_status("pending", limit=10)
        
        assert len(tweets) == 1
        assert tweets[0].id == "tweet_1"
        assert tweets[0].analysis_status == "pending"
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_get_pending_analysis_tweets(self, mock_get_db, repository):
        """Test getting tweets pending analysis."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock tweet record
        mock_record = MagicMock()
        mock_record.id = "tweet_1"
        mock_record.text = "Tweet 1"
        mock_record.time_ago = "1h"
        mock_record.publish_date = "2023-05-01 11:00:00"
        mock_record.scraped_at = datetime.now()
        mock_record.approximate_datetime = datetime.now() - timedelta(hours=1)
        mock_record.url = "https://twitter.com/test/1"
        mock_record.analysis_status = "pending"
        mock_record.analyzed_at = None
        
        mock_db.query.return_value.filter.return_value.limit.return_value.order_by.return_value.all.return_value = [mock_record]
        
        tweets = repository.get_pending_analysis_tweets(limit=5)
        
        assert len(tweets) == 1
        assert tweets[0].analysis_status == "pending"
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_update_tweet_analysis_status_success(self, mock_get_db, repository):
        """Test successfully updating tweet analysis status."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock existing tweet
        mock_tweet = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_tweet
        
        analyzed_at = datetime.now()
        result = repository.update_tweet_analysis_status("test_123", "completed", analyzed_at)
        
        assert result is True
        assert mock_tweet.analysis_status == "completed"
        assert mock_tweet.analyzed_at == analyzed_at
        mock_db.commit.assert_called_once()
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_update_tweet_analysis_status_not_found(self, mock_get_db, repository):
        """Test updating status for non-existent tweet."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = repository.update_tweet_analysis_status("test_123", "completed")
        
        assert result is False
        mock_db.commit.assert_not_called()
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_get_tweet_count(self, mock_get_db, repository):
        """Test getting total tweet count."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.count.return_value = 42
        
        count = repository.get_tweet_count()
        
        assert count == 42
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_add_tweet_to_db_calls_save_tweet(self, mock_get_db, repository, sample_tweet):
        """Test that add_tweet_to_db calls save_tweet."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        repository.add_tweet_to_db(sample_tweet)
        
        # Should call save_tweet internally
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
    
    @patch('tweet_scraper.repositories.database_repository.get_db_session_sync')
    def test_database_error_handling(self, mock_get_db, repository, sample_tweet):
        """Test database error handling."""
        # Mock database session that raises an exception
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.side_effect = Exception("Database error")
        
        # Should handle the error gracefully
        result = repository.is_tweet_scraped("test_123")
        assert result is False
        
        count = repository.get_tweet_count()
        assert count == 0
