"""
Tests for the configuration module.
"""

import os
from pathlib import Path
import pytest
from unittest.mock import patch

from tweet_scraper.domain.config import TweetScraperConfig, load_config_from_env
from tweet_scraper.domain.exceptions import ConfigurationError


class TestTweetScraperConfig:
    """Test suite for the TweetScraperConfig class."""
    
    def test_valid_config(self):
        """Test that a valid configuration is accepted."""
        config = TweetScraperConfig(
            url="https://example.com/tweets",
            debug_mode=False,
            data_dir=Path('data/tweets'),
            min_interval=1,
            max_interval=4,
            max_tweets_per_scrape=5,
            max_retries=3,
            retry_min_wait=2,
            retry_max_wait=30,
            retry_multiplier=1
        )
        
        assert config.url == "https://example.com/tweets"
        assert config.debug_mode is False
        assert config.data_dir == Path('data/tweets')
        assert config.min_interval == 1
        assert config.max_interval == 4
        assert config.max_tweets_per_scrape == 5
        assert config.max_retries == 3
        assert config.retry_min_wait == 2
        assert config.retry_max_wait == 30
        assert config.retry_multiplier == 1
    
    def test_min_interval_validation(self):
        """Test that min_interval must be positive."""
        with pytest.raises(ValueError):
            TweetScraperConfig(
                url="https://example.com/tweets",
                min_interval=0
            )
    
    def test_max_interval_validation(self):
        """Test that max_interval must be greater than or equal to min_interval."""
        with pytest.raises(ValueError):
            TweetScraperConfig(
                url="https://example.com/tweets",
                min_interval=2,
                max_interval=1
            )
    
    def test_max_tweets_validation(self):
        """Test that max_tweets_per_scrape must be positive."""
        with pytest.raises(ValueError):
            TweetScraperConfig(
                url="https://example.com/tweets",
                max_tweets_per_scrape=0
            )


class TestLoadConfigFromEnv:
    """Test suite for the load_config_from_env function."""
    
    @patch.dict('os.environ', {
        'TWEET_SCRAPER_URL': 'https://example.com/tweets',
        'TWEET_SCRAPER_DEBUG': 'true',
        'TWEET_SCRAPER_DATA_DIR': '/tmp/tweets',
        'TWEET_SCRAPER_MIN_INTERVAL': '2',
        'TWEET_SCRAPER_MAX_INTERVAL': '5',
        'TWEET_SCRAPER_MAX_TWEETS': '10',
        'TWEET_SCRAPER_MAX_RETRIES': '5',
        'TWEET_SCRAPER_RETRY_MIN_WAIT': '3',
        'TWEET_SCRAPER_RETRY_MAX_WAIT': '60',
        'TWEET_SCRAPER_RETRY_MULTIPLIER': '2'
    })
    def test_load_config_from_env_all_values(self):
        """Test loading configuration with all environment variables set."""
        config = load_config_from_env()
        
        assert config.url == 'https://example.com/tweets'
        assert config.debug_mode is True
        assert config.data_dir == Path('/tmp/tweets')
        assert config.min_interval == 2
        assert config.max_interval == 5
        assert config.max_tweets_per_scrape == 10
        assert config.max_retries == 5
        assert config.retry_min_wait == 3
        assert config.retry_max_wait == 60
        assert config.retry_multiplier == 2
    
    @patch.dict('os.environ', {
        'TWEET_SCRAPER_URL': 'https://example.com/tweets'
    })
    def test_load_config_from_env_minimal(self):
        """Test loading configuration with only required environment variables set."""
        config = load_config_from_env()
        
        assert config.url == 'https://example.com/tweets'
        assert config.debug_mode is False  # Default value
        assert config.data_dir == Path('data/tweets')  # Default value
        assert config.min_interval == 1  # Default value
        assert config.max_interval == 4  # Default value
        assert config.max_tweets_per_scrape == 5  # Default value
        assert config.max_retries == 3  # Default value
        assert config.retry_min_wait == 2  # Default value
        assert config.retry_max_wait == 30  # Default value
        assert config.retry_multiplier == 1  # Default value
    
    @patch.dict('os.environ', {})
    def test_load_config_from_env_missing_url(self):
        """Test that ConfigurationError is raised when TWEET_SCRAPER_URL is not set."""
        with pytest.raises(ConfigurationError):
            load_config_from_env()
    
    @patch.dict('os.environ', {
        'TWEET_SCRAPER_URL': 'https://example.com/tweets',
        'TWEET_SCRAPER_MIN_INTERVAL': 'not_a_number'
    })
    def test_load_config_from_env_invalid_value(self):
        """Test that ConfigurationError is raised when a value is invalid."""
        with pytest.raises(ConfigurationError):
            load_config_from_env()
