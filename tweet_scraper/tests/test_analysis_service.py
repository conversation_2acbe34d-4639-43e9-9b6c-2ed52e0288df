"""
Tests for the TweetAnalysisService class.
"""

import pytest
from unittest.mock import MagicMock, patch, Mock
from datetime import datetime

from tweet_scraper.services.analysis_service import TweetAnalysisService
from tweet_scraper.domain.config import TweetScraperConfig
from tweet_scraper.domain.exceptions import ConfigurationError
from tweet_scraper.domain.analysis_models import TweetAnalysisResult, AssetType, ImpactDirection, AffectedAsset


class TestTweetAnalysisService:
    """Test suite for the TweetAnalysisService class."""
    
    def test_init_analysis_disabled(self):
        """Test initialization with analysis disabled."""
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=False
        )
        
        service = TweetAnalysisService(config)
        
        assert service.config == config
        assert service.gemini_client is None
        assert service.gemini_free_client is None
    
    def test_init_no_api_keys_raises_error(self):
        """Test initialization without API keys raises ConfigurationError."""
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_api_key=None,
            gemini_free_api_key=None
        )
        
        with pytest.raises(ConfigurationError, match="No valid Gemini API keys provided"):
            TweetAnalysisService(config)
    
    @patch('tweet_scraper.services.analysis_service.GeminiClient')
    def test_init_with_production_key(self, mock_gemini_client):
        """Test initialization with production API key."""
        mock_client = MagicMock()
        mock_gemini_client.return_value = mock_client
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_api_key="prod-key",
            gemini_model="gemini-2.0-flash-exp",
            analysis_max_retries=3
        )
        
        service = TweetAnalysisService(config)
        
        assert service.gemini_client == mock_client
        mock_gemini_client.assert_called_with(
            api_key="prod-key",
            model_name="gemini-2.0-flash-exp",
            max_retries=3
        )
    
    @patch('tweet_scraper.services.analysis_service.GeminiClient')
    def test_init_with_free_key_only(self, mock_gemini_client):
        """Test initialization with free API key only."""
        mock_client = MagicMock()
        mock_gemini_client.return_value = mock_client
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_api_key=None,
            gemini_free_api_key="free-key",
            gemini_free_model="gemini-1.5-flash",
            analysis_max_retries=3
        )
        
        service = TweetAnalysisService(config)
        
        assert service.gemini_client is None
        assert service.gemini_free_client == mock_client
        mock_gemini_client.assert_called_with(
            api_key="free-key",
            model_name="gemini-1.5-flash",
            max_retries=3
        )
    
    @patch('tweet_scraper.services.analysis_service.get_db_session_sync')
    def test_queue_tweet_for_analysis_success(self, mock_get_db):
        """Test successfully queuing a tweet for analysis."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None  # No existing request
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key"
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient'):
            service = TweetAnalysisService(config)
            
            result = service.queue_tweet_for_analysis(
                tweet_id="test_123",
                tweet_text="Test tweet content",
                tweet_url="https://twitter.com/test/123",
                priority=1
            )
            
            assert result is True
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
    
    @patch('tweet_scraper.services.analysis_service.get_db_session_sync')
    def test_queue_tweet_already_exists(self, mock_get_db):
        """Test queuing a tweet that already exists."""
        # Mock database session with existing request
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_existing_request = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_existing_request
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key"
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient'):
            service = TweetAnalysisService(config)
            
            result = service.queue_tweet_for_analysis(
                tweet_id="test_123",
                tweet_text="Test tweet content"
            )
            
            assert result is False
            mock_db.add.assert_not_called()
    
    def test_queue_tweet_analysis_disabled(self):
        """Test queuing when analysis is disabled."""
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=False
        )
        
        service = TweetAnalysisService(config)
        
        result = service.queue_tweet_for_analysis(
            tweet_id="test_123",
            tweet_text="Test tweet content"
        )
        
        assert result is False
    
    @patch('tweet_scraper.services.analysis_service.get_db_session_sync')
    def test_process_pending_analyses_no_pending(self, mock_get_db):
        """Test processing when no pending analyses exist."""
        # Mock database session with no pending requests
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = []
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key",
            analysis_batch_size=5
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient'):
            service = TweetAnalysisService(config)
            
            result = service.process_pending_analyses()
            
            assert result == 0
    
    @patch('tweet_scraper.services.analysis_service.get_db_session_sync')
    def test_process_pending_analyses_success(self, mock_get_db):
        """Test successfully processing pending analyses."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock pending request
        mock_request = MagicMock()
        mock_request.tweet_id = "test_123"
        mock_request.tweet_text = "Test tweet content"
        mock_request.tweet_url = "https://twitter.com/test/123"
        mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = [mock_request]
        
        # Mock analysis result
        mock_analysis_result = TweetAnalysisResult(
            tweet_id="test_123",
            tweet_url="https://twitter.com/test/123",
            financial_impact_percentage=25.5,
            political_impact_percentage=10.0,
            affected_assets=[],
            model_version="test-model",
            processing_time_ms=1000,
            token_usage={"input_tokens": 100, "output_tokens": 50},
            estimated_cost_usd=0.001,
            raw_model_output='{"test": "response"}',
            confidence_score=0.75
        )
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key",
            analysis_batch_size=5
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient') as mock_gemini_client:
            mock_client = MagicMock()
            mock_client.analyze_tweet.return_value = mock_analysis_result
            mock_gemini_client.return_value = mock_client
            
            service = TweetAnalysisService(config)
            
            with patch.object(service, '_save_analysis_result') as mock_save:
                result = service.process_pending_analyses()
                
                assert result == 1
                mock_client.analyze_tweet.assert_called_once_with(
                    tweet_id="test_123",
                    tweet_text="Test tweet content",
                    tweet_url="https://twitter.com/test/123"
                )
                mock_save.assert_called_once()
                assert mock_request.status == "completed"
    
    @patch('tweet_scraper.services.analysis_service.get_db_session_sync')
    def test_get_analysis_stats(self, mock_get_db):
        """Test getting analysis statistics."""
        # Mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock query results
        mock_db.query.return_value.filter.return_value.count.return_value = 5  # pending
        mock_db.query.return_value.count.return_value = 100  # completed
        mock_db.query.return_value.with_entities.return_value.scalar.side_effect = [50.0, 0.8]  # cost, confidence
        
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key"
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient'):
            service = TweetAnalysisService(config)
            
            stats = service.get_analysis_stats()
            
            assert stats["pending_analyses"] == 5
            assert stats["completed_analyses"] == 100
            assert stats["total_cost_usd"] == 50.0
            assert stats["average_confidence"] == 0.8
    
    def test_estimate_cost_calculation(self):
        """Test cost estimation in the analysis result."""
        config = TweetScraperConfig(
            url="https://example.com",
            enable_analysis=True,
            gemini_free_api_key="free-key"
        )
        
        with patch('tweet_scraper.services.analysis_service.GeminiClient'):
            service = TweetAnalysisService(config)
            
            # Test the cost estimation logic
            usage_metadata = {
                "input_tokens": 1000,
                "output_tokens": 500,
                "total_tokens": 1500
            }
            
            # This would be called internally by the Gemini client
            # Just verify the structure is correct for our service
            assert "input_tokens" in usage_metadata
            assert "output_tokens" in usage_metadata
