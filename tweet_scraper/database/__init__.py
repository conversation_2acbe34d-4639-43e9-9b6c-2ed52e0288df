"""
Database utilities and setup for tweet analysis.
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import logging

logger = logging.getLogger(__name__)

# Create declarative base
Base = declarative_base()

# Global session factory
SessionLocal = None
engine = None


def init_database(database_url: str) -> None:
    """
    Initialize the database connection and create tables.
    
    Args:
        database_url: Database URL (e.g., sqlite:///path/to/db.sqlite)
    """
    global SessionLocal, engine
    
    # Ensure directory exists for SQLite databases
    if database_url.startswith('sqlite:///'):
        db_path = database_url.replace('sqlite:///', '')
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    # Create engine with appropriate settings
    if database_url.startswith('sqlite:'):
        # SQLite-specific settings
        engine = create_engine(
            database_url,
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
                "timeout": 30
            },
            echo=False
        )
    else:
        # Other databases
        engine = create_engine(database_url, echo=False)
    
    # Create session factory
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Import models to ensure they're registered
    from . import models
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    logger.info(f"Database initialized with URL: {database_url}")


def get_db_session():
    """
    Get a database session.
    
    Returns:
        Database session
    """
    if SessionLocal is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session_sync():
    """
    Get a database session synchronously.
    
    Returns:
        Database session
    """
    if SessionLocal is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    return SessionLocal()


def close_database():
    """Close database connections."""
    global engine
    if engine:
        engine.dispose()
        logger.info("Database connections closed")
