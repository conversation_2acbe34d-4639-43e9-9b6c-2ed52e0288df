#!/usr/bin/env python3
"""
Database migration script for tweet analysis.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tweet_scraper.database import init_database
from tweet_scraper.domain.config import load_config_from_env

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Run database migrations."""
    try:
        # Load configuration
        config = load_config_from_env()
        
        logger.info(f"Initializing database: {config.database_url}")
        
        # Initialize database (creates tables if they don't exist)
        init_database(config.database_url)
        
        logger.info("Database migration completed successfully")
        
    except Exception as e:
        logger.error(f"Database migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
