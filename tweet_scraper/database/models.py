"""
SQLAlchemy database models for tweet analysis.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, Float, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from . import Base


class TweetRecord(Base):
    """
    Database model for storing tweet information.
    """
    __tablename__ = "tweets"
    
    id = Column(String(50), primary_key=True, index=True)
    text = Column(Text, nullable=False)
    time_ago = Column(String(50))
    publish_date = Column(String(100))
    url = Column(String(500))
    scraped_at = Column(DateTime, default=func.now())
    approximate_datetime = Column(DateTime)
    
    # Analysis status
    analysis_status = Column(String(20), default="pending", index=True)
    analyzed_at = Column(DateTime)
    
    # Relationship to analysis results
    analysis_results = relationship("AnalysisResultRecord", back_populates="tweet", cascade="all, delete-orphan")


class AnalysisResultRecord(Base):
    """
    Database model for storing tweet analysis results.
    """
    __tablename__ = "analysis_results"
    
    id = Column(Integer, primary_key=True, index=True)
    tweet_id = Column(String(50), ForeignKey("tweets.id"), nullable=False, index=True)
    tweet_url = Column(String(500))
    
    # Impact percentages
    financial_impact_percentage = Column(Float, nullable=False)
    political_impact_percentage = Column(Float, nullable=False)
    
    # Analysis metadata
    analysis_timestamp = Column(DateTime, default=func.now())
    model_version = Column(String(100), nullable=False)
    processing_time_ms = Column(Integer, nullable=False)
    token_usage = Column(JSON)  # Store as JSON: {"input_tokens": 100, "output_tokens": 50}
    estimated_cost_usd = Column(Float, default=0.0)
    
    # Raw response
    raw_model_output = Column(Text, nullable=False)
    
    # Quality and status
    status = Column(String(20), default="completed", index=True)
    confidence_score = Column(Float, nullable=False)
    needs_manual_review = Column(Boolean, default=False, index=True)
    manual_review_reason = Column(Text)
    
    # Relationships
    tweet = relationship("TweetRecord", back_populates="analysis_results")
    affected_assets = relationship("AffectedAssetRecord", back_populates="analysis_result", cascade="all, delete-orphan")


class AffectedAssetRecord(Base):
    """
    Database model for storing affected assets from analysis.
    """
    __tablename__ = "affected_assets"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_result_id = Column(Integer, ForeignKey("analysis_results.id"), nullable=False, index=True)
    
    # Asset information
    symbol = Column(String(20), nullable=False, index=True)
    name = Column(String(200), nullable=False)
    asset_type = Column(String(10), nullable=False)  # 'crypto' or 'stock'
    
    # Impact assessment
    impact_direction = Column(String(10), nullable=False)  # 'positive', 'negative', 'neutral'
    confidence_score = Column(Float, nullable=False)
    reasoning = Column(Text, nullable=False)
    
    # Relationship
    analysis_result = relationship("AnalysisResultRecord", back_populates="affected_assets")


class AnalysisMetricsRecord(Base):
    """
    Database model for storing analysis metrics.
    """
    __tablename__ = "analysis_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Metrics
    total_tweets_analyzed = Column(Integer, default=0)
    average_processing_time_ms = Column(Float, default=0.0)
    total_cost_usd = Column(Float, default=0.0)
    success_rate = Column(Float, default=0.0)
    manual_review_rate = Column(Float, default=0.0)
    
    # Token usage
    total_input_tokens = Column(Integer, default=0)
    total_output_tokens = Column(Integer, default=0)
    
    # Time period
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.now())


class AnalysisRequestRecord(Base):
    """
    Database model for storing analysis requests (queue).
    """
    __tablename__ = "analysis_requests"
    
    id = Column(Integer, primary_key=True, index=True)
    tweet_id = Column(String(50), nullable=False, index=True)
    tweet_text = Column(Text, nullable=False)
    tweet_url = Column(String(500))
    priority = Column(Integer, default=0, index=True)
    
    # Status tracking
    status = Column(String(20), default="pending", index=True)  # pending, processing, completed, failed
    requested_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Error handling
    retry_count = Column(Integer, default=0)
    last_error = Column(Text)
    
    # Processing metadata
    assigned_worker = Column(String(100))  # For distributed processing if needed
