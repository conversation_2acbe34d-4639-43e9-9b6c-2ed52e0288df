"""
Main entry point for the Tweet Scraper service.
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

from tweet_scraper.domain.config import load_config_from_env
from tweet_scraper.domain.exceptions import ConfigurationError, TweetScraperError
from tweet_scraper.repositories.file_repository import FileTweetRepository
from tweet_scraper.repositories.database_repository import DatabaseTweetRepository
from tweet_scraper.services.scraper_service import TweetScraperService
from tweet_scraper.services.analysis_service import TweetAnalysisService
from tweet_scraper.database import init_database


def setup_logging(data_dir: Path) -> logging.Logger:
    """
    Set up logging for the application.

    Args:
        data_dir: The directory where log files will be stored

    Returns:
        A configured logger instance
    """
    # Create data directory if it doesn't exist
    data_dir.mkdir(parents=True, exist_ok=True)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(data_dir / 'tweet_scraper.log'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger('TweetScraperMain')


def main():
    """
    Main entry point for the Tweet Scraper service.

    Loads configuration, sets up logging, creates the repository and service,
    and runs the service. Handles errors gracefully.
    """
    # Load environment variables
    load_dotenv()

    try:
        # Load configuration
        config = load_config_from_env()

        # Set up data directory
        data_dir = config.data_dir
        data_dir.mkdir(parents=True, exist_ok=True)

        # Set up logging
        logger = setup_logging(data_dir)
        logger.info("Tweet Scraper service starting")

        # Initialize database if analysis is enabled
        if config.enable_analysis:
            logger.info("Initializing database for tweet analysis")
            init_database(config.database_url)
            repository = DatabaseTweetRepository()
        else:
            repository = FileTweetRepository(data_dir=data_dir)

        # Create services
        scraper_service = TweetScraperService(repository=repository, config=config)
        analysis_service = TweetAnalysisService(config=config) if config.enable_analysis else None

        # Run the services
        logger.info("Starting Tweet Scraper service")
        if analysis_service:
            logger.info("Tweet analysis is enabled")
            # Enhanced scraper service that also queues tweets for analysis
            scraper_service.set_analysis_service(analysis_service)

        scraper_service.run_scheduled()

    except ConfigurationError as e:
        # Configuration errors are fatal
        print(f"Configuration error: {str(e)}")
        sys.exit(1)

    except TweetScraperError as e:
        # Log other tweet scraper errors but don't exit
        print(f"Tweet Scraper error: {str(e)}")

    except Exception as e:
        # Log unexpected errors
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
