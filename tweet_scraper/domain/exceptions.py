"""
Exceptions for the Tweet Scraper service.

This module defines custom exception types for the Tweet Scraper service
to provide more specific error handling and better error messages.
"""

class TweetScraperError(Exception):
    """Base exception for all Tweet Scraper errors."""
    pass


class ConfigurationError(TweetScraperError):
    """Raised when there is an error in the configuration."""
    pass


class ScrapingError(TweetScraperError):
    """Raised when there is an error during the scraping process."""
    pass


class NetworkError(ScrapingError):
    """Raised when there is a network-related error during scraping."""
    pass


class ParsingError(ScrapingError):
    """Raised when there is an error parsing the scraped content."""
    pass


class StorageError(TweetScraperError):
    """Raised when there is an error storing or retrieving tweets."""
    pass


class RateLimitError(NetworkError):
    """Raised when the scraper hits a rate limit."""
    pass
