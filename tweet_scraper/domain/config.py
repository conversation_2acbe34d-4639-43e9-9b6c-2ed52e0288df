"""
Configuration for the Tweet Scraper service.

This module defines the configuration model for the Tweet Scraper service
using Pydantic for validation and type checking.
"""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseModel, Field, validator


class TweetScraperConfig(BaseModel):
    """
    Configuration for the Tweet Scraper service.
    
    Attributes:
        url: The URL to scrape tweets from
        debug_mode: Whether to run in debug mode (visible browser)
        data_dir: The directory where tweet data will be stored
        min_interval: Minimum interval between scrapes in minutes
        max_interval: Maximum interval between scrapes in minutes
        max_tweets_per_scrape: Maximum number of tweets to scrape per run
        max_retries: Maximum number of retry attempts for network operations
        retry_min_wait: Minimum wait time between retries in seconds
        retry_max_wait: Maximum wait time between retries in seconds
        retry_multiplier: Multiplier for exponential backoff
    """
    url: str = Field(..., description="The URL to scrape tweets from")
    debug_mode: bool = Field(False, description="Whether to run in debug mode (visible browser)")
    data_dir: Path = Field(Path('data/tweets'), description="The directory where tweet data will be stored")
    min_interval: int = Field(1, description="Minimum interval between scrapes in minutes")
    max_interval: int = Field(4, description="Maximum interval between scrapes in minutes")
    max_tweets_per_scrape: int = Field(5, description="Maximum number of tweets to scrape per run")
    max_retries: int = Field(3, description="Maximum number of retry attempts for network operations")
    retry_min_wait: int = Field(2, description="Minimum wait time between retries in seconds")
    retry_max_wait: int = Field(30, description="Maximum wait time between retries in seconds")
    retry_multiplier: int = Field(1, description="Multiplier for exponential backoff")

    # Gemini AI Configuration
    gemini_api_key: Optional[str] = Field(None, description="Google Gemini API key for production")
    gemini_free_api_key: Optional[str] = Field(None, description="Google Gemini free tier API key for testing")
    gemini_model: str = Field("gemini-2.0-flash-exp", description="Gemini model to use for analysis")
    gemini_free_model: str = Field("gemini-1.5-flash", description="Free Gemini model for testing")
    enable_analysis: bool = Field(False, description="Whether to enable tweet analysis")
    analysis_batch_size: int = Field(10, description="Number of tweets to analyze in each batch")
    analysis_max_retries: int = Field(3, description="Maximum retries for analysis requests")

    # Database Configuration
    database_url: str = Field("sqlite:///data/tweets/tweet_analysis.db", description="Database URL for storing analysis results")
    
    @validator('min_interval')
    def min_interval_must_be_positive(cls, v):
        """Validate that min_interval is positive."""
        if v <= 0:
            raise ValueError('min_interval must be positive')
        return v
    
    @validator('max_interval')
    def max_interval_must_be_greater_than_min(cls, v, values):
        """Validate that max_interval is greater than min_interval."""
        if 'min_interval' in values and v < values['min_interval']:
            raise ValueError('max_interval must be greater than or equal to min_interval')
        return v
    
    @validator('max_tweets_per_scrape')
    def max_tweets_must_be_positive(cls, v):
        """Validate that max_tweets_per_scrape is positive."""
        if v <= 0:
            raise ValueError('max_tweets_per_scrape must be positive')
        return v
    
    class Config:
        """Pydantic model configuration."""
        arbitrary_types_allowed = True


def load_config_from_env() -> TweetScraperConfig:
    """
    Load configuration from environment variables.
    
    Returns:
        A TweetScraperConfig instance with values from environment variables
        
    Raises:
        ConfigurationError: If required environment variables are missing or invalid
    """
    from tweet_scraper.domain.exceptions import ConfigurationError
    
    try:
        # Get required values from environment
        url = os.environ.get('TWEET_SCRAPER_URL')
        if not url:
            raise ConfigurationError("TWEET_SCRAPER_URL environment variable is not set")
        
        # Get optional values from environment with defaults
        debug_mode = os.environ.get('TWEET_SCRAPER_DEBUG', 'false').lower() == 'true'
        data_dir = Path(os.environ.get('TWEET_SCRAPER_DATA_DIR', 'data/tweets'))
        min_interval = int(os.environ.get('TWEET_SCRAPER_MIN_INTERVAL', '1'))
        max_interval = int(os.environ.get('TWEET_SCRAPER_MAX_INTERVAL', '4'))
        max_tweets_per_scrape = int(os.environ.get('TWEET_SCRAPER_MAX_TWEETS', '5'))
        max_retries = int(os.environ.get('TWEET_SCRAPER_MAX_RETRIES', '3'))
        retry_min_wait = int(os.environ.get('TWEET_SCRAPER_RETRY_MIN_WAIT', '2'))
        retry_max_wait = int(os.environ.get('TWEET_SCRAPER_RETRY_MAX_WAIT', '30'))
        retry_multiplier = int(os.environ.get('TWEET_SCRAPER_RETRY_MULTIPLIER', '1'))

        # Gemini AI configuration
        gemini_api_key = os.environ.get('GEMINI_API_KEY')
        gemini_free_api_key = os.environ.get('GEMINI_FREE_API_KEY')
        gemini_model = os.environ.get('GEMINI_MODEL', 'gemini-2.0-flash-exp')
        gemini_free_model = os.environ.get('GEMINI_FREE_MODEL', 'gemini-1.5-flash')
        enable_analysis = os.environ.get('ENABLE_TWEET_ANALYSIS', 'false').lower() == 'true'
        analysis_batch_size = int(os.environ.get('ANALYSIS_BATCH_SIZE', '10'))
        analysis_max_retries = int(os.environ.get('ANALYSIS_MAX_RETRIES', '3'))

        # Database configuration
        database_url = os.environ.get('DATABASE_URL', 'sqlite:///data/tweets/tweet_analysis.db')
        
        # Create and return the config
        return TweetScraperConfig(
            url=url,
            debug_mode=debug_mode,
            data_dir=data_dir,
            min_interval=min_interval,
            max_interval=max_interval,
            max_tweets_per_scrape=max_tweets_per_scrape,
            max_retries=max_retries,
            retry_min_wait=retry_min_wait,
            retry_max_wait=retry_max_wait,
            retry_multiplier=retry_multiplier,
            gemini_api_key=gemini_api_key,
            gemini_free_api_key=gemini_free_api_key,
            gemini_model=gemini_model,
            gemini_free_model=gemini_free_model,
            enable_analysis=enable_analysis,
            analysis_batch_size=analysis_batch_size,
            analysis_max_retries=analysis_max_retries,
            database_url=database_url
        )
    
    except ValueError as e:
        # Handle conversion errors
        raise ConfigurationError(f"Invalid configuration value: {str(e)}")
    except Exception as e:
        # Handle other errors
        raise ConfigurationError(f"Error loading configuration: {str(e)}")
