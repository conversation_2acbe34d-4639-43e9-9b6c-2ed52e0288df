"""
Domain models for tweet analysis results.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator


class AssetType(str, Enum):
    """Types of financial assets."""
    CRYPTO = "crypto"
    STOCK = "stock"


class ImpactDirection(str, Enum):
    """Direction of impact on an asset."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


class AnalysisStatus(str, Enum):
    """Status of tweet analysis."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    MANUAL_REVIEW = "manual_review"


class AffectedAsset(BaseModel):
    """
    Represents an asset potentially affected by a tweet.
    """
    symbol: str = Field(..., description="Asset symbol (e.g., BTC, AAPL)")
    name: str = Field(..., description="Full name of the asset")
    asset_type: AssetType = Field(..., description="Type of asset (crypto or stock)")
    impact_direction: ImpactDirection = Field(..., description="Expected impact direction")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in the impact assessment")
    reasoning: str = Field(..., description="Explanation for the impact assessment")

    class Config:
        """Pydantic model configuration."""
        use_enum_values = True


class TweetAnalysisResult(BaseModel):
    """
    Represents the analysis result for a tweet.
    """
    tweet_id: str = Field(..., description="ID of the analyzed tweet")
    tweet_url: Optional[str] = Field(None, description="URL of the tweet")
    
    # Financial/Political Impact
    financial_impact_percentage: float = Field(..., ge=0.0, le=100.0, description="Percentage impact on finances")
    political_impact_percentage: float = Field(..., ge=0.0, le=100.0, description="Percentage impact on politics")
    
    # Affected Assets
    affected_assets: List[AffectedAsset] = Field(default_factory=list, description="List of potentially affected assets")
    
    # Analysis Metadata
    analysis_timestamp: datetime = Field(default_factory=datetime.now, description="When the analysis was performed")
    model_version: str = Field(..., description="Version of the AI model used")
    processing_time_ms: int = Field(..., description="Time taken to process in milliseconds")
    token_usage: Dict[str, int] = Field(default_factory=dict, description="Token usage statistics")
    estimated_cost_usd: float = Field(default=0.0, description="Estimated cost of the analysis")
    
    # Raw Response
    raw_model_output: str = Field(..., description="Raw output from the AI model")
    
    # Status and Quality
    status: AnalysisStatus = Field(default=AnalysisStatus.COMPLETED, description="Analysis status")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence in the analysis")
    needs_manual_review: bool = Field(default=False, description="Whether this analysis needs manual review")
    manual_review_reason: Optional[str] = Field(None, description="Reason for manual review if needed")
    
    @validator('financial_impact_percentage', 'political_impact_percentage')
    def validate_impact_percentages(cls, v, values):
        """Ensure impact percentages are valid."""
        if v < 0 or v > 100:
            raise ValueError("Impact percentage must be between 0 and 100")
        return v
    
    @validator('affected_assets')
    def validate_affected_assets(cls, v):
        """Ensure we have at most 5 affected assets as per requirements."""
        if len(v) > 5:
            raise ValueError("Maximum of 5 affected assets allowed")
        return v

    class Config:
        """Pydantic model configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class AnalysisMetrics(BaseModel):
    """
    Metrics for monitoring analysis performance.
    """
    total_tweets_analyzed: int = Field(default=0, description="Total number of tweets analyzed")
    average_processing_time_ms: float = Field(default=0.0, description="Average processing time")
    total_cost_usd: float = Field(default=0.0, description="Total cost of analyses")
    success_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="Success rate of analyses")
    manual_review_rate: float = Field(default=0.0, ge=0.0, le=1.0, description="Rate of analyses requiring manual review")
    
    # Token usage statistics
    total_input_tokens: int = Field(default=0, description="Total input tokens used")
    total_output_tokens: int = Field(default=0, description="Total output tokens generated")
    
    # Time period
    period_start: datetime = Field(default_factory=datetime.now, description="Start of the metrics period")
    period_end: datetime = Field(default_factory=datetime.now, description="End of the metrics period")

    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class AnalysisRequest(BaseModel):
    """
    Request for analyzing a tweet.
    """
    tweet_id: str = Field(..., description="ID of the tweet to analyze")
    tweet_text: str = Field(..., description="Content of the tweet")
    tweet_url: Optional[str] = Field(None, description="URL of the tweet")
    priority: int = Field(default=0, description="Priority of the analysis (higher = more urgent)")
    requested_at: datetime = Field(default_factory=datetime.now, description="When the analysis was requested")
    
    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }
