"""
Domain models for the Tweet Scraper service.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class Tweet(BaseModel):
    """
    Represents a tweet with its metadata.
    """
    id: str = Field(..., description="Unique identifier for the tweet")
    text: str = Field(..., description="The content of the tweet")
    time_ago: str = Field(..., description="Human-readable time since the tweet was posted")
    publish_date: str = Field(..., description="The publish date of the tweet")
    scraped_at: datetime = Field(default_factory=datetime.now, description="When the tweet was scraped")
    approximate_datetime: datetime = Field(..., description="Approximate datetime of the tweet")

    # Analysis fields
    url: Optional[str] = Field(None, description="URL of the tweet")
    analysis_status: str = Field(default="pending", description="Status of analysis (pending, processing, completed, failed)")
    analyzed_at: Optional[datetime] = Field(None, description="When the tweet was analyzed")

    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class TweetDatabase(BaseModel):
    """
    Represents the database of scraped tweets.
    """
    tweets: list[str] = Field(default_factory=list, description="List of tweet IDs that have been scraped")
