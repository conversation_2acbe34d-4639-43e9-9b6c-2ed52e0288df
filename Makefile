help:
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m  %-30s\033[0m %s\n", $$1, $$2}'

prepare: ## How to prepare environment using UV
	uv venv finance-X
	@echo "✅ Virtual environment 'finance-X' created!"

install: ## Install dependencies using UV
	uv pip install -r requirements.txt
	uv pip install python-dotenv
	@echo "✅ Dependencies installed using UV!"

bit_bear: install ## Run the BitBear Discord bot
	@export PYTHONPATH=$(pwd) && uv venv && python interfaces/discord/bit_bear_bot.py
	@echo "✅ BitBear Discord bot started!"

run_tweet_scraper: install ## Run the Tweet Scraper service with AI analysis
	@export PYTHONPATH=$(pwd) && uv venv && python -m tweet_scraper.main
	@echo "✅ Tweet Scraper service with AI analysis started!"

migrate_db: install ## Run database migrations
	@export PYTHONPATH=$(pwd) && uv venv && python tweet_scraper/database/migrate.py
	@echo "✅ Database migration completed!"

smoke_test: install ## Run smoke test for Gemini API connectivity
	@export PYTHONPATH=$(pwd) && uv venv && python tweet_scraper/tests/smoke_test.py
	@echo "✅ Smoke test completed!"

test_analysis: install ## Run all analysis-related tests
	@export PYTHONPATH=$(pwd) && uv venv && pytest tweet_scraper/tests/test_gemini_client.py tweet_scraper/tests/test_analysis_service.py tweet_scraper/tests/test_database_repository.py -v
	@echo "✅ Analysis tests completed!"

tweet_scraper_debug: install ## Run the Tweet Scraper service in debug mode with visible browser
	@export PYTHONPATH=$(pwd) && export TWEET_SCRAPER_DEBUG=true && uv venv && python -m tweet_scraper.main
	@echo "✅ Tweet Scraper service started in debug mode!"

test: install ## Run tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest
	@echo "✅ Tests completed!"

test-bit-bear: install ## Run BitBear bot tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest interfaces/discord/tests/
	@echo "✅ BitBear tests completed!"

test-tweet-scraper: install ## Run Tweet Scraper service tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest tweet_scraper/tests/
	@echo "✅ Tweet Scraper tests completed!"
