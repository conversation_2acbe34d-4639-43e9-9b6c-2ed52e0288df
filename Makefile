help:
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m  %-30s\033[0m %s\n", $$1, $$2}'

prepare: ## How to prepare environment using UV
	uv venv finance-X
	@echo "✅ Virtual environment 'finance-X' created!"

install: ## Install dependencies using UV
	uv pip install -r requirements.txt
	uv pip install python-dotenv
	@echo "✅ Dependencies installed using UV!"

bit_bear: install ## Run the BitBear Discord bot
	@export PYTHONPATH=$(pwd) && uv venv && python interfaces/discord/bit_bear_bot.py
	@echo "✅ BitBear Discord bot started!"

run_tweet_scraper: install ## Run the Tweet Scraper service with AI analysis
	@export PYTHONPATH=$(pwd) && uv venv && python -m tweet_scraper.main
	@echo "✅ Tweet Scraper service with AI analysis started!"

migrate_db: install ## Run database migrations
	@export PYTHONPATH=$(pwd) && uv venv && python tweet_scraper/database/migrate.py
	@echo "✅ Database migration completed!"

smoke_test: install ## Run smoke test for Gemini API connectivity
	@export PYTHONPATH=$(pwd) && uv venv && python tweet_scraper/tests/smoke_test.py
	@echo "✅ Smoke test completed!"

test_analysis: install ## Run all analysis-related tests
	@export PYTHONPATH=$(pwd) && uv venv && pytest tweet_scraper/tests/test_gemini_client.py tweet_scraper/tests/test_analysis_service.py tweet_scraper/tests/test_database_repository.py -v
	@echo "✅ Analysis tests completed!"

tweet_scraper_debug: install ## Run the Tweet Scraper service in debug mode with visible browser
	@export PYTHONPATH=$(pwd) && export TWEET_SCRAPER_DEBUG=true && uv venv && python -m tweet_scraper.main
	@echo "✅ Tweet Scraper service started in debug mode!"

test: install ## Run tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest
	@echo "✅ Tests completed!"

test-bit-bear: install ## Run BitBear bot tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest interfaces/discord/tests/
	@echo "✅ BitBear tests completed!"

test-tweet-scraper: install ## Run Tweet Scraper service tests
	@export PYTHONPATH=$(pwd) && uv venv && python -m pytest tweet_scraper/tests/
	@echo "✅ Tweet Scraper tests completed!"

update-env-from-pulumi: ## Update .env file with Pulumi stack outputs
	

pulumi-deploy: ## Deploy infrastructure using Pulumi and update environment variables
	@echo "🚀 Deploying infrastructure with Pulumi..."
	@cd pulumi && pulumi up --yes
	@echo "🔄 Updating environment variables from deployment outputs..."
	@$(MAKE) update-env-from-pulumi
	@echo "✅ Deployment and environment update completed!"

pulumi-status: ## Show current Pulumi stack status and outputs
	@echo "📊 Current Pulumi stack status:"
	@cd pulumi && pulumi stack ls
	@echo "\n📋 Current stack outputs:"
	@cd pulumi && pulumi stack output --json | jq '.' 2>/dev/null || echo "No outputs available"

pulumi-refresh-env: ## Refresh environment variables from existing Pulumi deployment
	@echo "🔄 Updating environment variables from Pulumi stack outputs..."
	@cd pulumi && \
	if [ ! -f "../.env" ]; then \
		echo "📝 Creating .env file from .env.sample..."; \
		cp ../.env.sample ../.env; \
	fi && \
	PULUMI_OUTPUT=$$(pulumi stack output --json 2>/dev/null) && \
	if [ $$? -eq 0 ] && [ "$$PULUMI_OUTPUT" != "{}" ]; then \
		echo "📡 Found Pulumi outputs, updating .env file..."; \
		PUBLIC_IP=$$(echo "$$PULUMI_OUTPUT" | jq -r '.public_ip // empty'); \
		SSH_COMMAND=$$(echo "$$PULUMI_OUTPUT" | jq -r '.ssh_command // empty'); \
		INSTANCE_ID=$$(echo "$$PULUMI_OUTPUT" | jq -r '.instance_id // empty'); \
		PUBLIC_DNS=$$(echo "$$PULUMI_OUTPUT" | jq -r '.public_dns // empty'); \
		HOSTNAME=$$(echo "$$PULUMI_OUTPUT" | jq -r '.hostname // empty'); \
		if [ -n "$$PUBLIC_IP" ]; then \
			echo "🌐 Setting SERVER_PUBLIC_IP=$$PUBLIC_IP"; \
			sed -i.bak '/^SERVER_PUBLIC_IP=/d' ../.env; \
			echo "SERVER_PUBLIC_IP=$$PUBLIC_IP" >> ../.env; \
		fi; \
		if [ -n "$$SSH_COMMAND" ]; then \
			echo "🔑 Setting SSH_COMMAND=$$SSH_COMMAND"; \
			sed -i.bak '/^SSH_COMMAND=/d' ../.env; \
			echo "SSH_COMMAND=$$SSH_COMMAND" >> ../.env; \
		fi; \
		if [ -n "$$INSTANCE_ID" ]; then \
			echo "🏷️  Setting AWS_INSTANCE_ID=$$INSTANCE_ID"; \
			sed -i.bak '/^AWS_INSTANCE_ID=/d' ../.env; \
			echo "AWS_INSTANCE_ID=$$INSTANCE_ID" >> ../.env; \
		fi; \
		if [ -n "$$PUBLIC_DNS" ]; then \
			echo "🌍 Setting SERVER_PUBLIC_DNS=$$PUBLIC_DNS"; \
			sed -i.bak '/^SERVER_PUBLIC_DNS=/d' ../.env; \
			echo "SERVER_PUBLIC_DNS=$$PUBLIC_DNS" >> ../.env; \
		fi; \
		if [ -n "$$HOSTNAME" ]; then \
			echo "🖥️  Setting DEPLOYMENT_HOSTNAME=$$HOSTNAME"; \
			sed -i.bak '/^DEPLOYMENT_HOSTNAME=/d' ../.env; \
			echo "DEPLOYMENT_HOSTNAME=$$HOSTNAME" >> ../.env; \
		fi; \
		rm -f ../.env.bak; \
		echo "✅ Environment variables updated successfully!"; \
	else \
		echo "⚠️  No Pulumi outputs found or Pulumi not configured. Skipping update."; \
	fi
