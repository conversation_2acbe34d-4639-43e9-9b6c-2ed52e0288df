import os
import json
import time
import logging
import asyncio
import random
import discord
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Load environment variables
load_dotenv()

class BitBearBot:
    """
    Discord bot interface that monitors for new tweets and posts them in a Discord channel.
    Reads tweets from the current week's folder, orders them by date in descending order,
    and posts only the most recent tweets that haven't been posted before.
    """

    def __init__(self):
        """
        Initializes the BitBearBot by loading configuration, setting up directories, logging, and the Discord client.

        Raises:
            ValueError: If required environment variables are missing or invalid.
        """
        # Get Discord configuration from environment variables
        self.token = os.environ.get('BIT_BEAR_TOKEN')
        self.channel_id = os.environ.get('BIT_BEAR_NEWS_CHANNEL_ID')

        if not self.token:
            raise ValueError("BIT_BEAR_TOKEN environment variable is not set")
        if not self.channel_id:
            raise ValueError("BIT_BEAR_NEWS_CHANNEL_ID environment variable is not set")

        # Convert channel_id to integer
        try:
            self.channel_id = int(self.channel_id)
        except ValueError:
            raise ValueError("BIT_BEAR_NEWS_CHANNEL_ID must be a valid integer")

        # Set up directories
        self.data_dir = Path('data/tweets')
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.data_dir / 'tweet_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('BitBearBot')

        # Initialize database for tracking posted tweets
        self.db_file = self.data_dir / 'bit_bear_posted.json'
        if not self.db_file.exists():
            with open(self.db_file, 'w') as f:
                json.dump({
                    "posted_tweets": [],
                    "last_posted_datetime": None
                }, f)

        # Set up Discord client with intents
        intents = discord.Intents.default()
        intents.message_content = True
        self.client = discord.Client(intents=intents)

        # Number of tweets to post (can be adjusted)
        self.max_tweets_to_post = 3

        self.logger.info(f"BitBearBot initialized with channel ID: {self.channel_id}")

    def get_current_week_folder(self):
        """
        Returns the path to the current week's tweet data folder, named by the Monday date.

        The folder is located within the service's data directory and is named in the format 'week_YYYY-MM-DD', where the date corresponds to the Monday of the current week.
        """
        today = datetime.now()
        # Get the Monday of the current week
        monday = today - timedelta(days=today.weekday())
        week_folder = f"week_{monday.strftime('%Y-%m-%d')}"

        # Return the full path to the week folder
        return self.data_dir / week_folder

    def is_tweet_posted(self, tweet_id):
        """
        Checks whether a tweet with the given ID has already been posted.

        Args:
            tweet_id: The unique identifier of the tweet to check.

        Returns:
            True if the tweet ID is recorded as posted; otherwise, False.
        """
        with open(self.db_file, 'r') as f:
            db = json.load(f)

        return tweet_id in db["posted_tweets"]

    def mark_tweet_as_posted(self, tweet_id, tweet_datetime):
        """
        Records a tweet as posted and updates the last posted datetime in the database.

        Adds the given tweet ID to the list of posted tweets and sets the last posted datetime
        to the provided value, persisting these changes to the database file.
        """
        with open(self.db_file, 'r') as f:
            db = json.load(f)

        if tweet_id not in db["posted_tweets"]:
            db["posted_tweets"].append(tweet_id)
            db["last_posted_datetime"] = tweet_datetime

            with open(self.db_file, 'w') as f:
                json.dump(db, f, indent=2)

            self.logger.info(f"Updated last posted datetime to {tweet_datetime}")

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=2, max=30),
        retry=retry_if_exception_type((IOError, json.JSONDecodeError)),
        reraise=True
    )
    def get_latest_tweets(self):
        """
        Retrieves and returns the latest tweets from the current week's folder.

        Reads all tweet JSON files from the current week's directory, loads their content, and returns a list of tweet dictionaries sorted by the 'approximate_datetime' field in descending order (newest first). If the week's folder does not exist, returns an empty list.

        Uses exponential backoff retry strategy for handling transient file system errors.
        """
        week_folder = self.get_current_week_folder()
        tweets = []

        # Check if the week folder exists
        if not week_folder.exists():
            self.logger.warning(f"Week folder {week_folder} does not exist")
            return tweets

        # Read all tweet files in the folder
        for tweet_file in week_folder.glob('tweet_*.json'):
            try:
                with open(tweet_file, 'r') as f:
                    tweet = json.load(f)
                tweets.append(tweet)
            except Exception as e:
                self.logger.error(f"Error reading tweet file {tweet_file}: {str(e)}")
                # Re-raise IOError and JSONDecodeError for retry, but let other exceptions propagate
                if isinstance(e, (IOError, json.JSONDecodeError)):
                    raise

        # Sort tweets by approximate_datetime in descending order (newest first)
        tweets.sort(key=lambda x: x.get('approximate_datetime', ''), reverse=True)

        return tweets

    def format_tweet_message(self, tweet):
        """
        Formats a tweet dictionary into a Discord message string.

        Args:
        	tweet: A dictionary containing tweet data, expected to include 'text' and 'publish_date' keys.

        Returns:
        	A formatted string suitable for posting as a Discord message, including the tweet's publish date and text.
        """
        tweet_text = tweet.get('text', 'No text found')
        publish_date = tweet.get('publish_date', 'Unknown date')

        message = f"**New Tweet** - {publish_date}\n\n{tweet_text}\n\n"
        return message

    def get_last_posted_datetime(self):
        """
        Retrieves the datetime of the most recently posted tweet from the database.

        Returns:
            The value of "last_posted_datetime" from the database, or None if not set.
        """
        with open(self.db_file, 'r') as f:
            db = json.load(f)

        return db.get("last_posted_datetime")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=60),
        retry=retry_if_exception_type(discord.errors.HTTPException),
        reraise=True
    )
    async def post_new_tweets(self):
        """
        Posts new tweets from the current week's data folder to the configured Discord channel.

        Retrieves the latest tweets, filters out those already posted or older than the last posted tweet,
        and posts up to the configured maximum number of new tweets to the Discord channel. Marks each
        successfully posted tweet as posted in the local database. Logs the posting process and any errors encountered.

        Uses exponential backoff retry strategy for handling transient Discord API errors.
        """
        self.logger.info("Checking for new tweets to post")

        # Get the latest tweets
        latest_tweets = self.get_latest_tweets()
        self.logger.info(f"Found {len(latest_tweets)} tweets in the current week's folder")

        # Get the last posted datetime
        last_posted_datetime = self.get_last_posted_datetime()
        self.logger.info(f"Last posted datetime: {last_posted_datetime}")

        # Filter out tweets that have already been posted
        unposted_tweets = [tweet for tweet in latest_tweets if not self.is_tweet_posted(tweet.get('id'))]

        # Filter out tweets that are older than or equal to the last posted tweet
        if last_posted_datetime:
            unposted_tweets = [tweet for tweet in unposted_tweets 
                              if tweet.get('approximate_datetime', '') > last_posted_datetime]
            self.logger.info(f"Found {len(unposted_tweets)} unposted tweets newer than the last posted tweet")
        else:
            self.logger.info(f"Found {len(unposted_tweets)} unposted tweets (no previous posts)")

        # Limit to the maximum number of tweets to post
        tweets_to_post = unposted_tweets[:self.max_tweets_to_post]

        if not tweets_to_post:
            self.logger.info("No new tweets to post")
            return

        # Get the Discord channel
        channel = self.client.get_channel(self.channel_id)
        if not channel:
            self.logger.error(f"Could not find Discord channel with ID {self.channel_id}")
            return

        # Post each tweet
        for tweet in tweets_to_post:
            tweet_id = tweet.get('id')
            tweet_datetime = tweet.get('approximate_datetime')
            message = self.format_tweet_message(tweet)

            try:
                await channel.send(message)
                self.logger.info(f"Posted tweet {tweet_id} to Discord")
                self.mark_tweet_as_posted(tweet_id, tweet_datetime)
            except Exception as e:
                self.logger.error(f"Error posting tweet {tweet_id} to Discord: {str(e)}")

    async def run_periodic_check(self):
        """
        Periodically checks for and posts new tweets to Discord at random intervals.

        Waits for the Discord client to be ready, then repeatedly calls the method to post new tweets, sleeping for a random interval between 39 and 180 seconds between checks. Logs errors encountered during posting.
        """
        await self.client.wait_until_ready()
        self.logger.info("Starting periodic check at random intervals (39-180 seconds)")

        while not self.client.is_closed():
            try:
                await self.post_new_tweets()
            except Exception as e:
                self.logger.error(f"Error in periodic check: {str(e)}")

            # Random interval between 39 and 180 seconds (3 minutes)
            interval = random.randint(39, 180)
            self.logger.info(f"Next check in {interval} seconds")
            await asyncio.sleep(interval)

    def run(self):
        """
        Starts the Discord bot and sets up the periodic tweet posting task.

        Initializes the Discord client, registers the event handler for when the bot is ready, and begins the main event loop. If an error occurs during startup, it is logged.
        """
        self.logger.info("Starting BitBear bot")

        @self.client.event
        async def on_ready():
            """
            Handles the Discord client's ready event by logging the connection and starting the periodic tweet posting task.
            """
            self.logger.info(f'Bot connected as {self.client.user}')
            # Start the periodic check with random intervals
            self.client.loop.create_task(self.run_periodic_check())

        try:
            self.client.run(self.token)
        except Exception as e:
            self.logger.error(f"Error running Discord client: {str(e)}")

if __name__ == "__main__":
    bot = BitBearBot()
    bot.run()