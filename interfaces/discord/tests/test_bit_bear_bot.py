import os
import json
import unittest
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path
from datetime import datetime

from interfaces.discord.bit_bear_bot import BitBearBot


class TestBitBearBot(unittest.TestCase):
    """
    Test suite for the BitBearBot class.
    """

    def setUp(self):
        """
        Set up test environment before each test.
        """
        # Mock environment variables
        self.env_patcher = patch.dict('os.environ', {
            'BIT_BEAR_TOKEN': 'test_token',
            'BIT_BEAR_NEWS_CHANNEL_ID': '123456789'
        })
        self.env_patcher.start()
        
        # Mock Path.mkdir to avoid creating directories
        self.mkdir_patcher = patch('pathlib.Path.mkdir')
        self.mock_mkdir = self.mkdir_patcher.start()
        
        # Mock open for file operations
        self.mock_open_patcher = patch('builtins.open', mock_open(read_data='{"posted_tweets": [], "last_posted_datetime": null}'))
        self.mock_open = self.mock_open_patcher.start()
        
        # Create instance of BitBearBot
        self.service = BitBearBot()
        
        # Mock Discord client
        self.service.client = MagicMock()

    def tearDown(self):
        """
        Clean up after each test.
        """
        self.env_patcher.stop()
        self.mkdir_patcher.stop()
        self.mock_open_patcher.stop()

    def test_init(self):
        """
        Test initialization of BitBearBot.
        """
        self.assertEqual(self.service.token, 'test_token')
        self.assertEqual(self.service.channel_id, 123456789)
        self.assertEqual(self.service.max_tweets_to_post, 3)
        self.assertTrue(self.mock_mkdir.called)

    def test_is_tweet_posted(self):
        """
        Test is_tweet_posted method.
        """
        # Mock the database content
        mock_db_content = '{"posted_tweets": ["123", "456"], "last_posted_datetime": "2023-05-10T12:00:00"}'
        self.mock_open.return_value.read.return_value = mock_db_content
        
        # Test with a posted tweet
        self.assertTrue(self.service.is_tweet_posted("123"))
        
        # Test with a non-posted tweet
        self.assertFalse(self.service.is_tweet_posted("789"))

    def test_get_latest_tweets(self):
        """
        Test get_latest_tweets method.
        """
        # Mock Path.exists to return True
        with patch('pathlib.Path.exists', return_value=True):
            # Mock Path.glob to return a list of mock files
            mock_files = [MagicMock(), MagicMock()]
            with patch('pathlib.Path.glob', return_value=mock_files):
                # Mock the content of each tweet file
                tweet1 = {"id": "123", "text": "Test tweet 1", "approximate_datetime": "2023-05-11T12:00:00"}
                tweet2 = {"id": "456", "text": "Test tweet 2", "approximate_datetime": "2023-05-10T12:00:00"}
                
                # Set up the mock_open to return different content for each file
                self.mock_open.return_value.read.side_effect = [
                    json.dumps(tweet1),
                    json.dumps(tweet2)
                ]
                
                # Call the method
                result = self.service.get_latest_tweets()
                
                # Assert the result (tweets should be sorted by approximate_datetime in descending order)
                self.assertEqual(len(result), 2)
                self.assertEqual(result[0]["id"], "123")  # First tweet should be the newest
                self.assertEqual(result[1]["id"], "456")  # Second tweet should be older

    def test_format_tweet_message(self):
        """
        Test format_tweet_message method.
        """
        # Create a test tweet
        tweet = {
            "text": "This is a test tweet",
            "publish_date": "2023-05-11"
        }
        
        # Call the method
        result = self.service.format_tweet_message(tweet)
        
        # Assert the result
        self.assertIn("This is a test tweet", result)
        self.assertIn("2023-05-11", result)
        self.assertIn("New Tweet", result)

    def test_get_last_posted_datetime(self):
        """
        Test get_last_posted_datetime method.
        """
        # Mock the database content
        mock_db_content = '{"posted_tweets": ["123"], "last_posted_datetime": "2023-05-10T12:00:00"}'
        self.mock_open.return_value.read.return_value = mock_db_content
        
        # Call the method
        result = self.service.get_last_posted_datetime()
        
        # Assert the result
        self.assertEqual(result, "2023-05-10T12:00:00")


if __name__ == '__main__':
    unittest.main()