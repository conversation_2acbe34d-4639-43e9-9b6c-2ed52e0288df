import os
import json
import time
import random
import logging
import schedule
import requests
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TweetScraperService:
    """
    Service for scraping tweets from a website.
    Reads the URL from an environment variable, scrapes tweets,
    and stores them in JSON format organized by week.
    """

    def __init__(self):
        """Initialize the TweetScraperService with configuration from environment variables."""
        self.url = os.environ.get('TWEET_SCRAPER_URL')
        if not self.url:
            raise ValueError("TWEET_SCRAPER_URL environment variable is not set")

        # Check if debug mode is enabled
        self.debug_mode = os.environ.get('TWEET_SCRAPER_DEBUG', 'false').lower() == 'true'

        # Create data directory if it doesn't exist
        self.data_dir = Path('data/tweets')
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.data_dir / 'tweet_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('TweetScraperService')

        # Initialize database
        self.db_file = self.data_dir / 'tweet_database.json'
        if not self.db_file.exists():
            with open(self.db_file, 'w') as f:
                json.dump({"tweets": []}, f)

        self.logger.info(f"TweetScraperService initialized with URL: {self.url}")

    def setup_driver(self):
        """Set up and return a configured Chrome WebDriver."""
        chrome_options = Options()

        if not self.debug_mode:
            chrome_options.add_argument("--headless=new")  # Usa nuevo headless mode
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument(
                "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36")
            self.logger.info("Running in headless mode with stealth options")
        else:
            self.logger.info("Running in debug mode with visible browser")

        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")

        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Evitar ser detectado como bot
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                })
            """
        })

        return driver

    def get_week_folder(self):
        """Get the folder name for the current week."""
        today = datetime.now()
        # Get the Monday of the current week
        monday = today - timedelta(days=today.weekday())
        week_folder = f"week_{monday.strftime('%Y-%m-%d')}"

        # Create the week folder if it doesn't exist
        week_path = self.data_dir / week_folder
        week_path.mkdir(exist_ok=True)

        return week_path

    def is_tweet_scraped(self, tweet_id):
        """Check if a tweet has already been scraped."""
        with open(self.db_file, "r") as f:
            db = json.load(f)
        return any(t["id"] == tweet_id for t in db["tweets"])

    def add_tweet_to_db(self, tweet):
        """Add a tweet to the database."""
        with open(self.db_file, 'r') as f:
            db = json.load(f)

        db["tweets"].append(tweet)

        with open(self.db_file, 'w') as f:
            json.dump(db, f, indent=2)

    def parse_time_ago(self, time_ago_text):
        """Parse the 'time ago' text to get an approximate datetime."""
        now = datetime.now()

        if 'm' in time_ago_text and 'min' in time_ago_text:
            minutes = int(time_ago_text.split('m')[0])
            return now - timedelta(minutes=minutes)
        elif 'h' in time_ago_text:
            hours = int(time_ago_text.split('h')[0])
            return now - timedelta(hours=hours)
        elif 'd' in time_ago_text:
            days = int(time_ago_text.split('d')[0])
            return now - timedelta(days=days)
        else:
            # Default to current time if format is unknown
            return now

    def query_with_retries(self, driver, url, max_retries=3):
        """
        Load a URL with retry logic for handling HTTP errors.

        Args:
            driver: Selenium WebDriver instance
            url: URL to load
            max_retries: Maximum number of retry attempts

        Returns:
            True if successful, False otherwise
        """
        retry_count = 0
        backoff_time = 1  # Start with 1 second backoff

        while retry_count < max_retries:
            try:
                self.logger.info(f"Loading URL: {url} (Attempt {retry_count + 1}/{max_retries})")
                driver.get(url)

                # Wait for the page to load
                WebDriverWait(driver, 60).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='status']"))
                )

                # If we get here, the page loaded successfully
                return True

            except TimeoutException as e:
                # Check if we got a specific HTTP error
                error_text = driver.page_source

                # In debug mode, save the page source to a file for inspection
                if self.debug_mode:
                    debug_file = self.data_dir / f"debug_page_source_{int(time.time())}.html"
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(error_text)
                    self.logger.info(f"Saved page source to {debug_file} for debugging")

                if "429" in error_text or "Too Many Requests" in error_text:
                    self.logger.warning(f"Rate limited (429). Retrying in {backoff_time} seconds...")
                elif "500" in error_text or "Server Error" in error_text:
                    self.logger.warning(f"Server error (500). Retrying in {backoff_time} seconds...")
                    if self.debug_mode:
                        self.logger.info("In debug mode, you can see the browser window to inspect the error")
                else:
                    self.logger.warning(f"Timeout error. Retrying in {backoff_time} seconds... Error: {str(e)}")

                # Exponential backoff
                time.sleep(backoff_time)
                backoff_time *= 2  # Double the backoff time for next retry
                retry_count += 1

            except WebDriverException as e:
                self.logger.warning(f"WebDriver error. Retrying in {backoff_time} seconds... Error: {str(e)}")
                time.sleep(backoff_time)
                backoff_time *= 2
                retry_count += 1

            except Exception as e:
                self.logger.error(f"Unexpected error: {str(e)}")
                return False

        self.logger.error(f"Failed to load URL after {max_retries} attempts")
        return False

    def scrape_tweets(self):
        """Scrape tweets from the configured URL."""
        self.logger.info("Starting tweet scraping")

        driver = self.setup_driver()
        try:
            # Load the page with retry logic
            if not self.query_with_retries(driver, self.url):
                self.logger.error("Failed to load the page, aborting scrape")
                return

            # No scrolling - just get the tweets that are initially visible
            # This ensures we only get the most recent tweets
            self.logger.info("Skipping scrolling to focus on most recent tweets only")

            # Get the page source after scrolling
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # Find all tweet elements
            tweet_elements = soup.select("[data-testid='status']")
            self.logger.info(f"Found {len(tweet_elements)} tweets")

            # Limit to the latest 5 tweets
            tweet_elements = tweet_elements[:5]
            self.logger.info(f"Processing only the latest 5 tweets")

            # Reverse the order to process from newest to oldest
            tweet_elements.reverse()
            self.logger.info(f"Reversed order to process from newest to oldest")

            week_folder = self.get_week_folder()
            tweets_scraped = 0

            for tweet_element in tweet_elements:
                try:
                    # Extract tweet ID
                    tweet_id = tweet_element.select_one("[data-id]")
                    if tweet_id:
                        tweet_id = tweet_id.get('data-id')
                    else:
                        # Generate a unique ID if none is found
                        tweet_id = f"tweet_{int(time.time())}_{random.randint(1000, 9999)}"

                    # Skip if already scraped
                    if self.is_tweet_scraped(tweet_id):
                        self.logger.info(f"Tweet {tweet_id} already scraped, skipping")
                        continue

                    # Extract tweet text
                    tweet_text_element = tweet_element.select_one("[data-markup='true']")
                    tweet_text = tweet_text_element.get_text(strip=True) if tweet_text_element else "No text found"

                    # Extract time ago
                    time_element = tweet_element.select_one("time")
                    time_ago = time_element.get_text(strip=True) if time_element else "Unknown"

                    # Extract publish date from title attribute
                    publish_date_str = time_element.get('title') if time_element else None
                    publish_date = publish_date_str if publish_date_str else "Unknown"

                    # Parse the time ago to get an approximate datetime
                    approximate_datetime = self.parse_time_ago(time_ago)

                    # Create tweet object
                    tweet = {
                        "id": tweet_id,
                        "text": tweet_text,
                        "time_ago": time_ago,
                        "publish_date": publish_date,
                        "scraped_at": datetime.now().isoformat(),
                        "approximate_datetime": approximate_datetime.isoformat()
                    }

                    # Save tweet to file
                    tweet_file = week_folder / f"tweet_{tweet_id}.json"
                    with open(tweet_file, 'w') as f:
                        json.dump(tweet, f, indent=2)

                    # Add to database
                    self.add_tweet_to_db(tweet)

                    tweets_scraped += 1
                    self.logger.info(f"Scraped tweet {tweet_id}")

                except Exception as e:
                    self.logger.error(f"Error scraping tweet: {str(e)}")

            self.logger.info(f"Scraped {tweets_scraped} new tweets")

        except Exception as e:
            self.logger.error(f"Error during scraping: {str(e)}")
        finally:
            driver.quit()

    def run_scheduled(self):
        """Run the scraper on a schedule with random intervals."""
        self.logger.info("Starting scheduled scraping")

        def job():
            try:
                self.scrape_tweets()

                # Schedule next run with random interval (1-4 minutes)
                minutes = random.randint(1, 4)
                self.logger.info(f"Next scrape scheduled in {minutes} minutes")

                # Clear existing jobs and schedule a new one
                schedule.clear()
                schedule.every(minutes).minutes.do(job)

            except Exception as e:
                self.logger.error(f"Error in scheduled job: {str(e)}")

        # Initial run
        job()

        # Keep the script running
        while True:
            schedule.run_pending()
            time.sleep(1)

if __name__ == "__main__":
    # Run the scraper
    scraper = TweetScraperService()
    scraper.run_scheduled()
