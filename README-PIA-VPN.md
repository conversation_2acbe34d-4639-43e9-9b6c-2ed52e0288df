# Private Internet Access (PIA) VPN Setup for Finance-X

## Overview

This document provides complete instructions for setting up Private Internet Access (PIA) VPN to resolve Binance API geographic restrictions in your finance-x application.

## Problem Solved

**Error:** `Service unavailable from a restricted location (error code: 0)`

**Solution:** Route all traffic through PIA VPN servers in allowed geographic locations.

## Quick Setup Guide

### 1. Prerequisites

- Active PIA subscription ([Sign up here](https://www.privateinternetaccess.com))
- PIA username and password
- Access to GitHub repository settings (for automatic setup)

### 2. Automatic Setup via GitHub Actions (Recommended)

1. **Add PIA credentials to GitHub Secrets:**
   - Go to: Repository → Settings → Secrets and variables → Actions
   - Add these secrets:
     - `PIA_USERNAME`: Your PIA username
     - `PIA_PASSWORD`: Your PIA password

2. **Commit and push VPN scripts (if first time):**
   ```bash
   git add scripts/
   git commit -m "Add PIA VPN setup scripts"
   git push origin main
   ```

3. **Deploy automatically:**
   ```bash
   git push origin main
   ```

   This triggers **streamlined automated deployment** with:
   - **Simple credential detection**: Checks if PIA credentials are available
   - **Single VPN setup step**: No complex retry mechanisms or fallbacks
   - **Direct installation**: Fresh install or skip if already configured
   - PIA VPN installation and configuration
   - Automatic server selection (Singapore → Japan → UK)
   - Service startup and basic connectivity testing
   - Application restart with VPN connectivity

**The deployment is now simplified and reliable** - clean execution with minimal complexity!

### 3. Manual Setup (Alternative)

If you prefer manual configuration:

```bash
# 1. Add credentials to .env file
echo "PIA_USERNAME=your_username" >> .env
echo "PIA_PASSWORD=your_password" >> .env

# 2. Deploy with Pulumi
cd pulumi
pulumi config set --secret piaUsername "your_username"
pulumi config set --secret piaPassword "your_password"
pulumi up -s ec2

# 3. SSH and test
ssh -i ~/.ssh/pluton.pem ubuntu@YOUR_SERVER_IP
cd /home/<USER>/finance-X
./scripts/test-vpn.sh
```

### 4. Deployment Verification (Optional)

After deployment completes, you can verify everything is working:

1. **SSH to your server:**
   ```bash
   ssh -i ~/.ssh/pluton.pem ubuntu@YOUR_SERVER_IP
   ```

2. **Run comprehensive verification:**
   ```bash
   cd /home/<USER>/finance-X
   chmod +x scripts/verify-deployment.sh
   ./scripts/verify-deployment.sh
   ```

This script will:
- Check system and application status
- Verify VPN configuration and connectivity
- Test Binance API accessibility
- Generate a comprehensive deployment report

**Note:** Manual verification is optional since the deployment process includes automatic testing and validation.

## How It Works

### Architecture
```
Finance-X App → Docker → Ubuntu Server → PIA VPN → Binance API
                              ↓
                        PIA VPN Tunnel (tun0)
                              ↓
                    PIA Server (Singapore/Japan/UK)
                              ↓
                    Binance API (Allowed Location)
```

### Server Selection Priority
1. **Singapore** - Optimal for Binance API
2. **Japan** - Excellent alternative
3. **UK London** - Good backup option
4. **Netherlands** - Fallback option

### Automatic Features
- **Auto-reconnect:** VPN monitor service restarts connection if dropped
- **DNS leak protection:** Uses PIA's secure DNS servers
- **Kill switch:** Prevents traffic leaks if VPN disconnects
- **Health monitoring:** Continuous Binance API connectivity testing
- **Idempotent setup:** Scripts can be run multiple times safely without conflicts

## Files Created

### Scripts
- `scripts/setup-vpn.sh` - Simplified PIA VPN installation script
- `scripts/start-pia-vpn.sh` - Simple VPN service startup script
- `scripts/test-vpn.sh` - Basic connectivity testing
- `scripts/verify-deployment.sh` - Deployment verification and reporting

### Configuration Files
- `/etc/openvpn/client/pia-binance.conf` - PIA OpenVPN configuration
- `/etc/openvpn/client/pia-credentials` - Encrypted PIA credentials
- `/etc/openvpn/client/ca.rsa.2048.crt` - PIA certificate authority
- `/etc/openvpn/client/crl.rsa.2048.pem` - Certificate revocation list

### System Services
- `vpn-binance.service` - Main VPN connection service
- `vpn-monitor.service` - VPN monitoring and auto-reconnect service

## Testing and Verification

### Automatic Testing
After deployment, run the comprehensive test:
```bash
ssh -i ~/.ssh/pluton.pem ubuntu@YOUR_SERVER_IP
cd /home/<USER>/finance-X
./scripts/test-vpn.sh
```

### Manual Verification
```bash
# Check VPN connection
ip route | grep tun0

# Check new IP location
curl https://ipinfo.io

# Test Binance API
curl https://api.binance.com/api/v3/ping

# Check service status
sudo systemctl status vpn-binance
sudo systemctl status vpn-monitor
```



### Expected Results
- ✅ VPN interface (tun0) active
- ✅ IP address changed to PIA server location
- ✅ Binance API returns HTTP 200
- ✅ Both VPN services running

## Troubleshooting

### Common Issues

1. **VPN connects but no internet:**
   ```bash
   sudo systemctl restart systemd-resolved
   sudo systemctl restart vpn-binance
   ```

2. **Binance API still blocked:**
   ```bash
   # Try different PIA server
   sudo nano /etc/openvpn/client/pia-binance.conf
   # Change 'remote singapore.privateinternetaccess.com' to 'remote japan.privateinternetaccess.com'
   sudo systemctl restart vpn-binance
   ```

3. **Docker containers can't access internet:**
   ```bash
   sudo systemctl restart docker
   cd /home/<USER>/finance-X/labs/echo-scripts
   docker-compose restart
   ```

4. **Authentication failed:**
   ```bash
   # Check credentials
   sudo cat /etc/openvpn/client/pia-credentials
   # Update if needed
   sudo nano /etc/openvpn/client/pia-credentials
   sudo systemctl restart vpn-binance
   ```

5. **Script fails on repeated runs:**
   ```bash
   # The scripts are idempotent and should handle repeated runs
   # If you encounter issues, check the logs:
   ./scripts/setup-vpn.sh 2>&1 | tee setup-debug.log

   # Or test idempotency specifically:
   ./scripts/test-idempotency.sh
   ```

6. **Partial installation detected:**
   ```bash
   # The setup script automatically cleans up partial installations
   # If manual cleanup is needed:
   sudo systemctl stop vpn-binance vpn-monitor
   sudo systemctl disable vpn-binance vpn-monitor
   sudo rm -f /etc/systemd/system/vpn-*.service
   sudo rm -rf /etc/openvpn/client/*
   sudo systemctl daemon-reload
   ```

7. **Deployment appears to hang during VPN setup:**
   ```bash
   # The deployment includes retry mechanisms and may take several minutes
   # Check GitHub Actions logs for progress messages like:
   # "VPN setup attempt 1/3", "Repository refreshed", etc.

   # If deployment truly fails, check the logs and retry:
   git push origin main  # Retry deployment
   ```

8. **VPN setup completed but Binance API still blocked:**
   ```bash
   # The deployment automatically tries different PIA servers
   # If all fail, manually try a different server:
   ssh -i ~/.ssh/pluton.pem ubuntu@YOUR_SERVER_IP
   sudo nano /etc/openvpn/client/pia-binance.conf
   # Change server to: japan.privateinternetaccess.com or uk-london.privateinternetaccess.com
   sudo systemctl restart vpn-binance
   ```

9. **Deployment stuck in infinite loop (legacy issue):**
   ```bash
   # This was fixed in the latest version, but if you encounter it:
   # 1. Check if PIA credentials are properly set in GitHub secrets
   # 2. Cancel the deployment and retry
   # 3. Check deployment logs for credential injection confirmation
   ```

10. **GitHub Actions hangs during deployment:**
    ```bash
    # This was fixed by ensuring Docker containers run in detached mode
    # If you still experience hanging, check that all docker-compose commands use -d flag
    # The deployment should complete within 15-20 minutes maximum
    ```

11. **PIA credentials not found despite being set in GitHub secrets:**
    ```bash
    # Check GitHub Actions logs for credential debugging output:
    # Look for: "Debug: PIA Username available in Pulumi: true/false"

    # Verify credentials are properly set in GitHub repository secrets:
    # Repository → Settings → Secrets → Actions
    # Ensure PIA_USERNAME and PIA_PASSWORD are both set

    # Check deployment logs for credential injection confirmation
    ```

12. **Want to verify deployment status:**
    ```bash
    # Run comprehensive verification:
    ssh -i ~/.ssh/pluton.pem ubuntu@YOUR_SERVER_IP
    cd /home/<USER>/finance-X
    ./scripts/verify-deployment.sh
    ```

### Log Analysis
```bash
# VPN service logs
sudo journalctl -u vpn-binance -f

# VPN monitor logs
sudo tail -f /var/log/vpn-monitor.log

# Application logs
cd /home/<USER>/finance-X/labs/echo-scripts
docker-compose logs -f
```

## Monitoring and Maintenance

### Service Status
```bash
# Check all VPN services
sudo systemctl status vpn-binance vpn-monitor

# View real-time logs
sudo journalctl -u vpn-binance -u vpn-monitor -f
```

### Performance Monitoring
```bash
# Check VPN interface statistics
ip -s link show tun0

# Monitor bandwidth usage
sudo iftop -i tun0

# Test connection speed
curl -o /dev/null -s -w "%{time_total}\n" https://api.binance.com/api/v3/ping
```

### Automatic Maintenance
The VPN monitor service automatically:
- Detects connection drops
- Restarts VPN service
- Tests Binance API connectivity
- Logs all activities to `/var/log/vpn-monitor.log`

## Security Features

- **AES-128-CBC encryption** for fast, secure connections
- **SHA1 authentication** for packet integrity
- **DNS leak protection** using PIA's secure DNS
- **IPv6 disabled** to prevent leaks
- **Kill switch** prevents unencrypted traffic
- **Credential encryption** with restricted file permissions

## Cost Information

- **PIA Subscription:** ~$2-10/month depending on plan length
- **AWS EC2 costs:** Existing infrastructure, no additional cost
- **Bandwidth:** Included in PIA subscription (unlimited)

## Support

### PIA Support
- Account issues: [PIA Support](https://www.privateinternetaccess.com/helpdesk)
- Server status: [PIA Server Status](https://www.privateinternetaccess.com/helpdesk/kb/articles/server-status)

### Application Support
- Test connectivity: `./scripts/test-vpn.sh`
- Restart services: `./scripts/start-pia-vpn.sh`
- Check logs: `sudo journalctl -u vpn-binance -f`

## Success Indicators

After successful setup, you should see:
1. ✅ VPN connected with new IP address
2. ✅ Binance API accessible (HTTP 200 responses)
3. ✅ Finance-X application connecting without errors
4. ✅ Both VPN services running and enabled
5. ✅ Automatic reconnection working

Your finance-x application should now successfully connect to Binance API without geographic restrictions!
