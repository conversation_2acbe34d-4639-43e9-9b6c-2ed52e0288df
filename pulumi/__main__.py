"""
Main Pulumi program for deploying the finance-x bitbear service.
Selects the appropriate deployment method based on the stack name.
"""

import pulumi
import os
import sys

# Add the current directory to the path so we can import from infra
sys.path.insert(0, os.path.dirname(os.path.realpath(__file__)))

# Import deployment modules
from infra import deploy_ec2, deploy_ssh

# Get configuration
config = pulumi.Config()
service_name = config.get("service") or "bitbear"
environment = config.get("environment") or "development"

# Get the current stack name
stack = pulumi.get_stack()

# Export common variables
pulumi.export("service", service_name)
pulumi.export("environment", environment)
pulumi.export("stack", stack)

# Select deployment based on stack
if stack == "ec2":
    # Deploy to AWS EC2
    instance = deploy_ec2.deploy_to_ec2(service_name, environment)

    # Export EC2-specific outputs
    pulumi.export("instance_id", instance.id)
    pulumi.export("public_ip", instance.public_ip)
    pulumi.export("public_dns", instance.public_dns)

elif stack == "jet":
    # Deploy to local server via SSH
    result = deploy_ssh.deploy_to_ssh(service_name, environment)

    # Export SSH-specific outputs
    pulumi.export("hostname", "jet")
    pulumi.export("deployment_result", result.stdout)

else:
    # Default case - raise an error for unsupported stack
    raise pulumi.RunError(f"Unsupported stack: {stack}. Use 'ec2' or 'jet'.")
