# Finance-X Pulumi Deployment

This directory contains the Pulumi code for deploying the finance-x bitbear service to either AWS EC2 or a local server via SSH.

 ## Overview

 This template provisions an S3 bucket (`pulumi_aws.s3.BucketV2`) in your AWS account and exports its ID as an output. It’s an ideal starting point when:
  - You want to learn Pulumi with AWS in Python.
  - You need a barebones S3 bucket deployment to build upon.
  - You prefer a minimal template without extra dependencies.

## Prerequisites

- [Pulumi CLI](https://www.pulumi.com/docs/get-started/install/) installed
- Python 3.8+ installed
- AWS credentials configured (for EC2 deployment)
- SSH access to local server (for SSH deployment)

## Setup

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Initialize the Pulumi stack:

```bash
# For development environment
pulumi stack init dev
```

## Configuration

Set the required configuration values:

```bash
# Configure SSH private key for remote access
pulumi config set --secret privateKey "$(cat ~/.ssh/pluton.pem)"

# For EC2 deployment
pulumi stack select ec2
pulumi config set aws:region us-east-1

# For SSH deployment to local server
pulumi stack select jet
```

## Deployment

### Deploy to AWS EC2

```bash
pulumi stack select ec2
pulumi up
```

This will:
1. Create a security group with SSH and HTTP access
2. Launch an EC2 instance with Ubuntu 20.04
3. Install Docker if not already installed
4. Clone the repository and run the bitbear service using docker-compose

### Deploy to Local Server via SSH

```bash
pulumi stack select jet
pulumi up
```

This will:
1. Connect to the local server (hostname: jet) via SSH
2. Clone or update the repository
3. Run the bitbear service using docker-compose

## Cleanup

To destroy the resources created by Pulumi:

```bash
pulumi destroy
```

## Notes

- The SSH private key is stored securely in the Pulumi configuration
- The deployment is idempotent and safe to re-run without errors
- The code is structured to support deploying multiple services to the same host in the future

## Project Structure

```
pulumi/
├── Pulumi.dev.yaml       # Development environment configuration
├── Pulumi.yaml           # Project metadata
├── README.md             # This file
├── __main__.py           # Main entry point with stack selection logic
├── requirements.txt      # Python dependencies
└── infra/                # Infrastructure modules
    ├── __init__.py       # Package initialization
    ├── deploy_ec2.py     # AWS EC2 deployment logic
    └── deploy_ssh.py     # SSH deployment logic
```

## Help and Community

If you have questions or need assistance:
- Pulumi Documentation: https://www.pulumi.com/docs/
- Community Slack: https://slack.pulumi.com/
- GitHub Issues: https://github.com/pulumi/pulumi/issues
