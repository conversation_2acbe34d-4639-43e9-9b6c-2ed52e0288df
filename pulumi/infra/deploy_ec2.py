# pulumi_infra.py

import pulumi
import pulumi_aws as aws
import pulumi_command as command
from pulumi_aws import ec2
import textwrap

def deploy_to_ec2(service_name, environment):
    config = pulumi.Config()
    aws_provider = aws.Provider("aws-provider", region="us-east-1")

    default_vpc = aws.ec2.get_vpc_output(default=True, opts=pulumi.InvokeOptions(provider=aws_provider))
    subnets = aws.ec2.get_subnets_output(filters=[
        aws.ec2.GetSubnetsFilterArgs(name="vpc-id", values=[default_vpc.id])
    ], opts=pulumi.InvokeOptions(provider=aws_provider))

    subnet_id = subnets.ids.apply(lambda ids: ids[0])
    key_pair_name = "pluton"
    ssh_user = config.get("sshUser") or "ubuntu"
    private_key = config.require_secret("privateKey")

    security_group = ec2.SecurityGroup(
        f"{service_name}-sg",
        vpc_id=default_vpc.id,
        description=f"Security group for {service_name} service",
        ingress=[
            ec2.SecurityGroupIngressArgs(protocol="tcp", from_port=22, to_port=22, cidr_blocks=["0.0.0.0/0"]),
            ec2.SecurityGroupIngressArgs(protocol="tcp", from_port=80, to_port=80, cidr_blocks=["0.0.0.0/0"]),
            ec2.SecurityGroupIngressArgs(protocol="tcp", from_port=443, to_port=443, cidr_blocks=["0.0.0.0/0"]),
        ],
        egress=[
            ec2.SecurityGroupEgressArgs(protocol="-1", from_port=0, to_port=0, cidr_blocks=["0.0.0.0/0"]),
        ],
        tags={"Name": f"{service_name}-sg", "Environment": environment, "Service": service_name},
        opts=pulumi.ResourceOptions(provider=aws_provider),
    )

    ami = aws.ec2.get_ami_output(
        most_recent=True,
        owners=["099720109477"],
        filters=[
            aws.ec2.GetAmiFilterArgs(name="name", values=["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]),
            aws.ec2.GetAmiFilterArgs(name="virtualization-type", values=["hvm"]),
        ],
        opts=pulumi.InvokeOptions(provider=aws_provider),
    )

    instance = ec2.Instance(
        f"{service_name}-instance",
        instance_type="t2.micro",
        subnet_id=subnet_id,
        ami=ami.id,
        key_name=key_pair_name,
        vpc_security_group_ids=[security_group.id],
        root_block_device=ec2.InstanceRootBlockDeviceArgs(
            volume_size=50,
            volume_type="gp2",
            delete_on_termination=True,
        ),
        tags={"Name": f"{service_name}-instance", "Environment": environment, "Service": service_name},
        opts=pulumi.ResourceOptions(provider=aws_provider),
    )

    connection = command.remote.ConnectionArgs(
        host=instance.public_ip,
        user=ssh_user,
        private_key=private_key,
    )

    wait_for_ssh = command.local.Command(
        f"{service_name}-wait-ssh",
        create=pulumi.Output.concat(
            "timeout 300 bash -c 'until nc -z ",
            instance.public_ip,
            " 22; do sleep 5; done'"
        ),
        opts=pulumi.ResourceOptions(depends_on=[instance])
    )

    test_ssh = command.remote.Command(
        f"{service_name}-test-ssh",
        connection=connection,
        create="echo 'Pulumi SSH test OK' > /home/<USER>/test.txt",
        opts=pulumi.ResourceOptions(depends_on=[wait_for_ssh]),
    )

    git_token = config.require_secret("githubToken")
    branch_name = config.require("branchName")

    clone_repo = command.remote.Command(
        f"{service_name}-clone-repo",
        connection=connection,
        create=pulumi.Output.concat(
            "cd /home/<USER>",
            "if [ ! -d 'finance-X' ]; then ",
            "git clone -b ", branch_name, " https://", git_token, "@github.com/Echo-Effects/finance-X.git; ",
            "else ",
            "cd finance-X && ",
            "git reset --hard && git clean -fd && git fetch origin && git checkout ", branch_name, " && git reset --hard origin/", branch_name, "; ",
            "fi"
        ),
        opts=pulumi.ResourceOptions(depends_on=[test_ssh])
    )

    htpasswd = config.require_secret("htpasswdContent")
    write_htpasswd = command.remote.Command(
        f"{service_name}-write-htpasswd",
        connection=connection,
        create=pulumi.Output.concat(
            "echo '", htpasswd, "' > /home/<USER>/finance-X/labs/echo-scripts/htpasswd && ",
            "chmod 644 /home/<USER>/finance-X/labs/echo-scripts/htpasswd"
        ),
        opts=pulumi.ResourceOptions(depends_on=[clone_repo])
    )

    env_file = config.require_secret("envFile")
    pia_username = config.get_secret("piaUsername")
    pia_password = config.get_secret("piaPassword")

    write_env_file = command.remote.Command(
        f"{service_name}-write-env",
        connection=connection,
        create=pulumi.Output.concat(
            "echo '", env_file, "' > /home/<USER>/finance-X/.env && ",
            "chmod 600 /home/<USER>/finance-X/.env && ",
            "if [ ! -z '", pia_username or "", "' ] && [ ! -z '", pia_password or "", "' ]; then ",
            "echo 'PIA_USERNAME=", pia_username or "", "' >> /home/<USER>/finance-X/.env && ",
            "echo 'PIA_PASSWORD=", pia_password or "", "' >> /home/<USER>/finance-X/.env; ",
            "fi && ",
            "cp /home/<USER>/finance-X/.env /home/<USER>/finance-X/labs/echo-scripts/.env"
        ),
        opts=pulumi.ResourceOptions(depends_on=[clone_repo])
    )

    install_docker = command.remote.Command(
        f"{service_name}-install-docker",
        connection=connection,
        create=pulumi.Output.concat(
            "set -e && ",
            "if ! command -v docker &> /dev/null; then ",
            "sudo apt-get update && ",
            "sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common && ",
            "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add - && ",
            "sudo add-apt-repository \"deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" && ",
            "sudo apt-get update && ",
            "sudo apt-get install -y docker-ce docker-ce-cli containerd.io && ",
            "sudo usermod -aG docker ubuntu; ",
            "fi && docker --version"
        ),
        opts=pulumi.ResourceOptions(depends_on=[write_env_file])
    )

    # VPN setup is now handled inside the echo-scripts container
    # No need to set up VPN on the host machine

    deploy_service = command.remote.Command(
        f"{service_name}-deploy",
        connection=connection,
        create=pulumi.Output.concat(
            "cd /home/<USER>/finance-X && ",
            "docker compose up -d --build --force-recreate && ",
            "cd labs/echo-scripts && ",
            "docker compose -f docker_compose.yaml up -d --build --force-recreate && ",
            "docker compose -f docker_compose.yaml ps"
        ),
        opts=pulumi.ResourceOptions(depends_on=[install_docker, write_htpasswd])
    )

    pulumi.export("public_ip", instance.public_ip)
    pulumi.export("ssh_command", pulumi.Output.concat("ssh -i ~/.ssh/pluton.pem ", ssh_user, "@", instance.public_ip))
    pulumi.export("deployment_result", deploy_service.stdout)
    # VPN setup is now handled inside the container
    pulumi.export("env_written", write_env_file.stdout)
    pulumi.export("htpasswd_written", write_htpasswd.stdout)

    return instance
