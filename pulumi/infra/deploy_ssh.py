"""
Module for deploying the finance-x bitbear service to a local server via SSH.
"""

import pulumi
import pulumi_command as command

def deploy_to_ssh(service_name, environment):
    """
    Deploy the finance-x bitbear service to a local server via SSH.
    
    Args:
        service_name: The name of the service to deploy.
        environment: The environment to deploy to.
        
    Returns:
        The result of the deployment command.
    """
    # Get configuration
    config = pulumi.Config()
    
    # Get the private key for SSH access
    private_key = config.get_secret("privateKey")
    # Validate an SSH private key format before use
    _private_key = private_key.apply(
        lambda key: key if key.startswith("-----BEGIN") else pulumi.RunError("Invalid SSH private key format")
    )
    
    # Define the connection for remote execution
    connection = command.remote.ConnectionArgs(
        host="jet",  # Hostname of the local server
        user="ubuntu",  # Assuming the user is ubuntu, adjust if needed
        private_key=_private_key,
    )
    
    # Deploy the service
    deploy_service = command.remote.Command(
        f"{service_name}-deploy-ssh",
        connection=connection,
         create = pulumi.Output.concat(
            "set -e && ",  # Exit on any error
            "cd /home/<USER>",
            "if [ ! -d \"finance-X\" ]; then ",
            "git clone https://github.com/Echo-Effects/finance-X.git || exit 1; ",
            "else ",
            "cd finance-X && git pull || exit 1; ",
            "fi && ",
            "cd finance-X && ",
            "docker-compose up -d || exit 1"
        ),
    )

    # Export the deployment result
    pulumi.export("ssh_deployment_result", deploy_service.stdout)
    
    return deploy_service