# Tweet Scraper Service

## Overview

The Tweet Scraper Service is a standalone service responsible for scraping tweets from a configured URL and storing them in a structured format. It is designed to be run as a background service that periodically checks for new tweets and stores them for later use by other services, such as the BitBear Discord bot.

## Architecture

The Tweet Scraper Service follows SOLID principles and Clean Architecture:

1. **Domain Layer**: Contains the core business logic and entities
   - `Tweet` model: Represents a tweet with its metadata
   - `TweetDatabase` model: Represents the database of scraped tweets

2. **Repository Layer**: Handles data storage and retrieval
   - `TweetRepository` interface: Defines the contract for tweet repositories
   - `FileTweetRepository` implementation: Stores tweets in JSON files organized by week

3. **Service Layer**: Contains the application logic
   - `TweetScraperService`: Handles the scraping of tweets and interaction with the repository

4. **Main Application**: Entry point for the service
   - `main.py`: Sets up the service and runs it

## Data Storage

Tweets are stored in a structured format:

1. Each tweet is saved as a JSON file in a week-specific folder
2. Week folders are named in the format `week_YYYY-MM-DD`, where the date corresponds to the Monday of the week
3. A central database file (`tweet_database.json`) keeps track of all scraped tweet IDs to avoid duplicates

Example directory structure:
```
data/
└── tweets/
    ├── tweet_database.json
    ├── tweet_scraper.log
    └── week_2023-05-01/
        ├── tweet_123456789.json
        ├── tweet_987654321.json
        └── ...
```

## Configuration

The service can be configured using environment variables:

- `TWEET_SCRAPER_URL`: The URL to scrape tweets from (required)
- `TWEET_SCRAPER_DEBUG`: Set to "true" to run in debug mode with a visible browser (default: "false")

These can be set in a `.env` file in the project root.

## Running the Service

To run the Tweet Scraper Service:

1. Configure the required environment variables in `.env` file
2. Run the service using the Makefile command:
   ```
   make tweet_scraper
   ```

To run in debug mode with a visible browser:
```
make tweet_scraper_debug
```

## Testing

The service includes comprehensive unit tests for all layers:

- Repository tests: Test the storage and retrieval of tweets
- Service tests: Test the scraping and processing of tweets

To run the tests:
```
make test-tweet-scraper
```

## Integration with Other Services

The Tweet Scraper Service is designed to work seamlessly with other services in the Finance-X platform:

- **BitBear Discord Bot**: Reads tweets from the structured data directory created by the Tweet Scraper Service, orders them by date, and posts them to a Discord channel.

## Error Handling and Resilience

The service includes several features to ensure reliable operation:

1. **Retry Mechanisms**: Uses exponential backoff to retry operations that fail due to transient errors
2. **Logging**: Comprehensive logging of all operations for debugging and monitoring
3. **Exception Handling**: Proper handling of exceptions to prevent service crashes
4. **Idempotent Operations**: Ensures that the same tweet is not scraped multiple times

## Future Enhancements

Potential future enhancements for the Tweet Scraper Service:

1. **Multiple URL Support**: Add support for scraping tweets from multiple URLs
2. **Advanced Filtering**: Implement more sophisticated filtering of tweets based on content
3. **API Integration**: Add support for using Twitter API instead of web scraping
4. **Metrics and Monitoring**: Add metrics collection for monitoring service health
5. **Containerization**: Package the service as a Docker container for easier deployment
