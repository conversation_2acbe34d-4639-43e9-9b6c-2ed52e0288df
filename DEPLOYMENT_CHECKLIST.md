# Tweet Analysis Deployment Checklist

## Pre-Deployment

### Environment Setup
- [ ] Copy `.env.sample` to `.env` and configure all required variables
- [ ] Set `GEMINI_API_KEY` for production workloads
- [ ] Set `GEMINI_FREE_API_KEY` for testing and development
- [ ] Configure `TWEET_SCRAPER_URL` with the target tweet source
- [ ] Set `ENABLE_TWEET_ANALYSIS=true` to enable analysis features
- [ ] Configure `DATABASE_URL` for your preferred database (SQLite for local, PostgreSQL for production)

### Dependencies
- [ ] Install dependencies: `make install`
- [ ] Verify Python 3.8+ is installed
- [ ] Ensure required system packages are available (Chrome/Chromium for scraping)

### Database Setup
- [ ] Run database migrations: `make migrate_db`
- [ ] Verify database tables are created successfully
- [ ] Test database connectivity

## Testing

### Smoke Tests
- [ ] Run Gemini API connectivity test: `make smoke_test`
- [ ] Verify API keys are working correctly
- [ ] Test both production and free tier models if both keys are configured

### Unit Tests
- [ ] Run analysis service tests: `make test_analysis`
- [ ] Verify all tests pass with green status
- [ ] Check test coverage for critical components

### Integration Tests
- [ ] Test tweet scraping with analysis disabled: Set `ENABLE_TWEET_ANALYSIS=false`
- [ ] Test tweet scraping with analysis enabled: Set `ENABLE_TWEET_ANALYSIS=true`
- [ ] Verify tweets are queued for analysis correctly
- [ ] Test batch processing of pending analyses

## Deployment

### Local Deployment
- [ ] Start the service: `make run_tweet_scraper`
- [ ] Monitor logs for successful initialization
- [ ] Verify tweet scraping is working
- [ ] Verify analysis processing is working (if enabled)

### Production Deployment
- [ ] Deploy to production environment
- [ ] Configure production database (PostgreSQL recommended)
- [ ] Set up monitoring and alerting
- [ ] Configure log aggregation
- [ ] Set up backup procedures for analysis data

## Post-Deployment Verification

### Functional Verification
- [ ] Verify tweets are being scraped successfully
- [ ] Check that tweets are queued for analysis
- [ ] Confirm analyses are being processed in batches
- [ ] Validate analysis results are stored in database
- [ ] Test manual review flagging for high-impact tweets

### Performance Verification
- [ ] Monitor processing latency (target: < 10 seconds per tweet)
- [ ] Check token usage and cost metrics
- [ ] Verify batch processing efficiency
- [ ] Monitor memory and CPU usage

### Data Quality Verification
- [ ] Review sample analysis results for accuracy
- [ ] Check confidence scores are reasonable (> 0.3 for most tweets)
- [ ] Verify affected assets are relevant to tweet content
- [ ] Confirm impact percentages are within expected ranges

## Monitoring and Metrics

### Prometheus Metrics
- [ ] `tweet_analysis_requests_total` - Monitor success/failure rates
- [ ] `tweet_analysis_duration_seconds` - Track processing performance
- [ ] `tweet_analysis_cost_usd_total` - Monitor API costs
- [ ] `tweet_analysis_pending_total` - Watch for queue buildup
- [ ] `tweet_analysis_manual_review_total` - Track review queue

### Key Performance Indicators (KPIs)
- [ ] **Success Rate**: > 95% of analyses complete successfully
- [ ] **Processing Time**: < 10 seconds average per tweet
- [ ] **Cost Efficiency**: < $0.01 per tweet analysis
- [ ] **Queue Health**: < 100 pending analyses during normal operation
- [ ] **Manual Review Rate**: < 10% of analyses require manual review

### Alerting Thresholds
- [ ] Error rate > 5% in 5-minute window
- [ ] Average processing time > 30 seconds
- [ ] Pending queue > 500 tweets
- [ ] Daily cost > $50 (adjust based on volume)
- [ ] Manual review queue > 100 items

## Dashboard Links

### Monitoring Dashboards
- [ ] Grafana dashboard for tweet analysis metrics
- [ ] Database monitoring dashboard
- [ ] Application performance monitoring (APM)
- [ ] Cost tracking dashboard

### Operational Dashboards
- [ ] Tweet processing pipeline status
- [ ] Analysis queue management
- [ ] Manual review queue
- [ ] Error tracking and alerting

## Rollback Plan

### Rollback Triggers
- [ ] Error rate > 20% for 10 minutes
- [ ] Complete service failure
- [ ] Database corruption or data loss
- [ ] Excessive API costs (> 10x normal)

### Rollback Procedure
- [ ] Disable analysis: Set `ENABLE_TWEET_ANALYSIS=false`
- [ ] Revert to previous version if necessary
- [ ] Clear pending analysis queue if corrupted
- [ ] Restore database from backup if needed
- [ ] Notify stakeholders of rollback

## Security Checklist

### API Key Security
- [ ] API keys are stored securely (environment variables, not in code)
- [ ] API keys are not logged in application logs
- [ ] Production and development keys are separate
- [ ] Key rotation procedure is documented

### Data Security
- [ ] Database access is properly secured
- [ ] Tweet data is handled according to privacy policies
- [ ] Analysis results are stored securely
- [ ] Backup data is encrypted

## Documentation

### Updated Documentation
- [ ] README.md includes setup instructions
- [ ] API documentation is current
- [ ] Configuration options are documented
- [ ] Troubleshooting guide is available

### Operational Documentation
- [ ] Runbook for common issues
- [ ] Escalation procedures
- [ ] Maintenance procedures
- [ ] Disaster recovery plan

## Sign-off

- [ ] **Development Team**: Code review completed, tests passing
- [ ] **QA Team**: Integration testing completed successfully
- [ ] **DevOps Team**: Infrastructure and monitoring configured
- [ ] **Product Team**: Feature functionality verified
- [ ] **Security Team**: Security review completed

**Deployment Date**: _______________
**Deployed By**: _______________
**Approved By**: _______________

## 24-Hour Monitoring

### First 24 Hours Post-Deployment
- [ ] Hour 1: Verify basic functionality
- [ ] Hour 4: Check performance metrics
- [ ] Hour 8: Review error logs and alerts
- [ ] Hour 12: Validate cost tracking
- [ ] Hour 24: Complete health check and sign-off

**24-Hour Sign-off**: _______________
**Date**: _______________
