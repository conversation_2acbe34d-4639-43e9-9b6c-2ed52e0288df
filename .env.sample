# Private Internet Access VPN Configuration
# Required for Binance API access from restricted locations
PIA_USERNAME=your_pia_username
PIA_PASSWORD=your_pia_password

# Tweet Scraper Service
TWEET_SCRAPER_URL=
TWEET_SCRAPER_DEBUG=false
TWEET_SCRAPER_DATA_DIR=data/tweets
TWEET_SCRAPER_MIN_INTERVAL=1
TWEET_SCRAPER_MAX_INTERVAL=4
TWEET_SCRAPER_MAX_TWEETS=5
TWEET_SCRAPER_MAX_RETRIES=3
TWEET_SCRAPER_RETRY_MIN_WAIT=2
TWEET_SCRAPER_RETRY_MAX_WAIT=30
TWEET_SCRAPER_RETRY_MULTIPLIER=1


# Tweet Analysis with Google Gemini
ENABLE_TWEET_ANALYSIS=true

# Google Gemini API Keys
# Production API key for Gemini 2.5 Pro
GEMINI_API_KEY=your-production-gemini-api-key-here

# Free tier API key for testing and development
GEMINI_FREE_API_KEY=your-free-gemini-api-key-here

# Gemini Model Configuration
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_FREE_MODEL=gemini-1.5-flash

# Analysis Processing Configuration
ANALYSIS_BATCH_SIZE=10
ANALYSIS_MAX_RETRIES=3

# Database Configuration
DATABASE_URL=sqlite:///data/tweets/tweet_analysis.db

# BitBear Service
BIT_BEAR_TOKEN=
BIT_BEAR_NEWS_CHANNEL_ID=
