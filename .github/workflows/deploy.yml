name: Deploy with <PERSON>ulumi to EC2

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - chore-deploy/**
      - feature-deploy/**
      - fix-deploy/**

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.G_PAT }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install uv and project dependencies
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          ~/.local/bin/uv pip install --system --no-cache-dir -r pulumi/requirements.txt

      - name: Set up Pulumi CLI
        uses: pulumi/actions@v5

      - name: Pulumi login
        run: pulumi login --cloud-url https://api.pulumi.com
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Set up AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure Pulumi stack
        working-directory: pulumi
        run: |
          echo "$PRIVATE_KEY" > ./private.pem
          pulumi stack select ec2
          pulumi config set --secret privateKey < ./private.pem
          pulumi config set --secret githubToken "$GITHUB_TOKEN"
          pulumi config set sshUser ubuntu
          pulumi config set branchName "${GITHUB_REF#refs/heads/}"
          # Set PIA credentials if provided
          if [ ! -z "$PIA_USERNAME" ] && [ ! -z "$PIA_PASSWORD" ]; then
            pulumi config set --secret piaUsername "$PIA_USERNAME"
            pulumi config set --secret piaPassword "$PIA_PASSWORD"
            echo "PIA credentials configured"
          else
            echo "PIA credentials not provided - VPN setup will require manual configuration"
          fi
        env:
          PRIVATE_KEY: ${{ secrets.EC2_SSH_PRIVATE_KEY }}
          GITHUB_TOKEN: ${{ secrets.G_PAT }}
          PIA_USERNAME: ${{ secrets.PIA_USERNAME }}
          PIA_PASSWORD: ${{ secrets.PIA_PASSWORD }}

      - name: Deploy using Pulumi
        working-directory: pulumi
        run: pulumi up --yes -s ec2
