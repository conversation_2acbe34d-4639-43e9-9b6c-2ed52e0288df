# Finance-X

## Description
Finance-X is an AI-powered financial platform that offers advanced capabilities, including:

- Algorithmic trading with AI algorithms.
- Analysis of financial instruments and commodities.
- Real-time investment opportunity discovery.
- **AI-powered tweet analysis** using Google Gemini 2.5 Pro for financial and political impact assessment.

## Objective
The primary goal of this project is to build a robust algorithmic trading platform using AI algorithms to enhance decision-making in financial investments.

## Project Phases
- **Phase 1: Development of Algorithmic Trading Capabilities:** Implementation of AI-driven trading techniques.
- **Phase 2: Analysis of Financial Instruments and Commodities:** In-depth examination of global supply and demand.
- **Phase 3: Investment Opportunity Discovery:** Utilization of advanced algorithms to identify real-time opportunities.
- **Phase 4: Integration and Continuous Optimization:** Ongoing improvement of algorithm efficiency and accuracy.

## Components

### Tweet Scraper Service with AI Analysis
The Tweet Scraper Service is responsible for scraping tweets from a configured URL, storing them in a structured format, and analyzing them using Google Gemini 2.5 Pro AI for financial and political impact assessment.

Key features:
- Automated tweet scraping with configurable intervals
- **AI-powered analysis** using Google Gemini 2.5 Pro
- **Financial and political impact assessment** (0-100% scale)
- **Asset impact identification** (up to 5 crypto/stock assets per tweet)
- **Database storage** with SQLite/PostgreSQL support
- **Batch processing** of pending analyses
- **Prometheus metrics** for monitoring and cost tracking
- **Manual review flagging** for low-confidence or high-impact analyses
- Resilient operation with retry mechanisms for handling network issues
- Debug mode for development and troubleshooting

#### Setup and Configuration

1. **Environment Variables**: Configure the required environment variables in `.env` file:

```bash
# Tweet Scraper Configuration
TWEET_SCRAPER_URL=https://your-tweet-source.com
TWEET_SCRAPER_DEBUG=false
ENABLE_TWEET_ANALYSIS=true

# Google Gemini API Keys
GEMINI_API_KEY=your-production-gemini-api-key
GEMINI_FREE_API_KEY=your-free-gemini-api-key

# Analysis Configuration
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_FREE_MODEL=gemini-1.5-flash
ANALYSIS_BATCH_SIZE=10
ANALYSIS_MAX_RETRIES=3

# Database Configuration
DATABASE_URL=sqlite:///data/tweets/tweet_analysis.db
```

2. **Install Dependencies**:
```bash
make install
```

3. **Run the Service**:
```bash
make run_tweet_scraper
```

#### Testing

Run smoke tests with free Gemini model:
```bash
pytest tweet_scraper/tests/test_gemini_client.py -k "test_init"
```

Run all analysis tests:
```bash
pytest tweet_scraper/tests/test_analysis_service.py
pytest tweet_scraper/tests/test_gemini_client.py
pytest tweet_scraper/tests/test_database_repository.py
```

#### Monitoring and Metrics

The service exposes Prometheus metrics for monitoring:
- `tweet_analysis_requests_total` - Total analysis requests by status
- `tweet_analysis_duration_seconds` - Analysis processing time
- `tweet_analysis_cost_usd_total` - Total cost in USD
- `tweet_analysis_pending_total` - Number of pending analyses
- `tweet_analysis_manual_review_total` - Analyses requiring manual review

#### API Key Management

**Production**: Use `GEMINI_API_KEY` for production workloads with Gemini 2.5 Pro
**Testing**: Use `GEMINI_FREE_API_KEY` for development and testing with free tier models

The service automatically fails fast with clear error messages if API keys are not configured.

### BitBearBot
The BitBearBot is a Discord bot interface that monitors for new tweets and posts them in a Discord channel. It reads tweets from a structured data directory, orders them by date, and posts only the most recent tweets that haven't been posted before.

Key features:
- Automatic tweet monitoring and posting
- Resilient operation with exponential backoff retry mechanisms
- Configurable posting frequency and limits
- Persistent tracking of posted tweets

To run the BitBearBot:
1. Configure the required environment variables in `.env` file (see `.env.sample`)
2. Run `make bit_bear`

To run tests for the BitBearBot:
```
make test-bit-bear
```
