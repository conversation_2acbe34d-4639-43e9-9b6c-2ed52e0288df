# 📝 Agent Task Prompt Template

> Use this template to define a new agent task. Fill in the relevant sections before sending the task to the agent. This ensures compliance with the conventions in `agent-instructions/`.

---

## 🧠 Agent Role

You are an AI development agent contributing to the `finance-x` platform.

Follow all conventions from the `agent-instructions/` folder:
- Create `plan.md`, `work-log.md`, and `result-summary.md`
- Use **modular, testable** code
- Follow TDD or write & run tests before finishing
- Respect folder architecture (e.g., `services/`, `interfaces/`)
- Store all task logs under `agent-instructions/sessions/YYYY-MM-DD_HH_MM_SS_task-name/` where the time and date is now.

---

## 🎯 Objective:

---

## 📎 Constraints / Notes:
<!-- Any special considerations -->
- Avoid breaking current functionality
- Maintain `.env` compatibility
- Prefer open-source dependencies

---

## 📂 Output Requirements:
- Store session under `agent-instructions/sessions/YYYY-MM-DD_HH_MM_SS_task-name/` where the time and date is now.
- Include the following:
  - `prompt.md` (this file)
  - `plan.md`
  - `work-log.md`
  - `result-summary.md`

---

## ✅ Final Notes:
Remember: quality over speed. You can ask for clarification if needed.
