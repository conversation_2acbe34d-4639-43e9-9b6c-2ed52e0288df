# 🧠 agent-instructions/

This folder defines the **operational standards for AI agents** contributing to the `finance-x` project. It ensures consistency, traceability, and quality regardless of which agent platform is used (e.g., <PERSON><PERSON>, <PERSON>, Covenant).

## 📌 Folder Structure

```

agent-instructions/
├── prompts/              # Task templates (prompt instructions)
├── sessions/             # Logs of completed agent tasks
│   └── YYYY-MM-DD\_task-name/
│       ├── prompt.md
│       ├── plan.md
│       ├── work-log.md
│       └── result-summary.md
├── templates/            # Scaffolds for common task types
├── system-prompt.md      # Shared system prompt for all agents
└── README.md             # This file

```

## ✅ Agent Responsibility Checklist

1. Follow instructions in `system-prompt.md`
2. Each session must produce:
   - `prompt.md` – The original task definition
   - `plan.md` – Thoughtful step-by-step strategy before coding
   - `work-log.md` – Running output, insights, design notes
   - `result-summary.md` – Clear summary of what was done
3. Follow engineering best practices (TDD, modularity, clarity)
4. Respect the domain model and folder architecture
5. Seek feedback or generate self-review prompts before "task complete"

## 🤖 Agent Platforms Supported

- Junie
- Codex
- Augment code
- AutoGPT-style frameworks
- Manual prompt use in ChatGPT or similar

> All agents must respect the naming conventions and folder structures for interoperability.
