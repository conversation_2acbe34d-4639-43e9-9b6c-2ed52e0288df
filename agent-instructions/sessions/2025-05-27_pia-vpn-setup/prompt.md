# PIA VPN Setup Session Prompt

## User Request

The user requested to set up a VPN solution on their deployed Ubuntu server to resolve Binance API geographic restrictions. The finance-x application was failing with a "Service unavailable from a restricted location" error when trying to connect to Binance's API from the server location.

## Specific Requirements

1. **VPN Provider Change**: User specifically requested to change from generic OpenVPN to Private Internet Access (PIA) VPN, as they have an existing license
2. **Credential Management**: Allow PIA username and password to be configured via environment variables and GitHub Actions deployment
3. **Geographic Restriction Resolution**: Ensure the VPN solution allows Python application to make HTTPS requests to Binance API endpoints
4. **Auto-start Configuration**: Configure VPN to start automatically on server boot and reconnect if disconnected
5. **Production Deployment**: Ensure VPN solution doesn't interfere with existing deployment workflow
6. **Testing and Verification**: Provide testing capabilities to verify Binance API connection works after VPN setup

## Context

- **Server Environment**: Ubuntu server running on AWS EC2 (t2.micro)
- **Application**: Containerized finance-x application using Docker Compose
- **Deployment**: Pulumi-based infrastructure with GitHub Actions CI/CD
- **Error Location**: BinanceService initialization in portfolio_service.py
- **Existing Infrastructure**: EC2 instance with security groups, Docker, and application deployment

## Technical Constraints

- Must work with existing Pulumi deployment infrastructure
- Should integrate with GitHub Actions workflow
- Must not break existing application functionality
- Should provide monitoring and auto-reconnection capabilities
- Needs to be production-ready with proper error handling

## User Preferences

- **VPN Provider**: Private Internet Access (PIA) - user has existing license
- **Credential Storage**: Environment variables and GitHub Actions secrets
- **Automation Level**: Fully automated setup and startup
- **Monitoring**: Built-in monitoring and reconnection capabilities
- **Documentation**: Comprehensive setup and troubleshooting guides
