# PIA VPN Setup Implementation Plan

## Analysis Phase

### 1. Codebase Investigation
- **Examined deployment infrastructure**: Pulumi EC2 deployment configuration
- **Analyzed application structure**: BinanceService in portfolio_service.py
- **Reviewed existing configuration**: GitHub Actions workflow and environment setup
- **Identified error location**: Geographic restriction in Binance API calls

### 2. Requirements Analysis
- **VPN Provider**: Switch from generic OpenVPN to Private Internet Access (PIA)
- **Credential Management**: GitHub Actions secrets and environment variables
- **Integration**: Seamless integration with existing Pulumi deployment
- **Automation**: Fully automated setup, startup, and monitoring

## Implementation Plan

### Phase 1: Core VPN Setup Script
**File**: `scripts/setup-vpn.sh`
- Replace generic OpenVPN setup with PIA-specific configuration
- Add PIA credential validation from environment variables
- Implement automatic PIA server selection (Singapore → Japan → UK → Netherlands)
- Download and configure PIA OpenVPN files automatically
- Create optimized PIA configuration for crypto trading
- Set up systemd services for VPN and monitoring

### Phase 2: VPN Testing and Monitoring
**Files**: `scripts/test-vpn.sh`, `scripts/start-pia-vpn.sh`
- Update testing script for PIA-specific connectivity checks
- Create dedicated VPN startup script with connection monitoring
- Implement comprehensive Binance API connectivity testing
- Add detailed troubleshooting and status reporting

### Phase 3: GitHub Actions Integration
**File**: `.github/workflows/deploy.yml`
- Add PIA credential configuration from GitHub secrets
- Integrate VPN setup into existing deployment workflow
- Implement conditional VPN setup based on credential availability
- Add proper error handling and status reporting

### Phase 4: Pulumi Infrastructure Updates
**File**: `pulumi/infra/deploy_ec2.py`
- Add PIA credential injection into deployment process
- Update deployment sequence to include VPN setup
- Add automatic VPN service startup after credential configuration
- Export VPN setup and startup results for monitoring

### Phase 5: Environment Configuration
**Files**: `.env.sample`, configuration templates
- Add PIA credential placeholders to environment template
- Update documentation for credential management
- Ensure secure credential storage and permissions

### Phase 6: Documentation and Guides
**Files**: `docs/vpn-setup-guide.md`, `README-PIA-VPN.md`
- Create comprehensive PIA VPN setup documentation
- Provide troubleshooting guides and common solutions
- Document testing procedures and verification steps
- Include cost information and support resources

## Technical Implementation Details

### VPN Configuration Strategy
1. **Server Selection Priority**: Singapore (best for Binance) → Japan → UK → Netherlands
2. **Security Settings**: AES-128-CBC encryption, SHA1 authentication, DNS leak protection
3. **Reliability Features**: Auto-reconnect, kill switch, IPv6 disabled
4. **Monitoring**: Continuous connectivity testing and automatic restart

### Credential Management
1. **GitHub Secrets**: `PIA_USERNAME` and `PIA_PASSWORD` for CI/CD
2. **Environment Variables**: Local development and manual deployment
3. **Pulumi Config**: Encrypted storage in Pulumi configuration
4. **File Permissions**: Secure credential file storage with restricted access

### Service Architecture
1. **vpn-binance.service**: Main VPN connection service
2. **vpn-monitor.service**: Monitoring and auto-reconnect service
3. **Startup Scripts**: Automated service enablement and startup
4. **Health Checks**: Continuous Binance API connectivity testing

### Integration Points
1. **Pulumi Deployment**: VPN setup integrated into infrastructure deployment
2. **Docker Networking**: Ensure containers route through VPN tunnel
3. **Application Startup**: VPN services start before application containers
4. **Monitoring Integration**: VPN status exported for deployment monitoring

## Success Criteria

### Functional Requirements
- ✅ PIA VPN connects automatically during deployment
- ✅ Binance API accessible through VPN tunnel
- ✅ Application containers route traffic through VPN
- ✅ Auto-reconnection works on connection drops
- ✅ Comprehensive testing and verification tools

### Non-Functional Requirements
- ✅ Secure credential management through GitHub secrets
- ✅ Production-ready error handling and logging
- ✅ Comprehensive documentation and troubleshooting guides
- ✅ Minimal impact on existing deployment workflow
- ✅ Monitoring and alerting capabilities

### Verification Methods
1. **Automated Testing**: `scripts/test-vpn.sh` comprehensive connectivity test
2. **Manual Verification**: IP address change and Binance API access
3. **Service Monitoring**: systemd service status and log analysis
4. **Application Testing**: Finance-x application successful Binance API calls
