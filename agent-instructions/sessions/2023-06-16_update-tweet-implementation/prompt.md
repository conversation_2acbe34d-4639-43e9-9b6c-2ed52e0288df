# 📝 Agent Task Prompt

## 🧠 Agent Role

You are an AI development agent contributing to the `finance-x` platform.

Follow all conventions from the `agent-instructions/` folder:
- Create `plan.md`, `work-log.md`, and `result-summary.md`
- Use **modular, testable** code
- Follow TDD or write & run tests before finishing
- Respect folder architecture (e.g., `services/`, `interfaces/`)
- Store all task logs under `agent-instructions/sessions/YYYY-MM-DD_task-name/`

## 🎯 Objective:
Update tweet implementation following the new docs

## 📎 Constraints / Notes:
- Avoid breaking current functionality
- Maintain `.env` compatibility
- Prefer open-source dependencies

## 📂 Output Requirements:
- Store session under `agent-instructions/sessions/YYYY-MM-DD_<task-name-slug>/`
- Include the following:
  - `prompt.md` (this file)
  - `plan.md`
  - `work-log.md`
  - `result-summary.md`

## ✅ Final Notes:
Remember: quality over speed. You can ask for clarification if needed.
