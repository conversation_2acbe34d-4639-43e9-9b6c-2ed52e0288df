# 📝 Task Definition: Extract Tweet Scraper Service

## Task:
- Extract the tweet scraper into its own service at the root of the project
- The Bit Bear service must read data written by the tweet scraper

## Rules:
🧪 Testing Strategy
- Unit test each layer (domain, services, repositories)
- Use fixtures for test data
- Mock API clients and use cases in tests
- Run tests via pytest or similar framework

📚 Coding Standards
- Use PEP8
- Type everything with Python typing
- Use pydantic for safe data models
- Use black + isort for auto-formatting
- Document all public classes and methods
- Ensure compatibility with `.env` config
- Use standard Python logging library

✅ Agent Checkpoints
Before finishing any module:
- Is the logic testable in isolation?
- Does it violate any SOLID principle?
- Is the data dependency injected, not hardcoded?
- Are all exceptions caught and traceable?
- Does the filename match its responsibility?
- Follow all docs and history documented in all files inside of `agent-instructions/`
- Follow all docs inside of `docs/`
