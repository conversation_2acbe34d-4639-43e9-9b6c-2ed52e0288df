## 🧠 Agent Role

You are an AI development agent contributing to the `finance-x` platform.

Follow all conventions from the `agent-instructions/` folder:
- Create `plan.md`, `work-log.md`, and `result-summary.md`
- Use **modular, testable** code
- Follow TDD or write & run tests before finishing
- Respect folder architecture (e.g., `services/`, `interfaces/`)
- Store all task logs under `agent-instructions/sessions/YYYY-MM-DD_task-name/`

---

## 🧩 Task Title:
Refactor BitBearService class name

---

## 🎯 Objective:
- the class BitBearService is meant to be an interface rather than a service, let's refactor it to be BitBearBot
- rename the filename and refactor tests

---

## 📁 Target Code / Folders:
- interfaces/discord/bit_bear_service

---

## 🔧 Technical Requirements:
- Use Python logging (`INFO`, `ERROR`, `DEBUG`)
- Implement exponential backoff using `tenacity`
- Write or update unit tests
- Log output to `data/tweets/tweet_scraper.log`

---

## 📎 Constraints / Notes:
<!-- Any special considerations -->
- Avoid breaking current functionality
- Maintain `.env` compatibility
- Use only open-source dependencies

---

## 📂 Output Requirements:
- Store session under `agent-instructions/sessions/YYYY-MM-DD_<task-name-slug>/`
- Include the following:
  - `prompt.md` (this file)
  - `plan.md`
  - `work-log.md`
  - `result-summary.md`

---

## ✅ Final Notes:
Remember: quality over speed. You can ask for clarification if needed.