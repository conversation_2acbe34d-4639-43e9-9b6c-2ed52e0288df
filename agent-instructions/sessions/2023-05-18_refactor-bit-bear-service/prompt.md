## 🧠 Agent Role

You are an AI development agent contributing to the `finance-x` platform.

Follow all conventions defined in the `agent-instructions/` folder:
- Create `plan.md`, `work-log.md`, and `result-summary.md`
- Use **modular, testable** code
- Follow **TDD** or write & run tests before completing the task
- Respect the domain-based folder structure (e.g., `services/`, `interfaces/`)
- Store all session artifacts under:  
  `agent-instructions/sessions/YYYY-MM-DD_<task-name>/`

---

## 🧩 Task Title:
Refactor and Integrate BitBearService

---

## 🎯 Objective:

The `BitBearService` has been moved to a new location as part of an ongoing refactor to improve modularity and follow best practices. Supporting files like `Makefile`, `requirements.txt`, and `.env.sample` were added to begin standardizing the project layout.

Your objectives are:

- Add missing `__init__.py` files to ensure modules and classes are correctly discoverable.
- Create unit tests for `interfaces/bffs/discord-bff/app/services/bit_bear_service.py`
- Modify the root `Makefile` to include a `make test` command that runs all tests.
- Update the root `README.md`:
  - Add a general overview of the project.
  - Add a section describing the purpose and function of the `BitBearService`.

---

## 📁 Target Code / Folders:

- `interfaces/bffs/discord-bff/app/services/bit_bear_service.py`
- `interfaces/bffs/discord-bff/tests/`
- `Makefile`
- `README.md`

---

## 🔧 Technical Requirements:

- Tests should be compatible with `pytest`
- Use meaningful logging where applicable
- Ensure proper module imports with `__init__.py`
- Follow naming and structural conventions from `agent-instructions/`

---

## 📂 Output Requirements:

Create a task folder under:

```

agent-instructions/sessions/YYYY-MM-DD\_refactor-bitbear/

```

Include:

- `prompt.md` – the task definition (this file)
- `plan.md` – a step-by-step approach
- `work-log.md` – a live log of decisions and progress
- `result-summary.md` – final summary of the work

---

## ✅ Final Notes:

Prioritize clarity, test coverage, and code maintainability. Feel free to request clarification or propose improvements to the refactor as part of the task.
