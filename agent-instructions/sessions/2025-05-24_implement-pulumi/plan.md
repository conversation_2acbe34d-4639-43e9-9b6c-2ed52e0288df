# Implementation Plan for Pulumi Setup

## Objective
Implement Pulumi for the finance-X platform to deploy the bitbear service to either AWS EC2 or a local server via SSH.

## Steps

1. **Project Exploration**
   - Understand the existing project structure
   - Examine the bitbear service configuration in docker-compose.yml
   - Identify any dependencies or requirements for the deployment

2. **Pulumi Directory Structure Setup**
   - Create the pulumi directory with the specified structure:
     ```
     pulumi/
     ├── Pulumi.dev.yaml
     ├── Pulumi.yaml
     ├── requirements.txt
     ├── __main__.py
     └── infra/
         ├── deploy_ec2.py
         └── deploy_ssh.py
     ```

3. **Requirements File Creation**
   - Create a requirements.txt file with necessary Pulumi libraries for:
     - AWS EC2 deployment
     - SSH deployment to local server

4. **Main Deployment Logic Implementation**
   - Implement __main__.py with logic to select deployment based on stack
   - Handle configuration for both EC2 and SSH deployment options

5. **EC2 Deployment Implementation**
   - Create deploy_ec2.py for AWS EC2 deployment
   - Provision EC2 instance with Ubuntu 20.04
   - Configure security group with SSH and HTTP access
   - Implement Docker installation and service deployment

6. **SSH Deployment Implementation**
   - Create deploy_ssh.py for local server deployment
   - Implement SSH connection to local server (hostname: jet)
   - Configure git pull and docker-compose commands

7. **SSH Key Management**
   - Implement secure handling of SSH private key
   - Test key format compatibility (pluton.pem)

8. **Testing and Verification**
   - Test deployment to both targets
   - Verify idempotency of deployments
   - Ensure error handling is robust

9. **Documentation**
   - Document command-line steps for deployment
   - Provide usage instructions for the Pulumi setup