# 📝 Agent Task Prompt

## Task Name:
Improve Tweet Scraper Logging and Error Handling

## Summary:
Refactor the tweet scraper service to include structured logging (info, warning, error levels), and retry logic for failed HTTP responses using exponential backoff.

## Target:
`services/market-data-ingestion-service/app/scrapers/tweet_scraper_service.py`

## Notes:
- Ensure compatibility with `.env` config
- Use standard Python logging library
- Log to `data/tweets/tweet_scraper.log`
